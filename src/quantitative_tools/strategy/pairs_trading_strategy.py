"""
配对交易策略实现

基于统计套利的配对交易策略，通过价差回归获取稳定收益
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import logging
from sklearn.linear_model import LinearRegression
from statsmodels.tsa.stattools import adfuller

from .base import BaseStrategy, StrategyContext, StrategyState
from ..core.utils import validate_data

logger = logging.getLogger(__name__)


class PairsTradingStrategy(BaseStrategy):
    """
    配对交易策略实现类
    
    该策略基于统计套利原理：
    - 寻找历史上高度相关的股票对
    - 当价差偏离历史均值时进行反向操作
    - 等待价差回归获利
    """
    
    def __init__(self, context: StrategyContext):
        """
        初始化配对交易策略
        
        Args:
            context: 策略上下文
        """
        super().__init__(context)
        
        # 默认参数
        self.default_params = {
            'lookback_period': 252,     # 历史数据回看期
            'entry_threshold': 2.0,     # 开仓Z-score阈值
            'exit_threshold': 0.5,      # 平仓Z-score阈值
            'stop_loss_threshold': 3.5, # 止损Z-score阈值
            'position_size': 0.4,       # 仓位大小
            'min_correlation': 0.8,     # 最小相关性要求
            'cointegration_pvalue': 0.05, # 协整检验p值阈值
            'rebalance_freq': 30        # 重新计算参数频率(天)
        }
        
        # 合并用户参数
        self.params = {**self.default_params, **context.parameters}
        
        # 策略状态
        self.stock_a = context.universe[0] if len(context.universe) > 0 else None
        self.stock_b = context.universe[1] if len(context.universe) > 1 else None
        self.hedge_ratio = 0
        self.spread_mean = 0
        self.spread_std = 0
        self.position_a = 0
        self.position_b = 0
        self.last_rebalance = None
        
        logger.info(f"配对交易策略初始化完成: {self.stock_a} vs {self.stock_b}")
    
    def initialize(self) -> bool:
        """
        策略初始化
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 验证股票对
            if not self.stock_a or not self.stock_b:
                raise ValueError("需要指定两只股票进行配对交易")
            
            if self.stock_a == self.stock_b:
                raise ValueError("不能对同一只股票进行配对交易")
            
            # 验证参数
            if self.params['entry_threshold'] <= self.params['exit_threshold']:
                raise ValueError("开仓阈值必须大于平仓阈值")
            
            self.state = StrategyState.INITIALIZED
            logger.info("配对交易策略初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"配对交易策略初始化失败: {e}")
            self.state = StrategyState.ERROR
            return False
    
    def calculate_cointegration(self, price_a: pd.Series, price_b: pd.Series) -> Dict[str, float]:
        """
        计算协整关系
        
        Args:
            price_a: 股票A价格序列
            price_b: 股票B价格序列
            
        Returns:
            Dict[str, float]: 协整参数
        """
        # 对数变换
        log_a = np.log(price_a).dropna()
        log_b = np.log(price_b).dropna()
        
        # 确保数据长度一致
        min_length = min(len(log_a), len(log_b))
        log_a = log_a[-min_length:]
        log_b = log_b[-min_length:]
        
        # 线性回归求对冲比率
        model = LinearRegression()
        model.fit(log_b.values.reshape(-1, 1), log_a.values)
        hedge_ratio = model.coef_[0]
        intercept = model.intercept_
        
        # 计算价差
        spread = log_a - hedge_ratio * log_b - intercept
        
        # ADF检验
        try:
            adf_result = adfuller(spread.dropna())
            p_value = adf_result[1]
            is_cointegrated = p_value < self.params['cointegration_pvalue']
        except:
            p_value = 1.0
            is_cointegrated = False
        
        # 计算价差统计量
        spread_mean = spread.mean()
        spread_std = spread.std()
        
        # 计算相关性
        correlation = log_a.corr(log_b)
        
        return {
            'hedge_ratio': hedge_ratio,
            'intercept': intercept,
            'spread_mean': spread_mean,
            'spread_std': spread_std,
            'correlation': correlation,
            'p_value': p_value,
            'is_cointegrated': is_cointegrated
        }
    
    def calculate_z_score(self, price_a: float, price_b: float) -> float:
        """
        计算当前价差的Z-score
        
        Args:
            price_a: 股票A当前价格
            price_b: 股票B当前价格
            
        Returns:
            float: Z-score值
        """
        if self.spread_std == 0:
            return 0
        
        # 计算当前价差
        current_spread = np.log(price_a) - self.hedge_ratio * np.log(price_b)
        
        # 计算Z-score
        z_score = (current_spread - self.spread_mean) / self.spread_std
        
        return z_score
    
    def update_parameters(self, data: pd.DataFrame) -> bool:
        """
        更新策略参数
        
        Args:
            data: 价格数据，包含两只股票的价格
            
        Returns:
            bool: 更新是否成功
        """
        try:
            # 检查数据
            if self.stock_a not in data.columns or self.stock_b not in data.columns:
                logger.error(f"数据中缺少股票价格: {self.stock_a}, {self.stock_b}")
                return False
            
            # 获取足够的历史数据
            if len(data) < self.params['lookback_period']:
                logger.warning(f"历史数据不足: {len(data)} < {self.params['lookback_period']}")
                return False
            
            # 计算协整关系
            price_a = data[self.stock_a][-self.params['lookback_period']:]
            price_b = data[self.stock_b][-self.params['lookback_period']:]
            
            coint_result = self.calculate_cointegration(price_a, price_b)
            
            # 检查协整关系和相关性
            if not coint_result['is_cointegrated']:
                logger.warning(f"股票对不满足协整关系: p_value={coint_result['p_value']:.4f}")
                return False
            
            if coint_result['correlation'] < self.params['min_correlation']:
                logger.warning(f"相关性不足: {coint_result['correlation']:.4f} < {self.params['min_correlation']}")
                return False
            
            # 更新参数
            self.hedge_ratio = coint_result['hedge_ratio']
            self.spread_mean = coint_result['spread_mean']
            self.spread_std = coint_result['spread_std']
            
            logger.info(f"参数更新成功: hedge_ratio={self.hedge_ratio:.4f}, "
                       f"correlation={coint_result['correlation']:.4f}")
            
            return True
            
        except Exception as e:
            logger.error(f"参数更新失败: {e}")
            return False
    
    def generate_signals(self, data: pd.DataFrame) -> Dict[str, int]:
        """
        生成交易信号
        
        Args:
            data: 价格数据
            
        Returns:
            Dict[str, int]: 交易信号 (1: 买入, -1: 卖出, 0: 无信号)
        """
        if len(data) < self.params['lookback_period']:
            return {'signal_a': 0, 'signal_b': 0}
        
        # 获取当前价格
        current_price_a = data[self.stock_a].iloc[-1]
        current_price_b = data[self.stock_b].iloc[-1]
        
        # 计算Z-score
        z_score = self.calculate_z_score(current_price_a, current_price_b)
        
        signals = {'signal_a': 0, 'signal_b': 0, 'z_score': z_score}
        
        # 开仓信号
        if abs(z_score) > self.params['entry_threshold'] and self.position_a == 0:
            if z_score > 0:
                # 价差过大，卖出A买入B
                signals['signal_a'] = -1
                signals['signal_b'] = 1
            else:
                # 价差过小，买入A卖出B
                signals['signal_a'] = 1
                signals['signal_b'] = -1
        
        # 平仓信号
        elif abs(z_score) < self.params['exit_threshold'] and self.position_a != 0:
            # 平仓
            signals['signal_a'] = -np.sign(self.position_a)
            signals['signal_b'] = -np.sign(self.position_b)
        
        # 止损信号
        elif abs(z_score) > self.params['stop_loss_threshold'] and self.position_a != 0:
            # 强制平仓
            signals['signal_a'] = -np.sign(self.position_a)
            signals['signal_b'] = -np.sign(self.position_b)
        
        return signals
    
    def on_data(self, data: pd.DataFrame, current_time: pd.Timestamp) -> Dict[str, Any]:
        """
        数据更新时的处理逻辑
        
        Args:
            data: 最新的价格数据
            current_time: 当前时间
            
        Returns:
            Dict[str, Any]: 交易决策
        """
        try:
            # 检查是否需要重新计算参数
            if (self.last_rebalance is None or 
                (current_time - self.last_rebalance).days >= self.params['rebalance_freq']):
                
                if self.update_parameters(data):
                    self.last_rebalance = current_time
                else:
                    return {
                        'timestamp': current_time,
                        'action': 'hold',
                        'reason': '参数更新失败，暂停交易'
                    }
            
            # 生成信号
            signals = self.generate_signals(data)
            
            # 获取当前价格
            current_price_a = data[self.stock_a].iloc[-1]
            current_price_b = data[self.stock_b].iloc[-1]
            
            # 交易决策
            decision = {
                'timestamp': current_time,
                'z_score': signals['z_score'],
                'action': 'hold',
                'trades': [],
                'reason': ''
            }
            
            # 执行交易
            if signals['signal_a'] != 0 or signals['signal_b'] != 0:
                total_value = self.context.capital * self.params['position_size']
                
                # 股票A交易
                if signals['signal_a'] != 0:
                    if self.position_a == 0:  # 开仓
                        quantity_a = int(total_value / 2 / current_price_a) * signals['signal_a']
                    else:  # 平仓
                        quantity_a = -self.position_a
                    
                    decision['trades'].append({
                        'symbol': self.stock_a,
                        'action': 'buy' if quantity_a > 0 else 'sell',
                        'quantity': abs(quantity_a),
                        'price': current_price_a
                    })
                    self.position_a += quantity_a
                
                # 股票B交易
                if signals['signal_b'] != 0:
                    if self.position_b == 0:  # 开仓
                        quantity_b = int(total_value / 2 * self.hedge_ratio / current_price_b) * signals['signal_b']
                    else:  # 平仓
                        quantity_b = -self.position_b
                    
                    decision['trades'].append({
                        'symbol': self.stock_b,
                        'action': 'buy' if quantity_b > 0 else 'sell',
                        'quantity': abs(quantity_b),
                        'price': current_price_b
                    })
                    self.position_b += quantity_b
                
                # 设置交易原因
                if abs(signals['z_score']) > self.params['stop_loss_threshold']:
                    decision['reason'] = f'止损平仓，Z-score={signals["z_score"]:.2f}'
                elif abs(signals['z_score']) > self.params['entry_threshold']:
                    decision['reason'] = f'开仓交易，Z-score={signals["z_score"]:.2f}'
                else:
                    decision['reason'] = f'正常平仓，Z-score={signals["z_score"]:.2f}'
                
                decision['action'] = 'trade'
            
            return decision
            
        except Exception as e:
            logger.error(f"配对交易策略数据处理错误: {e}")
            return {
                'timestamp': current_time,
                'action': 'hold',
                'error': str(e)
            }
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """
        获取策略信息
        
        Returns:
            Dict[str, Any]: 策略信息
        """
        return {
            'name': '配对交易策略',
            'description': '基于统计套利的配对交易策略',
            'category': 'arbitrage',
            'difficulty': 'advanced',
            'stock_pair': [self.stock_a, self.stock_b],
            'parameters': self.params,
            'hedge_ratio': self.hedge_ratio,
            'spread_mean': self.spread_mean,
            'spread_std': self.spread_std,
            'position_a': self.position_a,
            'position_b': self.position_b,
            'state': self.state.value
        }


def create_pairs_trading_strategy(stock_a: str, stock_b: str, **kwargs) -> PairsTradingStrategy:
    """
    创建配对交易策略实例的便捷函数
    
    Args:
        stock_a: 股票A代码
        stock_b: 股票B代码
        **kwargs: 策略参数
        
    Returns:
        PairsTradingStrategy: 策略实例
    """
    context = StrategyContext(
        strategy_id=f"pairs_{stock_a}_{stock_b}",
        name=f"配对交易_{stock_a}_{stock_b}",
        description="基于统计套利的配对交易策略",
        universe=[stock_a, stock_b],
        parameters=kwargs
    )
    
    return PairsTradingStrategy(context)


# 策略配置模板
PAIRS_TRADING_CONFIG = {
    'name': '配对交易策略',
    'class': 'PairsTradingStrategy',
    'module': 'quantitative_tools.strategy.pairs_trading_strategy',
    'category': 'arbitrage',
    'difficulty': 'advanced',
    'description': '基于统计套利的配对交易策略，通过价差回归获取稳定收益',
    'parameters': {
        'lookback_period': {
            'type': 'int',
            'default': 252,
            'min': 100,
            'max': 500,
            'description': '历史数据回看期'
        },
        'entry_threshold': {
            'type': 'float',
            'default': 2.0,
            'min': 1.0,
            'max': 3.0,
            'description': '开仓Z-score阈值'
        },
        'exit_threshold': {
            'type': 'float',
            'default': 0.5,
            'min': 0.1,
            'max': 1.0,
            'description': '平仓Z-score阈值'
        },
        'position_size': {
            'type': 'float',
            'default': 0.4,
            'min': 0.1,
            'max': 0.8,
            'description': '仓位大小比例'
        }
    }
}
