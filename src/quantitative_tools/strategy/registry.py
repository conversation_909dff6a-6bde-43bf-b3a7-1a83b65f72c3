"""
策略注册中心

管理所有可用的策略类型和配置
"""

from typing import Dict, List, Type, Any
import importlib
import logging

from .base import BaseStrategy
from .macd_strategy import MACDStrategy, MACD_STRATEGY_CONFIG
from .pairs_trading_strategy import PairsTradingStrategy, PAIRS_TRADING_CONFIG

logger = logging.getLogger(__name__)


class StrategyRegistry:
    """策略注册中心"""
    
    def __init__(self):
        self._strategies: Dict[str, Dict[str, Any]] = {}
        self._register_builtin_strategies()
    
    def _register_builtin_strategies(self):
        """注册内置策略"""
        # 注册现有策略
        self.register_strategy('ma_cross', {
            'name': '双均线交叉策略',
            'class': 'MovingAverageCrossStrategy',
            'module': 'quantitative_tools.strategy.ma_cross',
            'category': 'trend',
            'difficulty': 'beginner',
            'description': '基于短期和长期移动平均线交叉的经典策略'
        })
        
        self.register_strategy('rsi_reversal', {
            'name': 'RSI反转策略',
            'class': 'RSIReversalStrategy', 
            'module': 'quantitative_tools.strategy.rsi_reversal',
            'category': 'reversal',
            'difficulty': 'intermediate',
            'description': '基于RSI指标的超买超卖反转策略'
        })
        
        self.register_strategy('bollinger_bands', {
            'name': '布林带策略',
            'class': 'BollingerBandsStrategy',
            'module': 'quantitative_tools.strategy.bollinger_bands',
            'category': 'volatility',
            'difficulty': 'intermediate',
            'description': '基于布林带的突破和回归策略'
        })
        
        self.register_strategy('momentum', {
            'name': '动量策略',
            'class': 'MomentumStrategy',
            'module': 'quantitative_tools.strategy.momentum',
            'category': 'momentum',
            'difficulty': 'advanced',
            'description': '基于价格动量的趋势跟踪策略'
        })
        
        self.register_strategy('mean_reversion', {
            'name': '均值回归策略',
            'class': 'MeanReversionStrategy',
            'module': 'quantitative_tools.strategy.mean_reversion',
            'category': 'reversal',
            'difficulty': 'beginner',
            'description': '基于价格均值回归的策略'
        })
        
        # 注册新策略
        self.register_strategy('macd_strategy', MACD_STRATEGY_CONFIG)
        self.register_strategy('pairs_trading', PAIRS_TRADING_CONFIG)
        
        # 注册其他新策略配置
        self.register_strategy('multi_factor', {
            'name': '多因子模型策略',
            'class': 'MultiFactorStrategy',
            'module': 'quantitative_tools.strategy.multi_factor',
            'category': 'factor',
            'difficulty': 'advanced',
            'description': '基于价值、成长、质量等多个因子的量化选股策略'
        })
        
        self.register_strategy('grid_trading', {
            'name': '网格交易策略',
            'class': 'GridTradingStrategy',
            'module': 'quantitative_tools.strategy.grid_trading',
            'category': 'grid',
            'difficulty': 'intermediate',
            'description': '在震荡市场中通过网格买卖获取价差收益'
        })
        
        self.register_strategy('svm_strategy', {
            'name': '支持向量机策略',
            'class': 'SVMStrategy',
            'module': 'quantitative_tools.strategy.svm_strategy',
            'category': 'ml',
            'difficulty': 'advanced',
            'description': '基于机器学习SVM算法进行价格方向预测'
        })
    
    def register_strategy(self, strategy_id: str, config: Dict[str, Any]):
        """
        注册策略
        
        Args:
            strategy_id: 策略ID
            config: 策略配置
        """
        self._strategies[strategy_id] = config
        logger.info(f"策略已注册: {strategy_id} - {config.get('name', 'Unknown')}")
    
    def get_strategy_config(self, strategy_id: str) -> Dict[str, Any]:
        """
        获取策略配置
        
        Args:
            strategy_id: 策略ID
            
        Returns:
            Dict[str, Any]: 策略配置
        """
        return self._strategies.get(strategy_id, {})
    
    def get_all_strategies(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有策略配置
        
        Returns:
            Dict[str, Dict[str, Any]]: 所有策略配置
        """
        return self._strategies.copy()
    
    def get_strategies_by_category(self, category: str) -> Dict[str, Dict[str, Any]]:
        """
        按分类获取策略
        
        Args:
            category: 策略分类
            
        Returns:
            Dict[str, Dict[str, Any]]: 指定分类的策略
        """
        return {
            strategy_id: config 
            for strategy_id, config in self._strategies.items()
            if config.get('category') == category
        }
    
    def get_strategies_by_difficulty(self, difficulty: str) -> Dict[str, Dict[str, Any]]:
        """
        按难度获取策略
        
        Args:
            difficulty: 策略难度 (beginner, intermediate, advanced)
            
        Returns:
            Dict[str, Dict[str, Any]]: 指定难度的策略
        """
        return {
            strategy_id: config 
            for strategy_id, config in self._strategies.items()
            if config.get('difficulty') == difficulty
        }
    
    def create_strategy(self, strategy_id: str, context) -> BaseStrategy:
        """
        创建策略实例
        
        Args:
            strategy_id: 策略ID
            context: 策略上下文
            
        Returns:
            BaseStrategy: 策略实例
        """
        config = self.get_strategy_config(strategy_id)
        if not config:
            raise ValueError(f"未找到策略: {strategy_id}")
        
        try:
            # 动态导入策略模块
            module_name = config['module']
            class_name = config['class']
            
            module = importlib.import_module(module_name)
            strategy_class = getattr(module, class_name)
            
            # 创建策略实例
            strategy = strategy_class(context)
            
            logger.info(f"策略实例创建成功: {strategy_id}")
            return strategy
            
        except Exception as e:
            logger.error(f"策略实例创建失败: {strategy_id}, 错误: {e}")
            raise
    
    def validate_strategy_config(self, config: Dict[str, Any]) -> bool:
        """
        验证策略配置
        
        Args:
            config: 策略配置
            
        Returns:
            bool: 配置是否有效
        """
        required_fields = ['name', 'class', 'module', 'category', 'difficulty']
        
        for field in required_fields:
            if field not in config:
                logger.error(f"策略配置缺少必需字段: {field}")
                return False
        
        # 验证分类
        valid_categories = [
            'trend', 'reversal', 'momentum', 'volatility', 
            'technical', 'arbitrage', 'factor', 'grid', 'ml'
        ]
        if config['category'] not in valid_categories:
            logger.error(f"无效的策略分类: {config['category']}")
            return False
        
        # 验证难度
        valid_difficulties = ['beginner', 'intermediate', 'advanced']
        if config['difficulty'] not in valid_difficulties:
            logger.error(f"无效的策略难度: {config['difficulty']}")
            return False
        
        return True
    
    def list_strategy_ids(self) -> List[str]:
        """
        获取所有策略ID列表
        
        Returns:
            List[str]: 策略ID列表
        """
        return list(self._strategies.keys())
    
    def search_strategies(self, keyword: str) -> Dict[str, Dict[str, Any]]:
        """
        搜索策略
        
        Args:
            keyword: 搜索关键词
            
        Returns:
            Dict[str, Dict[str, Any]]: 匹配的策略
        """
        keyword = keyword.lower()
        results = {}
        
        for strategy_id, config in self._strategies.items():
            # 在名称、描述中搜索
            if (keyword in config.get('name', '').lower() or
                keyword in config.get('description', '').lower() or
                keyword in strategy_id.lower()):
                results[strategy_id] = config
        
        return results


# 全局策略注册中心实例
strategy_registry = StrategyRegistry()


def get_strategy_registry() -> StrategyRegistry:
    """
    获取策略注册中心实例
    
    Returns:
        StrategyRegistry: 策略注册中心
    """
    return strategy_registry


def register_custom_strategy(strategy_id: str, config: Dict[str, Any]):
    """
    注册自定义策略
    
    Args:
        strategy_id: 策略ID
        config: 策略配置
    """
    if strategy_registry.validate_strategy_config(config):
        strategy_registry.register_strategy(strategy_id, config)
    else:
        raise ValueError(f"无效的策略配置: {strategy_id}")


def create_strategy_instance(strategy_id: str, context):
    """
    创建策略实例的便捷函数
    
    Args:
        strategy_id: 策略ID
        context: 策略上下文
        
    Returns:
        BaseStrategy: 策略实例
    """
    return strategy_registry.create_strategy(strategy_id, context)


# 策略分类映射
STRATEGY_CATEGORIES = {
    'trend': '趋势跟踪',
    'reversal': '反转策略', 
    'momentum': '动量策略',
    'volatility': '波动率策略',
    'technical': '技术指标',
    'arbitrage': '统计套利',
    'factor': '因子投资',
    'grid': '网格交易',
    'ml': '机器学习'
}

# 策略难度映射
STRATEGY_DIFFICULTIES = {
    'beginner': '初级',
    'intermediate': '中级',
    'advanced': '高级'
}


def get_strategy_categories() -> Dict[str, str]:
    """获取策略分类映射"""
    return STRATEGY_CATEGORIES.copy()


def get_strategy_difficulties() -> Dict[str, str]:
    """获取策略难度映射"""
    return STRATEGY_DIFFICULTIES.copy()


def get_strategy_summary() -> Dict[str, Any]:
    """
    获取策略概览信息
    
    Returns:
        Dict[str, Any]: 策略概览
    """
    all_strategies = strategy_registry.get_all_strategies()
    
    # 按分类统计
    category_stats = {}
    for category in STRATEGY_CATEGORIES:
        category_strategies = strategy_registry.get_strategies_by_category(category)
        category_stats[category] = {
            'name': STRATEGY_CATEGORIES[category],
            'count': len(category_strategies),
            'strategies': list(category_strategies.keys())
        }
    
    # 按难度统计
    difficulty_stats = {}
    for difficulty in STRATEGY_DIFFICULTIES:
        difficulty_strategies = strategy_registry.get_strategies_by_difficulty(difficulty)
        difficulty_stats[difficulty] = {
            'name': STRATEGY_DIFFICULTIES[difficulty],
            'count': len(difficulty_strategies),
            'strategies': list(difficulty_strategies.keys())
        }
    
    return {
        'total_strategies': len(all_strategies),
        'categories': category_stats,
        'difficulties': difficulty_stats,
        'strategy_list': list(all_strategies.keys())
    }
