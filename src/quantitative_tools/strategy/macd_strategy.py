"""
MACD策略实现

基于MACD指标的金叉死叉信号进行交易决策的经典趋势跟踪策略
"""

import pandas as pd
import numpy as np
import talib
from typing import Dict, List, Optional, Tuple
import logging

from .base import BaseStrategy, StrategyContext, StrategyState
from ..core.indicators import calculate_macd
from ..core.utils import validate_data

logger = logging.getLogger(__name__)


class MACDStrategy(BaseStrategy):
    """
    MACD策略实现类
    
    该策略基于MACD指标的金叉死叉信号进行交易决策：
    - 金叉（MACD上穿信号线）时买入
    - 死叉（MACD下穿信号线）时卖出
    - 支持零轴确认和背离信号
    """
    
    def __init__(self, context: StrategyContext):
        """
        初始化MACD策略
        
        Args:
            context: 策略上下文
        """
        super().__init__(context)
        
        # 默认参数
        self.default_params = {
            'fast_period': 12,      # 快速EMA周期
            'slow_period': 26,      # 慢速EMA周期
            'signal_period': 9,     # 信号线周期
            'min_periods': 35,      # 最小数据周期
            'position_size': 0.8,   # 仓位大小
            'stop_loss_pct': 0.05,  # 止损比例
            'use_zero_cross': True, # 是否使用零轴确认
            'use_divergence': False # 是否使用背离信号
        }
        
        # 合并用户参数
        self.params = {**self.default_params, **context.parameters}
        
        # 策略状态
        self.position = 0  # 当前仓位
        self.entry_price = 0  # 入场价格
        self.last_signal = None  # 上次信号
        
        logger.info(f"MACD策略初始化完成: {self.params}")
    
    def initialize(self) -> bool:
        """
        策略初始化
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 验证参数
            if self.params['fast_period'] >= self.params['slow_period']:
                raise ValueError("快速周期必须小于慢速周期")
            
            if self.params['signal_period'] <= 0:
                raise ValueError("信号线周期必须大于0")
            
            # 设置最小数据周期
            self.min_periods = max(
                self.params['slow_period'] + self.params['signal_period'],
                self.params['min_periods']
            )
            
            self.state = StrategyState.INITIALIZED
            logger.info("MACD策略初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"MACD策略初始化失败: {e}")
            self.state = StrategyState.ERROR
            return False
    
    def calculate_indicators(self, data: pd.DataFrame) -> Dict[str, pd.Series]:
        """
        计算MACD指标
        
        Args:
            data: 价格数据，包含close列
            
        Returns:
            Dict[str, pd.Series]: 指标数据
        """
        if not validate_data(data, ['close']):
            raise ValueError("数据格式不正确，需要包含close列")
        
        # 计算MACD指标
        macd, signal, histogram = calculate_macd(
            data['close'],
            fast_period=self.params['fast_period'],
            slow_period=self.params['slow_period'],
            signal_period=self.params['signal_period']
        )
        
        indicators = {
            'macd': macd,
            'signal': signal,
            'histogram': histogram
        }
        
        # 计算交叉信号
        indicators['macd_cross_above'] = (
            (macd > signal) & (macd.shift(1) <= signal.shift(1))
        )
        indicators['macd_cross_below'] = (
            (macd < signal) & (macd.shift(1) >= signal.shift(1))
        )
        
        # 零轴穿越信号
        if self.params['use_zero_cross']:
            indicators['zero_cross_above'] = (
                (macd > 0) & (macd.shift(1) <= 0)
            )
            indicators['zero_cross_below'] = (
                (macd < 0) & (macd.shift(1) >= 0)
            )
        
        return indicators
    
    def generate_signals(self, data: pd.DataFrame) -> pd.Series:
        """
        生成交易信号
        
        Args:
            data: 价格数据
            
        Returns:
            pd.Series: 交易信号序列 (1: 买入, -1: 卖出, 0: 无信号)
        """
        if len(data) < self.min_periods:
            return pd.Series(0, index=data.index)
        
        # 计算指标
        indicators = self.calculate_indicators(data)
        
        # 初始化信号序列
        signals = pd.Series(0, index=data.index)
        
        # 基本交叉信号
        buy_signals = indicators['macd_cross_above']
        sell_signals = indicators['macd_cross_below']
        
        # 零轴确认
        if self.params['use_zero_cross']:
            # 只在MACD在零轴上方时买入
            buy_signals = buy_signals & (indicators['macd'] > 0)
            # 只在MACD在零轴下方时卖出
            sell_signals = sell_signals & (indicators['macd'] < 0)
        
        # 设置信号
        signals[buy_signals] = 1
        signals[sell_signals] = -1
        
        return signals
    
    def on_data(self, data: pd.DataFrame, current_time: pd.Timestamp) -> Dict[str, Any]:
        """
        数据更新时的处理逻辑
        
        Args:
            data: 最新的价格数据
            current_time: 当前时间
            
        Returns:
            Dict[str, Any]: 交易决策
        """
        try:
            # 生成信号
            signals = self.generate_signals(data)
            current_signal = signals.iloc[-1] if len(signals) > 0 else 0
            
            # 获取当前价格
            current_price = data['close'].iloc[-1]
            
            # 交易决策
            decision = {
                'timestamp': current_time,
                'signal': current_signal,
                'price': current_price,
                'action': 'hold',
                'quantity': 0,
                'reason': ''
            }
            
            # 买入逻辑
            if current_signal == 1 and self.position == 0:
                quantity = int(self.params['position_size'] * self.context.capital / current_price)
                decision.update({
                    'action': 'buy',
                    'quantity': quantity,
                    'reason': 'MACD金叉买入信号'
                })
                self.position = quantity
                self.entry_price = current_price
                
            # 卖出逻辑
            elif current_signal == -1 and self.position > 0:
                decision.update({
                    'action': 'sell',
                    'quantity': self.position,
                    'reason': 'MACD死叉卖出信号'
                })
                self.position = 0
                self.entry_price = 0
            
            # 止损检查
            elif (self.position > 0 and 
                  current_price < self.entry_price * (1 - self.params['stop_loss_pct'])):
                decision.update({
                    'action': 'sell',
                    'quantity': self.position,
                    'reason': f'止损卖出，亏损{self.params["stop_loss_pct"]:.1%}'
                })
                self.position = 0
                self.entry_price = 0
            
            # 记录信号
            self.last_signal = current_signal
            
            # 更新指标数据
            if len(data) >= self.min_periods:
                indicators = self.calculate_indicators(data)
                decision['indicators'] = {
                    'macd': indicators['macd'].iloc[-1],
                    'signal': indicators['signal'].iloc[-1],
                    'histogram': indicators['histogram'].iloc[-1]
                }
            
            return decision
            
        except Exception as e:
            logger.error(f"MACD策略数据处理错误: {e}")
            return {
                'timestamp': current_time,
                'signal': 0,
                'action': 'hold',
                'quantity': 0,
                'error': str(e)
            }
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """
        获取策略信息
        
        Returns:
            Dict[str, Any]: 策略信息
        """
        return {
            'name': 'MACD策略',
            'description': '基于MACD指标的金叉死叉信号进行交易',
            'category': 'technical',
            'difficulty': 'intermediate',
            'parameters': self.params,
            'current_position': self.position,
            'entry_price': self.entry_price,
            'last_signal': self.last_signal,
            'state': self.state.value
        }
    
    def validate_parameters(self, params: Dict[str, Any]) -> bool:
        """
        验证策略参数
        
        Args:
            params: 参数字典
            
        Returns:
            bool: 参数是否有效
        """
        try:
            # 检查必需参数
            required_params = ['fast_period', 'slow_period', 'signal_period']
            for param in required_params:
                if param not in params:
                    logger.error(f"缺少必需参数: {param}")
                    return False
            
            # 检查参数范围
            if params['fast_period'] <= 0 or params['fast_period'] >= params['slow_period']:
                logger.error("快速周期必须大于0且小于慢速周期")
                return False
            
            if params['slow_period'] <= 0:
                logger.error("慢速周期必须大于0")
                return False
            
            if params['signal_period'] <= 0:
                logger.error("信号线周期必须大于0")
                return False
            
            if 'position_size' in params and (params['position_size'] <= 0 or params['position_size'] > 1):
                logger.error("仓位大小必须在0-1之间")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"参数验证错误: {e}")
            return False


def create_macd_strategy(symbol: str, **kwargs) -> MACDStrategy:
    """
    创建MACD策略实例的便捷函数
    
    Args:
        symbol: 交易标的
        **kwargs: 策略参数
        
    Returns:
        MACDStrategy: 策略实例
    """
    context = StrategyContext(
        strategy_id=f"macd_{symbol}",
        name=f"MACD策略_{symbol}",
        description="基于MACD指标的趋势跟踪策略",
        universe=[symbol],
        parameters=kwargs
    )
    
    return MACDStrategy(context)


# 策略配置模板
MACD_STRATEGY_CONFIG = {
    'name': 'MACD策略',
    'class': 'MACDStrategy',
    'module': 'quantitative_tools.strategy.macd_strategy',
    'category': 'technical',
    'difficulty': 'intermediate',
    'description': '基于MACD指标的金叉死叉信号进行交易的经典趋势跟踪策略',
    'parameters': {
        'fast_period': {
            'type': 'int',
            'default': 12,
            'min': 5,
            'max': 20,
            'description': '快速EMA周期'
        },
        'slow_period': {
            'type': 'int',
            'default': 26,
            'min': 15,
            'max': 50,
            'description': '慢速EMA周期'
        },
        'signal_period': {
            'type': 'int',
            'default': 9,
            'min': 5,
            'max': 15,
            'description': '信号线周期'
        },
        'position_size': {
            'type': 'float',
            'default': 0.8,
            'min': 0.1,
            'max': 1.0,
            'description': '仓位大小比例'
        },
        'stop_loss_pct': {
            'type': 'float',
            'default': 0.05,
            'min': 0.01,
            'max': 0.2,
            'description': '止损比例'
        }
    }
}
