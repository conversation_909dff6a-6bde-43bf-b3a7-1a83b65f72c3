"""
风险引擎

实现事件驱动的风险管理引擎，包括：
- 实时风险监控
- 仓位限制检查
- 止损止盈管理
- 风险预警和控制
"""

import logging
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from decimal import Decimal
import threading

from ..events.base import Event, EventType, EventHandler
from ..events.signal_events import StrategySignal, SignalType
from ..events.order_events import OrderEvent, OrderSubmittedEvent
from ..events.portfolio_events import PositionEvent, BalanceEvent, RiskEvent
from ..events.message_bus import MessageBus

logger = logging.getLogger(__name__)


@dataclass
class RiskLimits:
    """风险限制配置"""
    
    max_position_size: int = 1000  # 最大单个仓位大小
    max_portfolio_value: Decimal = Decimal('10000000')  # 最大组合价值
    max_daily_loss: Decimal = Decimal('50000')  # 最大日损失
    max_drawdown: Decimal = Decimal('0.1')  # 最大回撤比例
    max_leverage: Decimal = Decimal('3.0')  # 最大杠杆倍数
    max_concentration: Decimal = Decimal('0.2')  # 最大单品种集中度
    stop_loss_pct: Decimal = Decimal('0.02')  # 止损百分比
    take_profit_pct: Decimal = Decimal('0.05')  # 止盈百分比
    
    def __post_init__(self):
        """验证风险限制参数"""
        if self.max_position_size <= 0:
            raise ValueError("最大仓位大小必须大于0")
        
        if self.max_daily_loss <= 0:
            raise ValueError("最大日损失必须大于0")
        
        if not 0 < self.max_drawdown <= 1:
            raise ValueError("最大回撤比例必须在0-1之间")


class RiskRule(ABC):
    """
    风险规则抽象基类
    
    定义风险检查规则的标准接口。
    """
    
    @abstractmethod
    def check_risk(self, event: Event, context: Dict[str, Any]) -> Optional[RiskEvent]:
        """
        检查风险
        
        Args:
            event: 触发检查的事件
            context: 风险检查上下文
            
        Returns:
            Optional[RiskEvent]: 风险事件，如果没有风险则返回None
        """
        pass
    
    @abstractmethod
    def get_rule_name(self) -> str:
        """获取规则名称"""
        pass


class PositionSizeRule(RiskRule):
    """仓位大小风险规则"""
    
    def __init__(self, max_position_size: int):
        """
        初始化仓位大小规则
        
        Args:
            max_position_size: 最大仓位大小
        """
        self.max_position_size = max_position_size
    
    def check_risk(self, event: Event, context: Dict[str, Any]) -> Optional[RiskEvent]:
        """检查仓位大小风险"""
        if not isinstance(event, (StrategySignal, OrderSubmittedEvent)):
            return None
        
        symbol = event.symbol
        quantity = getattr(event, 'quantity', 0)
        
        if quantity > self.max_position_size:
            return RiskEvent(
                portfolio_id=context.get('portfolio_id', 'default'),
                risk_type='position_size',
                risk_level='high',
                current_value=Decimal(str(quantity)),
                threshold_value=Decimal(str(self.max_position_size)),
                risk_message=f"仓位大小 {quantity} 超过限制 {self.max_position_size}",
                action_required="减少订单数量",
                affected_symbols=[symbol],
                source="PositionSizeRule"
            )
        
        return None
    
    def get_rule_name(self) -> str:
        """获取规则名称"""
        return "PositionSizeRule"


class DrawdownRule(RiskRule):
    """回撤风险规则"""
    
    def __init__(self, max_drawdown: Decimal):
        """
        初始化回撤规则
        
        Args:
            max_drawdown: 最大回撤比例
        """
        self.max_drawdown = max_drawdown
    
    def check_risk(self, event: Event, context: Dict[str, Any]) -> Optional[RiskEvent]:
        """检查回撤风险"""
        current_value = context.get('portfolio_value', Decimal('0'))
        peak_value = context.get('peak_portfolio_value', current_value)
        
        if peak_value > 0:
            drawdown = (peak_value - current_value) / peak_value
            
            if drawdown > self.max_drawdown:
                risk_level = 'critical' if drawdown > self.max_drawdown * 2 else 'high'
                
                return RiskEvent(
                    portfolio_id=context.get('portfolio_id', 'default'),
                    risk_type='drawdown',
                    risk_level=risk_level,
                    current_value=drawdown,
                    threshold_value=self.max_drawdown,
                    risk_message=f"回撤 {drawdown:.2%} 超过限制 {self.max_drawdown:.2%}",
                    action_required="停止交易或减少仓位",
                    source="DrawdownRule"
                )
        
        return None
    
    def get_rule_name(self) -> str:
        """获取规则名称"""
        return "DrawdownRule"


class ConcentrationRule(RiskRule):
    """集中度风险规则"""
    
    def __init__(self, max_concentration: Decimal):
        """
        初始化集中度规则
        
        Args:
            max_concentration: 最大集中度比例
        """
        self.max_concentration = max_concentration
    
    def check_risk(self, event: Event, context: Dict[str, Any]) -> Optional[RiskEvent]:
        """检查集中度风险"""
        if not isinstance(event, (StrategySignal, OrderSubmittedEvent)):
            return None
        
        symbol = event.symbol
        positions = context.get('positions', {})
        portfolio_value = context.get('portfolio_value', Decimal('0'))
        
        if portfolio_value > 0 and symbol in positions:
            position_value = positions[symbol].get('market_value', Decimal('0'))
            concentration = position_value / portfolio_value
            
            if concentration > self.max_concentration:
                return RiskEvent(
                    portfolio_id=context.get('portfolio_id', 'default'),
                    risk_type='concentration',
                    risk_level='medium',
                    current_value=concentration,
                    threshold_value=self.max_concentration,
                    risk_message=f"{symbol} 集中度 {concentration:.2%} 超过限制 {self.max_concentration:.2%}",
                    action_required="分散投资或减少仓位",
                    affected_symbols=[symbol],
                    source="ConcentrationRule"
                )
        
        return None
    
    def get_rule_name(self) -> str:
        """获取规则名称"""
        return "ConcentrationRule"


class DailyLossRule(RiskRule):
    """日损失风险规则"""
    
    def __init__(self, max_daily_loss: Decimal):
        """
        初始化日损失规则
        
        Args:
            max_daily_loss: 最大日损失
        """
        self.max_daily_loss = max_daily_loss
    
    def check_risk(self, event: Event, context: Dict[str, Any]) -> Optional[RiskEvent]:
        """检查日损失风险"""
        daily_pnl = context.get('daily_pnl', Decimal('0'))
        
        if daily_pnl < -self.max_daily_loss:
            risk_level = 'critical' if daily_pnl < -self.max_daily_loss * 2 else 'high'
            
            return RiskEvent(
                portfolio_id=context.get('portfolio_id', 'default'),
                risk_type='daily_loss',
                risk_level=risk_level,
                current_value=abs(daily_pnl),
                threshold_value=self.max_daily_loss,
                risk_message=f"日损失 {daily_pnl} 超过限制 {self.max_daily_loss}",
                action_required="停止交易",
                source="DailyLossRule"
            )
        
        return None
    
    def get_rule_name(self) -> str:
        """获取规则名称"""
        return "DailyLossRule"


class RiskManager:
    """
    风险管理器
    
    管理风险规则和风险检查逻辑。
    """
    
    def __init__(self, risk_limits: RiskLimits):
        """
        初始化风险管理器
        
        Args:
            risk_limits: 风险限制配置
        """
        self.risk_limits = risk_limits
        self.rules: List[RiskRule] = []
        self.risk_events: List[RiskEvent] = []
        self._lock = threading.RLock()
        
        # 添加默认风险规则
        self._add_default_rules()
        
        logger.info("风险管理器初始化完成")
    
    def _add_default_rules(self) -> None:
        """添加默认风险规则"""
        self.rules.extend([
            PositionSizeRule(self.risk_limits.max_position_size),
            DrawdownRule(self.risk_limits.max_drawdown),
            ConcentrationRule(self.risk_limits.max_concentration),
            DailyLossRule(self.risk_limits.max_daily_loss)
        ])
    
    def add_rule(self, rule: RiskRule) -> None:
        """添加风险规则"""
        with self._lock:
            self.rules.append(rule)
            logger.info(f"风险规则已添加: {rule.get_rule_name()}")
    
    def remove_rule(self, rule_name: str) -> bool:
        """移除风险规则"""
        with self._lock:
            for i, rule in enumerate(self.rules):
                if rule.get_rule_name() == rule_name:
                    del self.rules[i]
                    logger.info(f"风险规则已移除: {rule_name}")
                    return True
            
            logger.warning(f"风险规则不存在: {rule_name}")
            return False
    
    def check_risks(self, event: Event, context: Dict[str, Any]) -> List[RiskEvent]:
        """检查所有风险规则"""
        risk_events = []
        
        with self._lock:
            for rule in self.rules:
                try:
                    risk_event = rule.check_risk(event, context)
                    if risk_event:
                        risk_events.append(risk_event)
                        self.risk_events.append(risk_event)
                        
                        logger.warning(f"风险检测: {rule.get_rule_name()} - {risk_event.risk_message}")
                        
                except Exception as e:
                    logger.error(f"风险规则 {rule.get_rule_name()} 检查失败: {e}")
        
        return risk_events
    
    def get_recent_risks(self, hours: int = 24) -> List[RiskEvent]:
        """获取最近的风险事件"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        with self._lock:
            return [
                risk for risk in self.risk_events
                if risk.timestamp >= cutoff_time
            ]
    
    def clear_risk_history(self) -> None:
        """清空风险历史"""
        with self._lock:
            self.risk_events.clear()
            logger.info("风险历史已清空")


class RiskEngine(EventHandler):
    """
    风险引擎
    
    事件驱动的风险管理引擎，实时监控和控制交易风险。
    """
    
    def __init__(
        self,
        message_bus: MessageBus,
        risk_limits: RiskLimits,
        portfolio_id: str = "default"
    ):
        """
        初始化风险引擎
        
        Args:
            message_bus: 消息总线
            risk_limits: 风险限制配置
            portfolio_id: 投资组合ID
        """
        self.message_bus = message_bus
        self.risk_manager = RiskManager(risk_limits)
        self.portfolio_id = portfolio_id
        self._portfolio_context: Dict[str, Any] = {
            'portfolio_id': portfolio_id,
            'portfolio_value': Decimal('0'),
            'peak_portfolio_value': Decimal('0'),
            'daily_pnl': Decimal('0'),
            'positions': {}
        }
        self._risk_callbacks: List[Callable[[RiskEvent], None]] = []
        
        # 订阅相关事件
        self.message_bus.subscribe(
            handler=self,
            event_types=[
                EventType.STRATEGY_SIGNAL,
                EventType.ORDER_SUBMITTED,
                EventType.POSITION_OPENED,
                EventType.POSITION_UPDATED,
                EventType.BALANCE_UPDATED
            ],
            handler_name="RiskEngine"
        )
        
        logger.info("风险引擎初始化完成")
    
    @property
    def handler_name(self) -> str:
        """处理器名称"""
        return "RiskEngine"
    
    def handle_event(self, event: Event) -> None:
        """处理事件"""
        try:
            # 更新投资组合上下文
            self._update_portfolio_context(event)
            
            # 检查风险
            risk_events = self.risk_manager.check_risks(event, self._portfolio_context)
            
            # 发布风险事件
            for risk_event in risk_events:
                self.message_bus.publish(risk_event)
                
                # 调用风险回调
                for callback in self._risk_callbacks:
                    try:
                        callback(risk_event)
                    except Exception as e:
                        logger.error(f"风险回调执行失败: {e}")
                
                # 如果是严重风险，可能需要采取紧急措施
                if risk_event.is_critical:
                    self._handle_critical_risk(risk_event)
            
        except Exception as e:
            logger.error(f"风险引擎处理事件失败: {e}")
    
    def can_handle(self, event_type: EventType) -> bool:
        """检查是否可以处理指定类型的事件"""
        return event_type in [
            EventType.STRATEGY_SIGNAL,
            EventType.ORDER_SUBMITTED,
            EventType.POSITION_OPENED,
            EventType.POSITION_UPDATED,
            EventType.BALANCE_UPDATED
        ]
    
    def _update_portfolio_context(self, event: Event) -> None:
        """更新投资组合上下文"""
        if isinstance(event, PositionEvent):
            self._portfolio_context['positions'][event.symbol] = {
                'quantity': event.quantity,
                'market_value': event.market_value,
                'unrealized_pnl': event.unrealized_pnl,
                'realized_pnl': event.realized_pnl
            }
        
        elif isinstance(event, BalanceEvent):
            self._portfolio_context['portfolio_value'] = event.total_balance
            
            # 更新峰值
            current_value = event.total_balance
            peak_value = self._portfolio_context['peak_portfolio_value']
            if current_value > peak_value:
                self._portfolio_context['peak_portfolio_value'] = current_value
    
    def _handle_critical_risk(self, risk_event: RiskEvent) -> None:
        """处理严重风险"""
        logger.critical(f"严重风险事件: {risk_event.risk_message}")
        
        # 这里可以实现紧急风险处理逻辑
        # 例如：强制平仓、停止交易等
        if risk_event.risk_type == 'daily_loss':
            logger.critical("触发日损失限制，建议停止所有交易")
        elif risk_event.risk_type == 'drawdown':
            logger.critical("触发回撤限制，建议减少仓位")
    
    def add_risk_callback(self, callback: Callable[[RiskEvent], None]) -> None:
        """添加风险回调函数"""
        self._risk_callbacks.append(callback)
    
    def add_risk_rule(self, rule: RiskRule) -> None:
        """添加风险规则"""
        self.risk_manager.add_rule(rule)
    
    def get_risk_stats(self) -> Dict[str, Any]:
        """获取风险统计"""
        recent_risks = self.risk_manager.get_recent_risks()
        
        return {
            'total_risk_events': len(self.risk_manager.risk_events),
            'recent_risk_events': len(recent_risks),
            'critical_risks': len([r for r in recent_risks if r.is_critical]),
            'portfolio_context': self._portfolio_context,
            'risk_limits': self.risk_manager.risk_limits
        }
