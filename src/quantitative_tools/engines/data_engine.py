"""
数据引擎

实现事件驱动的数据处理引擎，包括：
- 历史数据回放
- 实时数据模拟
- 多数据源支持
- 数据验证和清洗
"""

import logging
import pandas as pd
import numpy as np
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Iterator, Any, Callable
from dataclasses import dataclass, field
from decimal import Decimal
import asyncio
import threading
import time

from ..events.base import Event, EventHandler
from ..events.market_events import MarketDataEvent, BarEvent, TickEvent
from ..events.message_bus import MessageBus

logger = logging.getLogger(__name__)


@dataclass
class DataConfig:
    """数据配置"""
    
    symbols: List[str]
    start_date: datetime
    end_date: datetime
    timeframe: str = "1D"  # 数据频率
    data_source: str = "csv"  # 数据源类型
    data_path: str = ""  # 数据路径
    validate_data: bool = True  # 是否验证数据
    fill_missing: bool = True  # 是否填充缺失数据
    
    def __post_init__(self):
        """验证配置参数"""
        if self.start_date >= self.end_date:
            raise ValueError("开始日期必须早于结束日期")
        
        if not self.symbols:
            raise ValueError("必须指定至少一个交易品种")


class DataProvider(ABC):
    """
    数据提供者抽象基类
    
    定义数据提供者的标准接口，支持多种数据源。
    """
    
    @abstractmethod
    def get_historical_data(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        timeframe: str = "1D"
    ) -> pd.DataFrame:
        """
        获取历史数据
        
        Args:
            symbol: 交易品种
            start_date: 开始日期
            end_date: 结束日期
            timeframe: 时间周期
            
        Returns:
            pd.DataFrame: 历史数据
        """
        pass
    
    @abstractmethod
    def validate_data(self, data: pd.DataFrame) -> bool:
        """
        验证数据质量
        
        Args:
            data: 要验证的数据
            
        Returns:
            bool: 数据是否有效
        """
        pass
    
    @abstractmethod
    def clean_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        清洗数据
        
        Args:
            data: 原始数据
            
        Returns:
            pd.DataFrame: 清洗后的数据
        """
        pass


class HistoricalDataProvider(DataProvider):
    """
    历史数据提供者
    
    从本地文件或数据库加载历史数据。
    """
    
    def __init__(self, data_path: str = "data/"):
        """
        初始化历史数据提供者
        
        Args:
            data_path: 数据文件路径
        """
        self.data_path = data_path
        self._cache: Dict[str, pd.DataFrame] = {}
        
        logger.info(f"历史数据提供者初始化完成，数据路径: {data_path}")
    
    def get_historical_data(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        timeframe: str = "1D"
    ) -> pd.DataFrame:
        """获取历史数据"""
        cache_key = f"{symbol}_{timeframe}_{start_date}_{end_date}"
        
        if cache_key in self._cache:
            logger.debug(f"从缓存获取数据: {symbol}")
            return self._cache[cache_key]
        
        try:
            # 尝试从CSV文件加载数据
            file_path = f"{self.data_path}/{symbol}_{timeframe}.csv"
            data = pd.read_csv(file_path)
            
            # 处理日期列
            if 'date' in data.columns:
                data['date'] = pd.to_datetime(data['date'])
                data = data.set_index('date')
            elif 'datetime' in data.columns:
                data['datetime'] = pd.to_datetime(data['datetime'])
                data = data.set_index('datetime')
            
            # 筛选日期范围
            data = data[(data.index >= start_date) & (data.index <= end_date)]
            
            # 验证和清洗数据
            if self.validate_data(data):
                data = self.clean_data(data)
                self._cache[cache_key] = data
                logger.info(f"成功加载历史数据: {symbol}, 记录数: {len(data)}")
                return data
            else:
                logger.warning(f"数据验证失败: {symbol}")
                return self._generate_mock_data(symbol, start_date, end_date, timeframe)
                
        except Exception as e:
            logger.warning(f"加载历史数据失败: {symbol}, 错误: {e}")
            return self._generate_mock_data(symbol, start_date, end_date, timeframe)
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        """验证数据质量"""
        if data.empty:
            return False
        
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        if not all(col in data.columns for col in required_columns):
            logger.warning("数据缺少必要列")
            return False
        
        # 检查价格数据的合理性
        invalid_rows = (
            (data['high'] < data['low']) |
            (data['high'] < data['open']) |
            (data['high'] < data['close']) |
            (data['low'] > data['open']) |
            (data['low'] > data['close']) |
            (data['volume'] < 0)
        )
        
        if invalid_rows.any():
            logger.warning(f"发现 {invalid_rows.sum()} 行无效数据")
            return len(data) - invalid_rows.sum() > len(data) * 0.95  # 95%以上数据有效
        
        return True
    
    def clean_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """清洗数据"""
        # 移除无效行
        data = data[
            (data['high'] >= data['low']) &
            (data['high'] >= data['open']) &
            (data['high'] >= data['close']) &
            (data['low'] <= data['open']) &
            (data['low'] <= data['close']) &
            (data['volume'] >= 0)
        ]
        
        # 填充缺失值
        data = data.fillna(method='ffill').fillna(method='bfill')
        
        # 移除重复索引
        data = data[~data.index.duplicated(keep='first')]
        
        # 排序
        data = data.sort_index()
        
        logger.debug(f"数据清洗完成，剩余记录数: {len(data)}")
        return data
    
    def _generate_mock_data(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        timeframe: str
    ) -> pd.DataFrame:
        """生成模拟数据"""
        logger.info(f"生成模拟数据: {symbol}")
        
        # 生成日期序列
        if timeframe == "1D":
            dates = pd.date_range(start=start_date, end=end_date, freq='D')
            dates = dates[dates.weekday < 5]  # 只保留工作日
        elif timeframe == "1H":
            dates = pd.date_range(start=start_date, end=end_date, freq='H')
        else:
            dates = pd.date_range(start=start_date, end=end_date, freq='D')
        
        # 设置随机种子以确保可重复性
        np.random.seed(hash(symbol) % 2**32)
        
        # 生成价格数据
        base_price = 10 + np.random.random() * 90  # 10-100之间的基础价格
        returns = np.random.normal(0.0005, 0.02, len(dates))  # 日收益率
        
        prices = [base_price]
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        # 生成OHLCV数据
        data = []
        for i, (date, close) in enumerate(zip(dates, prices)):
            if i == 0:
                open_price = close
            else:
                open_price = prices[i-1]
            
            # 生成高低价
            volatility = abs(np.random.normal(0, 0.01))
            high = max(open_price, close) * (1 + volatility)
            low = min(open_price, close) * (1 - volatility)
            
            # 生成成交量
            volume = int(np.random.lognormal(13, 1))  # 对数正态分布
            
            data.append({
                'open': round(open_price, 2),
                'high': round(high, 2),
                'low': round(low, 2),
                'close': round(close, 2),
                'volume': volume
            })
        
        df = pd.DataFrame(data, index=dates)
        return df


class DataEngine(EventHandler):
    """
    数据引擎
    
    事件驱动的数据处理引擎，负责数据的获取、处理和分发。
    """
    
    def __init__(
        self,
        message_bus: MessageBus,
        data_provider: DataProvider,
        config: DataConfig
    ):
        """
        初始化数据引擎
        
        Args:
            message_bus: 消息总线
            data_provider: 数据提供者
            config: 数据配置
        """
        self.message_bus = message_bus
        self.data_provider = data_provider
        self.config = config
        self._running = False
        self._current_time: Optional[datetime] = None
        self._data_cache: Dict[str, pd.DataFrame] = {}
        self._replay_speed = 1.0  # 回放速度倍数
        self._event_callbacks: List[Callable[[Event], None]] = []
        
        # 加载历史数据
        self._load_historical_data()
        
        logger.info("数据引擎初始化完成")
    
    @property
    def handler_name(self) -> str:
        """处理器名称"""
        return "DataEngine"
    
    def handle_event(self, event: Event) -> None:
        """处理事件"""
        # 数据引擎主要发布事件，较少处理事件
        pass
    
    def can_handle(self, event_type) -> bool:
        """检查是否可以处理指定类型的事件"""
        return False  # 数据引擎主要发布事件
    
    def _load_historical_data(self) -> None:
        """加载历史数据"""
        logger.info("开始加载历史数据...")
        
        for symbol in self.config.symbols:
            try:
                data = self.data_provider.get_historical_data(
                    symbol=symbol,
                    start_date=self.config.start_date,
                    end_date=self.config.end_date,
                    timeframe=self.config.timeframe
                )
                
                if not data.empty:
                    self._data_cache[symbol] = data
                    logger.info(f"成功加载 {symbol} 数据，记录数: {len(data)}")
                else:
                    logger.warning(f"未能加载 {symbol} 数据")
                    
            except Exception as e:
                logger.error(f"加载 {symbol} 数据失败: {e}")
        
        logger.info(f"历史数据加载完成，共 {len(self._data_cache)} 个品种")
    
    def start_replay(self, speed: float = 1.0) -> None:
        """
        开始数据回放
        
        Args:
            speed: 回放速度倍数
        """
        if self._running:
            logger.warning("数据回放已在运行")
            return
        
        self._running = True
        self._replay_speed = speed
        self._current_time = self.config.start_date
        
        # 启动回放线程
        threading.Thread(target=self._replay_data, daemon=True).start()
        
        logger.info(f"数据回放已启动，速度: {speed}x")
    
    def stop_replay(self) -> None:
        """停止数据回放"""
        self._running = False
        logger.info("数据回放已停止")
    
    def _replay_data(self) -> None:
        """回放历史数据"""
        try:
            # 获取所有时间点
            all_timestamps = set()
            for data in self._data_cache.values():
                all_timestamps.update(data.index)
            
            timestamps = sorted(all_timestamps)
            timestamps = [ts for ts in timestamps if self.config.start_date <= ts <= self.config.end_date]
            
            logger.info(f"开始回放数据，时间点数量: {len(timestamps)}")
            
            for timestamp in timestamps:
                if not self._running:
                    break
                
                self._current_time = timestamp
                
                # 为每个品种生成事件
                for symbol in self.config.symbols:
                    if symbol in self._data_cache:
                        data = self._data_cache[symbol]
                        
                        if timestamp in data.index:
                            row = data.loc[timestamp]
                            
                            # 创建Bar事件
                            bar_event = BarEvent(
                                symbol=symbol,
                                timestamp=timestamp,
                                source="DataEngine",
                                open_price=Decimal(str(row['open'])),
                                high_price=Decimal(str(row['high'])),
                                low_price=Decimal(str(row['low'])),
                                close_price=Decimal(str(row['close'])),
                                volume=int(row['volume']),
                                timeframe=self.config.timeframe
                            )
                            
                            # 发布事件
                            self.message_bus.publish(bar_event)
                            
                            # 调用回调函数
                            for callback in self._event_callbacks:
                                try:
                                    callback(bar_event)
                                except Exception as e:
                                    logger.error(f"事件回调执行失败: {e}")
                
                # 控制回放速度
                if self._replay_speed > 0:
                    time.sleep(1.0 / self._replay_speed)
            
            logger.info("数据回放完成")
            
        except Exception as e:
            logger.error(f"数据回放异常: {e}")
        finally:
            self._running = False
    
    def add_event_callback(self, callback: Callable[[Event], None]) -> None:
        """添加事件回调函数"""
        self._event_callbacks.append(callback)
    
    def get_current_time(self) -> Optional[datetime]:
        """获取当前回放时间"""
        return self._current_time
    
    def get_current_data(self, symbol: str) -> Optional[pd.Series]:
        """获取指定品种的当前数据"""
        if symbol in self._data_cache and self._current_time:
            data = self._data_cache[symbol]
            if self._current_time in data.index:
                return data.loc[self._current_time]
        return None
    
    def get_historical_data(self, symbol: str, periods: int = 100) -> Optional[pd.DataFrame]:
        """获取指定品种的历史数据"""
        if symbol in self._data_cache and self._current_time:
            data = self._data_cache[symbol]
            end_idx = data.index.get_loc(self._current_time) if self._current_time in data.index else len(data)
            start_idx = max(0, end_idx - periods)
            return data.iloc[start_idx:end_idx + 1]
        return None
    
    def is_running(self) -> bool:
        """检查是否正在运行"""
        return self._running
