"""
策略引擎

实现事件驱动的策略执行引擎，包括：
- 策略管理和调度
- 信号生成和处理
- 策略状态管理
- 多策略协调
"""

import logging
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from decimal import Decimal
import threading

from ..events.base import Event, EventType, EventHandler
from ..events.market_events import MarketDataEvent, BarEvent, TickEvent
from ..events.signal_events import SignalEvent, StrategySignal, SignalType, SignalStrength
from ..events.order_events import OrderEvent
from ..events.message_bus import MessageBus

logger = logging.getLogger(__name__)


@dataclass
class StrategyConfig:
    """策略配置"""
    
    strategy_id: str
    strategy_name: str
    symbols: List[str]
    parameters: Dict[str, Any] = field(default_factory=dict)
    enabled: bool = True
    max_position_size: int = 1000
    risk_limit: float = 0.02  # 最大风险敞口
    
    def __post_init__(self):
        """验证配置参数"""
        if not self.strategy_id:
            raise ValueError("策略ID不能为空")
        
        if not self.symbols:
            raise ValueError("必须指定至少一个交易品种")


class BaseStrategy(ABC):
    """
    策略基类
    
    所有交易策略的抽象基类，定义策略的标准接口。
    """
    
    def __init__(self, config: StrategyConfig):
        """
        初始化策略
        
        Args:
            config: 策略配置
        """
        self.config = config
        self.is_active = False
        self._positions: Dict[str, int] = {}  # 当前仓位
        self._last_signals: Dict[str, StrategySignal] = {}  # 最近信号
        self._performance_stats = {
            'total_signals': 0,
            'profitable_signals': 0,
            'total_pnl': 0.0
        }
        
        logger.info(f"策略初始化: {config.strategy_name} ({config.strategy_id})")
    
    @abstractmethod
    def on_market_data(self, event: MarketDataEvent) -> Optional[StrategySignal]:
        """
        处理市场数据事件
        
        Args:
            event: 市场数据事件
            
        Returns:
            Optional[StrategySignal]: 生成的策略信号
        """
        pass
    
    @abstractmethod
    def on_bar_data(self, event: BarEvent) -> Optional[StrategySignal]:
        """
        处理K线数据事件
        
        Args:
            event: K线数据事件
            
        Returns:
            Optional[StrategySignal]: 生成的策略信号
        """
        pass
    
    def on_tick_data(self, event: TickEvent) -> Optional[StrategySignal]:
        """
        处理Tick数据事件（可选实现）
        
        Args:
            event: Tick数据事件
            
        Returns:
            Optional[StrategySignal]: 生成的策略信号
        """
        return None
    
    def on_order_event(self, event: OrderEvent) -> None:
        """
        处理订单事件（可选实现）
        
        Args:
            event: 订单事件
        """
        pass
    
    def start(self) -> None:
        """启动策略"""
        self.is_active = True
        logger.info(f"策略已启动: {self.config.strategy_name}")
    
    def stop(self) -> None:
        """停止策略"""
        self.is_active = False
        logger.info(f"策略已停止: {self.config.strategy_name}")
    
    def get_position(self, symbol: str) -> int:
        """获取指定品种的仓位"""
        return self._positions.get(symbol, 0)
    
    def update_position(self, symbol: str, quantity: int) -> None:
        """更新仓位"""
        self._positions[symbol] = quantity
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取策略绩效统计"""
        return self._performance_stats.copy()
    
    def _create_signal(
        self,
        symbol: str,
        signal_type: SignalType,
        strength: SignalStrength = SignalStrength.MODERATE,
        confidence: float = 0.5,
        target_price: Optional[Decimal] = None,
        stop_loss: Optional[Decimal] = None,
        take_profit: Optional[Decimal] = None,
        quantity: Optional[int] = None,
        reasoning: str = ""
    ) -> StrategySignal:
        """创建策略信号"""
        signal = StrategySignal(
            symbol=symbol,
            signal_type=signal_type,
            strength=strength,
            confidence=confidence,
            target_price=target_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            quantity=quantity,
            strategy_id=self.config.strategy_id,
            strategy_name=self.config.strategy_name,
            reasoning=reasoning,
            source=f"Strategy_{self.config.strategy_id}"
        )
        
        self._last_signals[symbol] = signal
        self._performance_stats['total_signals'] += 1
        
        return signal


class MovingAverageCrossoverStrategy(BaseStrategy):
    """
    移动平均线交叉策略
    
    基于短期和长期移动平均线的交叉信号进行交易。
    """
    
    def __init__(self, config: StrategyConfig):
        """初始化移动平均线交叉策略"""
        super().__init__(config)
        
        # 策略参数
        self.short_window = config.parameters.get('short_window', 10)
        self.long_window = config.parameters.get('long_window', 30)
        
        # 数据缓存
        self._price_history: Dict[str, List[float]] = {}
        
        logger.info(f"移动平均线交叉策略初始化完成，短周期: {self.short_window}, 长周期: {self.long_window}")
    
    def on_market_data(self, event: MarketDataEvent) -> Optional[StrategySignal]:
        """处理市场数据事件"""
        if isinstance(event, BarEvent):
            return self.on_bar_data(event)
        return None
    
    def on_bar_data(self, event: BarEvent) -> Optional[StrategySignal]:
        """处理K线数据"""
        if not self.is_active or event.symbol not in self.config.symbols:
            return None
        
        symbol = event.symbol
        price = float(event.close_price)
        
        # 更新价格历史
        if symbol not in self._price_history:
            self._price_history[symbol] = []
        
        self._price_history[symbol].append(price)
        
        # 保持固定长度的历史数据
        max_length = max(self.short_window, self.long_window) + 10
        if len(self._price_history[symbol]) > max_length:
            self._price_history[symbol] = self._price_history[symbol][-max_length:]
        
        # 检查是否有足够的数据
        if len(self._price_history[symbol]) < self.long_window:
            return None
        
        # 计算移动平均线
        prices = self._price_history[symbol]
        short_ma = sum(prices[-self.short_window:]) / self.short_window
        long_ma = sum(prices[-self.long_window:]) / self.long_window
        
        # 计算前一期的移动平均线
        if len(prices) > self.long_window:
            prev_short_ma = sum(prices[-self.short_window-1:-1]) / self.short_window
            prev_long_ma = sum(prices[-self.long_window-1:-1]) / self.long_window
        else:
            return None
        
        # 生成交易信号
        signal = None
        current_position = self.get_position(symbol)
        
        # 金叉：短期均线上穿长期均线，买入信号
        if prev_short_ma <= prev_long_ma and short_ma > long_ma and current_position <= 0:
            signal = self._create_signal(
                symbol=symbol,
                signal_type=SignalType.BUY,
                strength=SignalStrength.MODERATE,
                confidence=0.7,
                target_price=Decimal(str(price * 1.02)),  # 目标价格上涨2%
                stop_loss=Decimal(str(price * 0.98)),     # 止损价格下跌2%
                quantity=100,
                reasoning=f"短期均线({short_ma:.2f})上穿长期均线({long_ma:.2f})"
            )
            
            # 添加技术指标值
            signal.add_indicator('short_ma', short_ma)
            signal.add_indicator('long_ma', long_ma)
            signal.add_indicator('price', price)
        
        # 死叉：短期均线下穿长期均线，卖出信号
        elif prev_short_ma >= prev_long_ma and short_ma < long_ma and current_position >= 0:
            signal = self._create_signal(
                symbol=symbol,
                signal_type=SignalType.SELL,
                strength=SignalStrength.MODERATE,
                confidence=0.7,
                target_price=Decimal(str(price * 0.98)),  # 目标价格下跌2%
                stop_loss=Decimal(str(price * 1.02)),     # 止损价格上涨2%
                quantity=100,
                reasoning=f"短期均线({short_ma:.2f})下穿长期均线({long_ma:.2f})"
            )
            
            # 添加技术指标值
            signal.add_indicator('short_ma', short_ma)
            signal.add_indicator('long_ma', long_ma)
            signal.add_indicator('price', price)
        
        return signal


class StrategyManager:
    """
    策略管理器
    
    管理多个策略的生命周期和协调。
    """
    
    def __init__(self):
        """初始化策略管理器"""
        self._strategies: Dict[str, BaseStrategy] = {}
        self._strategy_stats: Dict[str, Dict[str, Any]] = {}
        self._lock = threading.RLock()
        
        logger.info("策略管理器初始化完成")
    
    def add_strategy(self, strategy: BaseStrategy) -> None:
        """添加策略"""
        with self._lock:
            strategy_id = strategy.config.strategy_id
            self._strategies[strategy_id] = strategy
            self._strategy_stats[strategy_id] = {
                'start_time': None,
                'signals_generated': 0,
                'last_signal_time': None
            }
            
            logger.info(f"策略已添加: {strategy.config.strategy_name} ({strategy_id})")
    
    def remove_strategy(self, strategy_id: str) -> bool:
        """移除策略"""
        with self._lock:
            if strategy_id in self._strategies:
                strategy = self._strategies[strategy_id]
                strategy.stop()
                del self._strategies[strategy_id]
                del self._strategy_stats[strategy_id]
                
                logger.info(f"策略已移除: {strategy_id}")
                return True
            
            logger.warning(f"策略不存在: {strategy_id}")
            return False
    
    def start_strategy(self, strategy_id: str) -> bool:
        """启动策略"""
        with self._lock:
            if strategy_id in self._strategies:
                strategy = self._strategies[strategy_id]
                strategy.start()
                self._strategy_stats[strategy_id]['start_time'] = datetime.now()
                
                logger.info(f"策略已启动: {strategy_id}")
                return True
            
            logger.warning(f"策略不存在: {strategy_id}")
            return False
    
    def stop_strategy(self, strategy_id: str) -> bool:
        """停止策略"""
        with self._lock:
            if strategy_id in self._strategies:
                strategy = self._strategies[strategy_id]
                strategy.stop()
                
                logger.info(f"策略已停止: {strategy_id}")
                return True
            
            logger.warning(f"策略不存在: {strategy_id}")
            return False
    
    def start_all_strategies(self) -> None:
        """启动所有策略"""
        with self._lock:
            for strategy_id in self._strategies:
                self.start_strategy(strategy_id)
    
    def stop_all_strategies(self) -> None:
        """停止所有策略"""
        with self._lock:
            for strategy_id in self._strategies:
                self.stop_strategy(strategy_id)
    
    def get_strategy(self, strategy_id: str) -> Optional[BaseStrategy]:
        """获取策略实例"""
        return self._strategies.get(strategy_id)
    
    def get_all_strategies(self) -> Dict[str, BaseStrategy]:
        """获取所有策略"""
        with self._lock:
            return self._strategies.copy()
    
    def get_strategy_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取策略统计信息"""
        with self._lock:
            stats = {}
            for strategy_id, strategy in self._strategies.items():
                stats[strategy_id] = {
                    **self._strategy_stats[strategy_id],
                    'is_active': strategy.is_active,
                    'performance': strategy.get_performance_stats(),
                    'config': strategy.config
                }
            return stats


class StrategyEngine(EventHandler):
    """
    策略引擎
    
    事件驱动的策略执行引擎，负责策略的调度和信号生成。
    """
    
    def __init__(self, message_bus: MessageBus):
        """
        初始化策略引擎
        
        Args:
            message_bus: 消息总线
        """
        self.message_bus = message_bus
        self.strategy_manager = StrategyManager()
        self._signal_callbacks: List[Callable[[StrategySignal], None]] = []
        
        # 订阅市场数据事件
        self.message_bus.subscribe(
            handler=self,
            event_types=[EventType.MARKET_DATA, EventType.BAR_DATA, EventType.TICK_DATA],
            handler_name="StrategyEngine"
        )
        
        logger.info("策略引擎初始化完成")
    
    @property
    def handler_name(self) -> str:
        """处理器名称"""
        return "StrategyEngine"
    
    def handle_event(self, event: Event) -> None:
        """处理事件"""
        try:
            if isinstance(event, (MarketDataEvent, BarEvent, TickEvent)):
                self._process_market_data(event)
        except Exception as e:
            logger.error(f"策略引擎处理事件失败: {e}")
    
    def can_handle(self, event_type: EventType) -> bool:
        """检查是否可以处理指定类型的事件"""
        return event_type in [EventType.MARKET_DATA, EventType.BAR_DATA, EventType.TICK_DATA]
    
    def _process_market_data(self, event: MarketDataEvent) -> None:
        """处理市场数据事件"""
        strategies = self.strategy_manager.get_all_strategies()
        
        for strategy_id, strategy in strategies.items():
            if not strategy.is_active:
                continue
            
            try:
                signal = None
                
                if isinstance(event, BarEvent):
                    signal = strategy.on_bar_data(event)
                elif isinstance(event, TickEvent):
                    signal = strategy.on_tick_data(event)
                else:
                    signal = strategy.on_market_data(event)
                
                if signal:
                    # 发布信号事件
                    self.message_bus.publish(signal)
                    
                    # 更新统计信息
                    stats = self.strategy_manager._strategy_stats[strategy_id]
                    stats['signals_generated'] += 1
                    stats['last_signal_time'] = datetime.now()
                    
                    # 调用信号回调
                    for callback in self._signal_callbacks:
                        try:
                            callback(signal)
                        except Exception as e:
                            logger.error(f"信号回调执行失败: {e}")
                    
                    logger.info(f"策略信号生成: {strategy.config.strategy_name} -> {signal.signal_type.value} {signal.symbol}")
                    
            except Exception as e:
                logger.error(f"策略 {strategy_id} 处理市场数据失败: {e}")
    
    def add_strategy(self, strategy: BaseStrategy) -> None:
        """添加策略"""
        self.strategy_manager.add_strategy(strategy)
    
    def add_signal_callback(self, callback: Callable[[StrategySignal], None]) -> None:
        """添加信号回调函数"""
        self._signal_callbacks.append(callback)
    
    def start_all_strategies(self) -> None:
        """启动所有策略"""
        self.strategy_manager.start_all_strategies()
    
    def stop_all_strategies(self) -> None:
        """停止所有策略"""
        self.strategy_manager.stop_all_strategies()
    
    def get_strategy_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取策略统计信息"""
        return self.strategy_manager.get_strategy_stats()
