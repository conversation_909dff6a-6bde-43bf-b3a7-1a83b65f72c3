"""
执行引擎

实现事件驱动的订单管理和交易执行引擎，包括：
- 订单管理系统
- 交易执行模拟
- 投资组合管理
- 滑点和手续费处理
"""

import logging
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from decimal import Decimal
import threading

from ..events.base import Event, EventType, EventHandler
from ..events.signal_events import StrategySignal, SignalType
from ..events.order_events import (
    OrderEvent, OrderSubmittedEvent, OrderFilledEvent, OrderCancelledEvent,
    OrderSide, OrderType, OrderStatus, TimeInForce
)
from ..events.portfolio_events import PositionEvent, BalanceEvent, PositionSide
from ..events.market_events import BarEvent
from ..events.message_bus import MessageBus

logger = logging.getLogger(__name__)


@dataclass
class Position:
    """仓位信息"""
    
    symbol: str
    side: PositionSide
    quantity: int
    avg_price: Decimal
    market_price: Decimal
    unrealized_pnl: Decimal = Decimal('0')
    realized_pnl: Decimal = Decimal('0')
    commission: Decimal = Decimal('0')
    
    @property
    def market_value(self) -> Decimal:
        """市值"""
        return self.market_price * self.quantity
    
    @property
    def cost_basis(self) -> Decimal:
        """成本基础"""
        return self.avg_price * self.quantity
    
    @property
    def total_pnl(self) -> Decimal:
        """总盈亏"""
        return self.unrealized_pnl + self.realized_pnl


@dataclass
class Order:
    """订单信息"""
    
    order_id: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: int
    price: Optional[Decimal] = None
    stop_price: Optional[Decimal] = None
    time_in_force: TimeInForce = TimeInForce.DAY
    status: OrderStatus = OrderStatus.PENDING
    filled_quantity: int = 0
    avg_fill_price: Decimal = Decimal('0')
    commission: Decimal = Decimal('0')
    create_time: datetime = field(default_factory=datetime.now)
    strategy_id: Optional[str] = None
    
    @property
    def remaining_quantity(self) -> int:
        """剩余数量"""
        return self.quantity - self.filled_quantity
    
    @property
    def is_filled(self) -> bool:
        """是否完全成交"""
        return self.filled_quantity >= self.quantity
    
    @property
    def is_partially_filled(self) -> bool:
        """是否部分成交"""
        return 0 < self.filled_quantity < self.quantity


class Portfolio:
    """
    投资组合管理
    
    管理账户余额、仓位和绩效统计。
    """
    
    def __init__(self, initial_capital: Decimal = Decimal('1000000')):
        """
        初始化投资组合
        
        Args:
            initial_capital: 初始资金
        """
        self.initial_capital = initial_capital
        self.cash = initial_capital
        self.positions: Dict[str, Position] = {}
        self.trades_history: List[Dict[str, Any]] = []
        self._lock = threading.RLock()
        
        logger.info(f"投资组合初始化完成，初始资金: {initial_capital}")
    
    def get_position(self, symbol: str) -> Optional[Position]:
        """获取仓位"""
        with self._lock:
            return self.positions.get(symbol)
    
    def update_position(
        self,
        symbol: str,
        side: OrderSide,
        quantity: int,
        price: Decimal,
        commission: Decimal = Decimal('0')
    ) -> Position:
        """更新仓位"""
        with self._lock:
            if symbol not in self.positions:
                # 新建仓位
                position_side = PositionSide.LONG if side == OrderSide.BUY else PositionSide.SHORT
                self.positions[symbol] = Position(
                    symbol=symbol,
                    side=position_side,
                    quantity=quantity,
                    avg_price=price,
                    market_price=price,
                    commission=commission
                )
            else:
                # 更新现有仓位
                position = self.positions[symbol]
                
                if side == OrderSide.BUY:
                    if position.side == PositionSide.LONG or position.quantity == 0:
                        # 增加多头仓位
                        total_cost = position.avg_price * position.quantity + price * quantity
                        total_quantity = position.quantity + quantity
                        position.avg_price = total_cost / total_quantity if total_quantity > 0 else price
                        position.quantity = total_quantity
                        position.side = PositionSide.LONG
                    else:
                        # 减少空头仓位
                        if quantity >= position.quantity:
                            # 平仓并开多仓
                            remaining = quantity - position.quantity
                            position.realized_pnl += (position.avg_price - price) * position.quantity
                            position.quantity = remaining
                            position.avg_price = price
                            position.side = PositionSide.LONG if remaining > 0 else PositionSide.FLAT
                        else:
                            # 部分平仓
                            position.realized_pnl += (position.avg_price - price) * quantity
                            position.quantity -= quantity
                
                elif side == OrderSide.SELL:
                    if position.side == PositionSide.SHORT or position.quantity == 0:
                        # 增加空头仓位
                        total_cost = position.avg_price * position.quantity + price * quantity
                        total_quantity = position.quantity + quantity
                        position.avg_price = total_cost / total_quantity if total_quantity > 0 else price
                        position.quantity = total_quantity
                        position.side = PositionSide.SHORT
                    else:
                        # 减少多头仓位
                        if quantity >= position.quantity:
                            # 平仓并开空仓
                            remaining = quantity - position.quantity
                            position.realized_pnl += (price - position.avg_price) * position.quantity
                            position.quantity = remaining
                            position.avg_price = price
                            position.side = PositionSide.SHORT if remaining > 0 else PositionSide.FLAT
                        else:
                            # 部分平仓
                            position.realized_pnl += (price - position.avg_price) * quantity
                            position.quantity -= quantity
                
                position.commission += commission
                
                # 如果仓位为0，设置为空仓
                if position.quantity == 0:
                    position.side = PositionSide.FLAT
            
            # 更新现金
            if side == OrderSide.BUY:
                self.cash -= price * quantity + commission
            else:
                self.cash += price * quantity - commission
            
            # 记录交易
            self.trades_history.append({
                'timestamp': datetime.now(),
                'symbol': symbol,
                'side': side.value,
                'quantity': quantity,
                'price': float(price),
                'commission': float(commission)
            })
            
            return self.positions[symbol]
    
    def update_market_prices(self, prices: Dict[str, Decimal]) -> None:
        """更新市场价格"""
        with self._lock:
            for symbol, price in prices.items():
                if symbol in self.positions:
                    position = self.positions[symbol]
                    position.market_price = price
                    
                    # 计算未实现盈亏
                    if position.side == PositionSide.LONG:
                        position.unrealized_pnl = (price - position.avg_price) * position.quantity
                    elif position.side == PositionSide.SHORT:
                        position.unrealized_pnl = (position.avg_price - price) * position.quantity
                    else:
                        position.unrealized_pnl = Decimal('0')
    
    def get_total_value(self) -> Decimal:
        """获取总资产价值"""
        with self._lock:
            total_value = self.cash
            for position in self.positions.values():
                total_value += position.market_value
            return total_value
    
    def get_total_pnl(self) -> Decimal:
        """获取总盈亏"""
        with self._lock:
            total_pnl = Decimal('0')
            for position in self.positions.values():
                total_pnl += position.total_pnl
            return total_pnl
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取绩效统计"""
        with self._lock:
            total_value = self.get_total_value()
            total_pnl = self.get_total_pnl()
            total_return = (total_value - self.initial_capital) / self.initial_capital
            
            return {
                'initial_capital': float(self.initial_capital),
                'current_cash': float(self.cash),
                'total_value': float(total_value),
                'total_pnl': float(total_pnl),
                'total_return': float(total_return),
                'total_trades': len(self.trades_history),
                'positions_count': len([p for p in self.positions.values() if p.quantity > 0])
            }


class OrderManager:
    """
    订单管理器
    
    管理订单的生命周期和状态。
    """
    
    def __init__(self):
        """初始化订单管理器"""
        self.orders: Dict[str, Order] = {}
        self.active_orders: Dict[str, Order] = {}
        self._lock = threading.RLock()
        
        logger.info("订单管理器初始化完成")
    
    def create_order(
        self,
        symbol: str,
        side: OrderSide,
        order_type: OrderType,
        quantity: int,
        price: Optional[Decimal] = None,
        stop_price: Optional[Decimal] = None,
        time_in_force: TimeInForce = TimeInForce.DAY,
        strategy_id: Optional[str] = None
    ) -> Order:
        """创建订单"""
        with self._lock:
            order_id = str(uuid.uuid4())
            
            order = Order(
                order_id=order_id,
                symbol=symbol,
                side=side,
                order_type=order_type,
                quantity=quantity,
                price=price,
                stop_price=stop_price,
                time_in_force=time_in_force,
                strategy_id=strategy_id
            )
            
            self.orders[order_id] = order
            self.active_orders[order_id] = order
            
            logger.info(f"订单已创建: {order_id} {side.value} {quantity} {symbol}")
            return order
    
    def update_order_status(self, order_id: str, status: OrderStatus) -> bool:
        """更新订单状态"""
        with self._lock:
            if order_id in self.orders:
                self.orders[order_id].status = status
                
                # 如果订单完成，从活跃订单中移除
                if status in [OrderStatus.FILLED, OrderStatus.CANCELLED, OrderStatus.REJECTED]:
                    self.active_orders.pop(order_id, None)
                
                logger.debug(f"订单状态更新: {order_id} -> {status.value}")
                return True
            
            return False
    
    def fill_order(
        self,
        order_id: str,
        fill_quantity: int,
        fill_price: Decimal,
        commission: Decimal = Decimal('0')
    ) -> bool:
        """订单成交"""
        with self._lock:
            if order_id not in self.orders:
                return False
            
            order = self.orders[order_id]
            
            # 更新成交信息
            total_filled = order.filled_quantity + fill_quantity
            if total_filled > order.quantity:
                fill_quantity = order.quantity - order.filled_quantity
                total_filled = order.quantity
            
            # 计算平均成交价格
            if order.filled_quantity == 0:
                order.avg_fill_price = fill_price
            else:
                total_value = order.avg_fill_price * order.filled_quantity + fill_price * fill_quantity
                order.avg_fill_price = total_value / total_filled
            
            order.filled_quantity = total_filled
            order.commission += commission
            
            # 更新订单状态
            if order.is_filled:
                order.status = OrderStatus.FILLED
                self.active_orders.pop(order_id, None)
            else:
                order.status = OrderStatus.PARTIALLY_FILLED
            
            logger.info(f"订单成交: {order_id} {fill_quantity}@{fill_price}")
            return True
    
    def cancel_order(self, order_id: str, reason: str = "") -> bool:
        """取消订单"""
        with self._lock:
            if order_id in self.active_orders:
                order = self.active_orders[order_id]
                order.status = OrderStatus.CANCELLED
                del self.active_orders[order_id]
                
                logger.info(f"订单已取消: {order_id} {reason}")
                return True
            
            return False
    
    def get_order(self, order_id: str) -> Optional[Order]:
        """获取订单"""
        return self.orders.get(order_id)
    
    def get_active_orders(self, symbol: Optional[str] = None) -> List[Order]:
        """获取活跃订单"""
        with self._lock:
            orders = list(self.active_orders.values())
            if symbol:
                orders = [order for order in orders if order.symbol == symbol]
            return orders


class ExecutionEngine(EventHandler):
    """
    执行引擎
    
    事件驱动的订单管理和交易执行引擎。
    """
    
    def __init__(
        self,
        message_bus: MessageBus,
        portfolio: Portfolio,
        commission_rate: float = 0.0003,
        slippage_rate: float = 0.001
    ):
        """
        初始化执行引擎
        
        Args:
            message_bus: 消息总线
            portfolio: 投资组合
            commission_rate: 手续费率
            slippage_rate: 滑点率
        """
        self.message_bus = message_bus
        self.portfolio = portfolio
        self.order_manager = OrderManager()
        self.commission_rate = commission_rate
        self.slippage_rate = slippage_rate
        self._current_prices: Dict[str, Decimal] = {}
        self._fill_callbacks: List[Callable[[OrderFilledEvent], None]] = []
        
        # 订阅策略信号和市场数据事件
        self.message_bus.subscribe(
            handler=self,
            event_types=[EventType.STRATEGY_SIGNAL, EventType.BAR_DATA],
            handler_name="ExecutionEngine"
        )
        
        logger.info("执行引擎初始化完成")
    
    @property
    def handler_name(self) -> str:
        """处理器名称"""
        return "ExecutionEngine"
    
    def handle_event(self, event: Event) -> None:
        """处理事件"""
        try:
            if isinstance(event, StrategySignal):
                self._process_strategy_signal(event)
            elif isinstance(event, BarEvent):
                self._update_market_price(event)
        except Exception as e:
            logger.error(f"执行引擎处理事件失败: {e}")
    
    def can_handle(self, event_type: EventType) -> bool:
        """检查是否可以处理指定类型的事件"""
        return event_type in [EventType.STRATEGY_SIGNAL, EventType.BAR_DATA]
    
    def _process_strategy_signal(self, signal: StrategySignal) -> None:
        """处理策略信号"""
        try:
            # 将信号转换为订单
            if signal.signal_type in [SignalType.BUY, SignalType.SELL]:
                order = self._create_order_from_signal(signal)
                if order:
                    # 提交订单
                    self._submit_order(order)
                    
                    # 模拟订单执行
                    self._simulate_order_execution(order)
            
        except Exception as e:
            logger.error(f"处理策略信号失败: {e}")
    
    def _create_order_from_signal(self, signal: StrategySignal) -> Optional[Order]:
        """从策略信号创建订单"""
        side = OrderSide.BUY if signal.signal_type == SignalType.BUY else OrderSide.SELL
        
        # 确定订单类型和价格
        if signal.target_price:
            order_type = OrderType.LIMIT
            price = signal.target_price
        else:
            order_type = OrderType.MARKET
            price = self._current_prices.get(signal.symbol)
        
        if not price:
            logger.warning(f"无法获取 {signal.symbol} 的当前价格")
            return None
        
        quantity = signal.quantity or 100  # 默认数量
        
        order = self.order_manager.create_order(
            symbol=signal.symbol,
            side=side,
            order_type=order_type,
            quantity=quantity,
            price=price,
            strategy_id=signal.strategy_id
        )
        
        return order
    
    def _submit_order(self, order: Order) -> None:
        """提交订单"""
        # 发布订单提交事件
        submit_event = OrderSubmittedEvent(
            order_id=order.order_id,
            symbol=order.symbol,
            side=order.side,
            order_type=order.order_type,
            quantity=order.quantity,
            price=order.price,
            time_in_force=order.time_in_force,
            status=OrderStatus.SUBMITTED,
            strategy_id=order.strategy_id,
            source="ExecutionEngine"
        )
        
        self.order_manager.update_order_status(order.order_id, OrderStatus.SUBMITTED)
        self.message_bus.publish(submit_event)
        
        logger.info(f"订单已提交: {order.order_id}")
    
    def _simulate_order_execution(self, order: Order) -> None:
        """模拟订单执行"""
        current_price = self._current_prices.get(order.symbol)
        if not current_price:
            logger.warning(f"无法执行订单，缺少 {order.symbol} 的当前价格")
            return
        
        # 计算滑点
        if order.side == OrderSide.BUY:
            fill_price = current_price * (1 + Decimal(str(self.slippage_rate)))
        else:
            fill_price = current_price * (1 - Decimal(str(self.slippage_rate)))
        
        # 计算手续费
        commission = fill_price * order.quantity * Decimal(str(self.commission_rate))
        
        # 执行订单
        self.order_manager.fill_order(
            order_id=order.order_id,
            fill_quantity=order.quantity,
            fill_price=fill_price,
            commission=commission
        )
        
        # 更新投资组合
        self.portfolio.update_position(
            symbol=order.symbol,
            side=order.side,
            quantity=order.quantity,
            price=fill_price,
            commission=commission
        )
        
        # 发布订单成交事件
        fill_event = OrderFilledEvent(
            order_id=order.order_id,
            symbol=order.symbol,
            side=order.side,
            order_type=order.order_type,
            quantity=order.quantity,
            price=order.price,
            time_in_force=order.time_in_force,
            status=OrderStatus.FILLED,
            strategy_id=order.strategy_id,
            fill_price=fill_price,
            fill_quantity=order.quantity,
            fill_time=datetime.now(),
            commission=commission,
            source="ExecutionEngine"
        )
        
        self.message_bus.publish(fill_event)
        
        # 调用成交回调
        for callback in self._fill_callbacks:
            try:
                callback(fill_event)
            except Exception as e:
                logger.error(f"成交回调执行失败: {e}")
        
        logger.info(f"订单已成交: {order.order_id} {order.quantity}@{fill_price}")
    
    def _update_market_price(self, event: BarEvent) -> None:
        """更新市场价格"""
        self._current_prices[event.symbol] = event.close_price
        
        # 更新投资组合的市场价格
        self.portfolio.update_market_prices({event.symbol: event.close_price})
    
    def add_fill_callback(self, callback: Callable[[OrderFilledEvent], None]) -> None:
        """添加成交回调函数"""
        self._fill_callbacks.append(callback)
    
    def get_portfolio_stats(self) -> Dict[str, Any]:
        """获取投资组合统计"""
        return self.portfolio.get_performance_stats()
