"""
事件驱动引擎模块

提供完整的事件驱动交易系统引擎，包括：
- 数据引擎：历史数据回放和实时数据处理
- 策略引擎：策略执行和信号生成
- 执行引擎：订单管理和交易执行
- 风险引擎：风险控制和监控
- 回测引擎：事件驱动回测框架
"""

from .data_engine import DataEngine, DataProvider, HistoricalDataProvider
from .strategy_engine import StrategyEngine, StrategyManager
from .execution_engine import ExecutionEngine, OrderManager, Portfolio
from .risk_engine import RiskEngine, RiskManager
from .backtest_engine import EventDrivenBacktester, BacktestConfig, BacktestResult

__all__ = [
    # 数据引擎
    'DataEngine',
    'DataProvider',
    'HistoricalDataProvider',
    
    # 策略引擎
    'StrategyEngine',
    'StrategyManager',
    
    # 执行引擎
    'ExecutionEngine',
    'OrderManager',
    'Portfolio',
    
    # 风险引擎
    'RiskEngine',
    'RiskManager',
    
    # 回测引擎
    'EventDrivenBacktester',
    'BacktestConfig',
    'BacktestResult',
]
