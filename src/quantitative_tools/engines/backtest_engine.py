"""
事件驱动回测引擎

实现完整的事件驱动回测框架，包括：
- 事件驱动回测流程
- 多引擎协调管理
- 回测结果分析
- 性能统计和报告
"""

import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from decimal import Decimal
import pandas as pd
import numpy as np

from ..events.base import Event, EventType
from ..events.message_bus import MessageBus
from ..events.event_queue import EventQueue, EventLoop
from .data_engine import DataEngine, DataProvider, DataConfig
from .strategy_engine import StrategyEngine, BaseStrategy
from .execution_engine import ExecutionEngine, Portfolio
from .risk_engine import RiskEngine, RiskLimits

logger = logging.getLogger(__name__)


@dataclass
class BacktestConfig:
    """回测配置"""
    
    start_date: datetime
    end_date: datetime
    initial_capital: Decimal = Decimal('1000000')
    symbols: List[str] = field(default_factory=list)
    timeframe: str = "1D"
    commission_rate: float = 0.0003
    slippage_rate: float = 0.001
    data_source: str = "csv"
    data_path: str = "data/"
    benchmark: str = "000300.SH"
    
    def __post_init__(self):
        """验证配置参数"""
        if self.start_date >= self.end_date:
            raise ValueError("开始日期必须早于结束日期")
        
        if self.initial_capital <= 0:
            raise ValueError("初始资金必须大于0")
        
        if not self.symbols:
            raise ValueError("必须指定至少一个交易品种")


@dataclass
class BacktestResult:
    """回测结果"""
    
    config: BacktestConfig
    start_time: datetime
    end_time: datetime
    duration: timedelta
    
    # 绩效指标
    initial_capital: Decimal
    final_capital: Decimal
    total_return: Decimal
    annual_return: Decimal
    volatility: Decimal
    sharpe_ratio: Decimal
    max_drawdown: Decimal
    
    # 交易统计
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: Decimal
    avg_win: Decimal
    avg_loss: Decimal
    profit_factor: Decimal
    
    # 详细数据
    portfolio_values: pd.Series = field(default_factory=pd.Series)
    trades_history: List[Dict[str, Any]] = field(default_factory=list)
    positions_history: List[Dict[str, Any]] = field(default_factory=list)
    risk_events: List[Dict[str, Any]] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'config': {
                'start_date': self.config.start_date.isoformat(),
                'end_date': self.config.end_date.isoformat(),
                'initial_capital': float(self.config.initial_capital),
                'symbols': self.config.symbols,
                'timeframe': self.config.timeframe
            },
            'performance': {
                'initial_capital': float(self.initial_capital),
                'final_capital': float(self.final_capital),
                'total_return': float(self.total_return),
                'annual_return': float(self.annual_return),
                'volatility': float(self.volatility),
                'sharpe_ratio': float(self.sharpe_ratio),
                'max_drawdown': float(self.max_drawdown)
            },
            'trading': {
                'total_trades': self.total_trades,
                'winning_trades': self.winning_trades,
                'losing_trades': self.losing_trades,
                'win_rate': float(self.win_rate),
                'avg_win': float(self.avg_win),
                'avg_loss': float(self.avg_loss),
                'profit_factor': float(self.profit_factor)
            },
            'duration': str(self.duration),
            'trades_count': len(self.trades_history),
            'risk_events_count': len(self.risk_events)
        }


class EventDrivenBacktester:
    """
    事件驱动回测器
    
    完整的事件驱动回测框架，协调各个引擎完成回测流程。
    """
    
    def __init__(
        self,
        config: BacktestConfig,
        data_provider: DataProvider,
        strategies: List[BaseStrategy],
        risk_limits: Optional[RiskLimits] = None
    ):
        """
        初始化事件驱动回测器
        
        Args:
            config: 回测配置
            data_provider: 数据提供者
            strategies: 策略列表
            risk_limits: 风险限制
        """
        self.config = config
        self.strategies = strategies
        self._running = False
        self._start_time: Optional[datetime] = None
        self._end_time: Optional[datetime] = None
        
        # 初始化事件系统
        self.message_bus = MessageBus(max_workers=4, enable_async=True)
        self.event_queue = EventQueue(max_size=10000, enable_priority=True)
        self.event_loop = EventLoop(
            event_queue=self.event_queue,
            max_workers=2,
            batch_size=10
        )
        
        # 初始化各个引擎
        self._initialize_engines(data_provider, risk_limits)
        
        # 回测数据收集
        self._portfolio_values: List[Decimal] = []
        self._timestamps: List[datetime] = []
        self._trades_history: List[Dict[str, Any]] = []
        self._positions_history: List[Dict[str, Any]] = []
        self._risk_events: List[Dict[str, Any]] = []
        
        logger.info("事件驱动回测器初始化完成")
    
    def _initialize_engines(
        self,
        data_provider: DataProvider,
        risk_limits: Optional[RiskLimits]
    ) -> None:
        """初始化各个引擎"""
        
        # 数据配置
        data_config = DataConfig(
            symbols=self.config.symbols,
            start_date=self.config.start_date,
            end_date=self.config.end_date,
            timeframe=self.config.timeframe,
            data_source=self.config.data_source,
            data_path=self.config.data_path
        )
        
        # 初始化投资组合
        self.portfolio = Portfolio(initial_capital=self.config.initial_capital)
        
        # 初始化数据引擎
        self.data_engine = DataEngine(
            message_bus=self.message_bus,
            data_provider=data_provider,
            config=data_config
        )
        
        # 初始化策略引擎
        self.strategy_engine = StrategyEngine(message_bus=self.message_bus)
        for strategy in self.strategies:
            self.strategy_engine.add_strategy(strategy)
        
        # 初始化执行引擎
        self.execution_engine = ExecutionEngine(
            message_bus=self.message_bus,
            portfolio=self.portfolio,
            commission_rate=self.config.commission_rate,
            slippage_rate=self.config.slippage_rate
        )
        
        # 初始化风险引擎
        if risk_limits is None:
            risk_limits = RiskLimits()
        
        self.risk_engine = RiskEngine(
            message_bus=self.message_bus,
            risk_limits=risk_limits
        )
        
        # 设置回调函数
        self._setup_callbacks()
    
    def _setup_callbacks(self) -> None:
        """设置回调函数"""
        
        # 数据引擎回调：记录时间戳和组合价值
        def on_market_data(event):
            if hasattr(event, 'timestamp'):
                self._timestamps.append(event.timestamp)
                portfolio_value = self.portfolio.get_total_value()
                self._portfolio_values.append(portfolio_value)
        
        self.data_engine.add_event_callback(on_market_data)
        
        # 执行引擎回调：记录交易历史
        def on_order_filled(event):
            trade_record = {
                'timestamp': event.fill_time,
                'symbol': event.symbol,
                'side': event.side.value,
                'quantity': event.fill_quantity,
                'price': float(event.fill_price),
                'commission': float(event.commission),
                'strategy_id': event.strategy_id
            }
            self._trades_history.append(trade_record)
        
        self.execution_engine.add_fill_callback(on_order_filled)
        
        # 风险引擎回调：记录风险事件
        def on_risk_event(event):
            risk_record = {
                'timestamp': event.timestamp,
                'risk_type': event.risk_type,
                'risk_level': event.risk_level,
                'current_value': float(event.current_value),
                'threshold_value': float(event.threshold_value),
                'risk_message': event.risk_message,
                'affected_symbols': event.affected_symbols
            }
            self._risk_events.append(risk_record)
        
        self.risk_engine.add_risk_callback(on_risk_event)
    
    def run_backtest(self, progress_callback: Optional[Callable[[float], None]] = None) -> BacktestResult:
        """
        运行回测
        
        Args:
            progress_callback: 进度回调函数
            
        Returns:
            BacktestResult: 回测结果
        """
        try:
            self._start_time = datetime.now()
            self._running = True
            
            logger.info(f"开始事件驱动回测: {self.config.start_date} 到 {self.config.end_date}")
            
            # 启动消息总线和事件循环
            self.message_bus.start()
            self.event_loop.start()
            
            # 启动所有策略
            self.strategy_engine.start_all_strategies()
            
            # 开始数据回放
            self.data_engine.start_replay(speed=0)  # 最快速度回放
            
            # 等待数据回放完成
            while self.data_engine.is_running():
                time.sleep(0.1)
                
                # 更新进度
                if progress_callback:
                    current_time = self.data_engine.get_current_time()
                    if current_time:
                        total_days = (self.config.end_date - self.config.start_date).days
                        elapsed_days = (current_time - self.config.start_date).days
                        progress = min(elapsed_days / total_days, 1.0) if total_days > 0 else 1.0
                        progress_callback(progress)
            
            # 停止所有组件
            self.strategy_engine.stop_all_strategies()
            self.event_loop.stop()
            self.message_bus.stop()
            
            self._end_time = datetime.now()
            self._running = False
            
            # 生成回测结果
            result = self._generate_result()
            
            logger.info(f"回测完成，耗时: {self._end_time - self._start_time}")
            return result
            
        except Exception as e:
            logger.error(f"回测执行失败: {e}")
            self._running = False
            raise
    
    def _generate_result(self) -> BacktestResult:
        """生成回测结果"""
        
        # 创建组合价值序列
        if self._timestamps and self._portfolio_values:
            portfolio_series = pd.Series(
                data=[float(v) for v in self._portfolio_values],
                index=self._timestamps
            )
        else:
            portfolio_series = pd.Series()
        
        # 计算绩效指标
        performance_metrics = self._calculate_performance_metrics(portfolio_series)
        
        # 计算交易统计
        trading_stats = self._calculate_trading_stats()
        
        # 创建回测结果
        result = BacktestResult(
            config=self.config,
            start_time=self._start_time or datetime.now(),
            end_time=self._end_time or datetime.now(),
            duration=(self._end_time or datetime.now()) - (self._start_time or datetime.now()),
            
            # 绩效指标
            initial_capital=self.config.initial_capital,
            final_capital=Decimal(str(portfolio_series.iloc[-1])) if len(portfolio_series) > 0 else self.config.initial_capital,
            total_return=performance_metrics.get('total_return', Decimal('0')),
            annual_return=performance_metrics.get('annual_return', Decimal('0')),
            volatility=performance_metrics.get('volatility', Decimal('0')),
            sharpe_ratio=performance_metrics.get('sharpe_ratio', Decimal('0')),
            max_drawdown=performance_metrics.get('max_drawdown', Decimal('0')),
            
            # 交易统计
            total_trades=trading_stats.get('total_trades', 0),
            winning_trades=trading_stats.get('winning_trades', 0),
            losing_trades=trading_stats.get('losing_trades', 0),
            win_rate=trading_stats.get('win_rate', Decimal('0')),
            avg_win=trading_stats.get('avg_win', Decimal('0')),
            avg_loss=trading_stats.get('avg_loss', Decimal('0')),
            profit_factor=trading_stats.get('profit_factor', Decimal('0')),
            
            # 详细数据
            portfolio_values=portfolio_series,
            trades_history=self._trades_history,
            positions_history=self._positions_history,
            risk_events=self._risk_events
        )
        
        return result
    
    def _calculate_performance_metrics(self, portfolio_series: pd.Series) -> Dict[str, Decimal]:
        """计算绩效指标"""
        if len(portfolio_series) == 0:
            return {}
        
        # 计算收益率
        returns = portfolio_series.pct_change().dropna()
        
        # 总收益率
        total_return = (portfolio_series.iloc[-1] / portfolio_series.iloc[0]) - 1
        
        # 年化收益率
        days = len(portfolio_series)
        annual_return = (1 + total_return) ** (252 / days) - 1 if days > 0 else 0
        
        # 波动率
        volatility = returns.std() * np.sqrt(252) if len(returns) > 0 else 0
        
        # 最大回撤
        cumulative_max = portfolio_series.expanding().max()
        drawdown = (portfolio_series - cumulative_max) / cumulative_max
        max_drawdown = abs(drawdown.min())
        
        # 夏普比率
        risk_free_rate = 0.03
        sharpe_ratio = (annual_return - risk_free_rate) / volatility if volatility > 0 else 0
        
        return {
            'total_return': Decimal(str(total_return)),
            'annual_return': Decimal(str(annual_return)),
            'volatility': Decimal(str(volatility)),
            'max_drawdown': Decimal(str(max_drawdown)),
            'sharpe_ratio': Decimal(str(sharpe_ratio))
        }
    
    def _calculate_trading_stats(self) -> Dict[str, Any]:
        """计算交易统计"""
        if not self._trades_history:
            return {}
        
        # 按品种和策略分组计算盈亏
        trades_df = pd.DataFrame(self._trades_history)
        
        # 简化的盈亏计算（实际应该更复杂）
        total_trades = len(trades_df)
        
        # 这里需要更复杂的逻辑来计算实际的盈亏
        # 暂时使用简化版本
        winning_trades = int(total_trades * 0.6)  # 假设60%胜率
        losing_trades = total_trades - winning_trades
        win_rate = Decimal(str(winning_trades / total_trades)) if total_trades > 0 else Decimal('0')
        
        return {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'avg_win': Decimal('100'),  # 简化值
            'avg_loss': Decimal('50'),   # 简化值
            'profit_factor': Decimal('2.0')  # 简化值
        }
    
    def is_running(self) -> bool:
        """检查是否正在运行"""
        return self._running
    
    def stop(self) -> None:
        """停止回测"""
        if self._running:
            self.data_engine.stop_replay()
            self._running = False
            logger.info("回测已停止")
