"""
市场数据事件

定义各种市场数据相关的事件类型，包括：
- 基础市场数据事件
- K线数据事件
- Tick数据事件
- 订单簿事件
"""

import pandas as pd
from datetime import datetime
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from decimal import Decimal

from .base import Event, EventType, TimestampedData


@dataclass
class MarketDataEvent(Event):
    """
    市场数据事件基类
    
    所有市场数据事件的基础类，包含通用的市场数据属性。
    """
    
    symbol: str
    exchange: str = ""
    event_type: EventType = EventType.MARKET_DATA
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'event_id': self.event_id,
            'event_type': self.event_type.value,
            'timestamp': self.timestamp.isoformat(),
            'source': self.source,
            'metadata': self.metadata,
            'symbol': self.symbol,
            'exchange': self.exchange
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MarketDataEvent':
        """从字典创建事件实例"""
        return cls(
            event_id=data['event_id'],
            timestamp=datetime.fromisoformat(data['timestamp']),
            source=data.get('source'),
            metadata=data.get('metadata', {}),
            symbol=data['symbol'],
            exchange=data.get('exchange', '')
        )


@dataclass
class BarEvent(MarketDataEvent):
    """
    K线数据事件
    
    包含OHLCV数据的K线事件，支持不同时间周期的K线数据。
    """
    
    open_price: Decimal
    high_price: Decimal
    low_price: Decimal
    close_price: Decimal
    volume: int
    timeframe: str = "1D"  # 时间周期：1m, 5m, 15m, 1H, 1D等
    event_type: EventType = EventType.BAR_DATA
    
    def __post_init__(self):
        """验证K线数据的有效性"""
        super().__post_init__()
        
        # 验证价格数据的合理性
        if not (self.low_price <= self.open_price <= self.high_price and
                self.low_price <= self.close_price <= self.high_price):
            raise ValueError("K线价格数据不合理：最高价和最低价应该包含开盘价和收盘价")
        
        if self.volume < 0:
            raise ValueError("成交量不能为负数")
    
    @property
    def typical_price(self) -> Decimal:
        """典型价格 (HLC/3)"""
        return (self.high_price + self.low_price + self.close_price) / 3
    
    @property
    def weighted_price(self) -> Decimal:
        """加权平均价格 (OHLC/4)"""
        return (self.open_price + self.high_price + self.low_price + self.close_price) / 4
    
    @property
    def price_change(self) -> Decimal:
        """价格变化"""
        return self.close_price - self.open_price
    
    @property
    def price_change_pct(self) -> Decimal:
        """价格变化百分比"""
        if self.open_price == 0:
            return Decimal('0')
        return (self.close_price - self.open_price) / self.open_price * 100
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        base_dict = super().to_dict()
        base_dict.update({
            'open_price': float(self.open_price),
            'high_price': float(self.high_price),
            'low_price': float(self.low_price),
            'close_price': float(self.close_price),
            'volume': self.volume,
            'timeframe': self.timeframe
        })
        return base_dict
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BarEvent':
        """从字典创建事件实例"""
        return cls(
            event_id=data['event_id'],
            timestamp=datetime.fromisoformat(data['timestamp']),
            source=data.get('source'),
            metadata=data.get('metadata', {}),
            symbol=data['symbol'],
            exchange=data.get('exchange', ''),
            open_price=Decimal(str(data['open_price'])),
            high_price=Decimal(str(data['high_price'])),
            low_price=Decimal(str(data['low_price'])),
            close_price=Decimal(str(data['close_price'])),
            volume=data['volume'],
            timeframe=data.get('timeframe', '1D')
        )


@dataclass
class TickEvent(MarketDataEvent):
    """
    Tick数据事件
    
    包含逐笔成交数据或报价数据的事件。
    """
    
    price: Decimal
    size: int
    tick_type: str = "TRADE"  # TRADE, BID, ASK
    bid_price: Optional[Decimal] = None
    ask_price: Optional[Decimal] = None
    bid_size: Optional[int] = None
    ask_size: Optional[int] = None
    event_type: EventType = EventType.TICK_DATA
    
    def __post_init__(self):
        """验证Tick数据的有效性"""
        super().__post_init__()
        
        if self.price <= 0:
            raise ValueError("价格必须大于0")
        
        if self.size < 0:
            raise ValueError("数量不能为负数")
        
        if self.tick_type not in ["TRADE", "BID", "ASK", "QUOTE"]:
            raise ValueError("Tick类型必须是 TRADE, BID, ASK 或 QUOTE 之一")
    
    @property
    def spread(self) -> Optional[Decimal]:
        """买卖价差"""
        if self.bid_price is not None and self.ask_price is not None:
            return self.ask_price - self.bid_price
        return None
    
    @property
    def mid_price(self) -> Optional[Decimal]:
        """中间价"""
        if self.bid_price is not None and self.ask_price is not None:
            return (self.bid_price + self.ask_price) / 2
        return None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        base_dict = super().to_dict()
        base_dict.update({
            'price': float(self.price),
            'size': self.size,
            'tick_type': self.tick_type,
            'bid_price': float(self.bid_price) if self.bid_price else None,
            'ask_price': float(self.ask_price) if self.ask_price else None,
            'bid_size': self.bid_size,
            'ask_size': self.ask_size
        })
        return base_dict
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TickEvent':
        """从字典创建事件实例"""
        return cls(
            event_id=data['event_id'],
            timestamp=datetime.fromisoformat(data['timestamp']),
            source=data.get('source'),
            metadata=data.get('metadata', {}),
            symbol=data['symbol'],
            exchange=data.get('exchange', ''),
            price=Decimal(str(data['price'])),
            size=data['size'],
            tick_type=data.get('tick_type', 'TRADE'),
            bid_price=Decimal(str(data['bid_price'])) if data.get('bid_price') else None,
            ask_price=Decimal(str(data['ask_price'])) if data.get('ask_price') else None,
            bid_size=data.get('bid_size'),
            ask_size=data.get('ask_size')
        )


@dataclass
class OrderBookEvent(MarketDataEvent):
    """
    订单簿事件
    
    包含订单簿快照或增量更新数据的事件。
    """
    
    bids: list[tuple[Decimal, int]]  # [(价格, 数量), ...]
    asks: list[tuple[Decimal, int]]  # [(价格, 数量), ...]
    is_snapshot: bool = True  # True为快照，False为增量更新
    sequence_number: Optional[int] = None
    
    def __post_init__(self):
        """验证订单簿数据的有效性"""
        super().__post_init__()
        
        # 验证买盘价格递减排序
        if len(self.bids) > 1:
            for i in range(len(self.bids) - 1):
                if self.bids[i][0] < self.bids[i + 1][0]:
                    raise ValueError("买盘价格应该按递减顺序排列")
        
        # 验证卖盘价格递增排序
        if len(self.asks) > 1:
            for i in range(len(self.asks) - 1):
                if self.asks[i][0] > self.asks[i + 1][0]:
                    raise ValueError("卖盘价格应该按递增顺序排列")
    
    @property
    def best_bid(self) -> Optional[tuple[Decimal, int]]:
        """最优买价"""
        return self.bids[0] if self.bids else None
    
    @property
    def best_ask(self) -> Optional[tuple[Decimal, int]]:
        """最优卖价"""
        return self.asks[0] if self.asks else None
    
    @property
    def spread(self) -> Optional[Decimal]:
        """买卖价差"""
        best_bid = self.best_bid
        best_ask = self.best_ask
        if best_bid and best_ask:
            return best_ask[0] - best_bid[0]
        return None
    
    @property
    def mid_price(self) -> Optional[Decimal]:
        """中间价"""
        best_bid = self.best_bid
        best_ask = self.best_ask
        if best_bid and best_ask:
            return (best_bid[0] + best_ask[0]) / 2
        return None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        base_dict = super().to_dict()
        base_dict.update({
            'bids': [[float(price), size] for price, size in self.bids],
            'asks': [[float(price), size] for price, size in self.asks],
            'is_snapshot': self.is_snapshot,
            'sequence_number': self.sequence_number
        })
        return base_dict
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'OrderBookEvent':
        """从字典创建事件实例"""
        return cls(
            event_id=data['event_id'],
            timestamp=datetime.fromisoformat(data['timestamp']),
            source=data.get('source'),
            metadata=data.get('metadata', {}),
            symbol=data['symbol'],
            exchange=data.get('exchange', ''),
            bids=[(Decimal(str(price)), size) for price, size in data['bids']],
            asks=[(Decimal(str(price)), size) for price, size in data['asks']],
            is_snapshot=data.get('is_snapshot', True),
            sequence_number=data.get('sequence_number')
        )
