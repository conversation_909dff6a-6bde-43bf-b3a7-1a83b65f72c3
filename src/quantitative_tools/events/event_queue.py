"""
事件队列和事件循环

实现高性能的事件队列和事件循环机制，包括：
- 优先级事件队列
- 事件循环管理
- 事件调度和分发
- 性能监控和统计
"""

import asyncio
import heapq
import logging
import threading
import time
from collections import deque
from typing import Optional, Callable, Any, Dict, List
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor

from .base import Event, EventType, PriorityEvent, EventPriority

logger = logging.getLogger(__name__)


@dataclass
class QueuedEvent:
    """队列中的事件包装器"""
    
    event: Event
    priority: int = 0
    timestamp: datetime = field(default_factory=datetime.now)
    retry_count: int = 0
    max_retries: int = 3
    
    def __lt__(self, other: 'QueuedEvent') -> bool:
        """支持优先队列排序"""
        if self.priority != other.priority:
            return self.priority > other.priority  # 高优先级排在前面
        return self.timestamp < other.timestamp  # 时间早的排在前面


class EventQueue:
    """
    事件队列
    
    高性能的事件队列实现，支持优先级排序和批量处理。
    """
    
    def __init__(self, max_size: int = 10000, enable_priority: bool = True):
        """
        初始化事件队列
        
        Args:
            max_size: 队列最大容量
            enable_priority: 是否启用优先级排序
        """
        self._max_size = max_size
        self._enable_priority = enable_priority
        self._lock = threading.RLock()
        
        if enable_priority:
            self._queue: List[QueuedEvent] = []  # 优先队列（堆）
        else:
            self._queue = deque(maxlen=max_size)  # 普通队列
        
        self._stats = {
            'enqueued': 0,
            'dequeued': 0,
            'dropped': 0,
            'retries': 0
        }
        
        logger.info(f"事件队列初始化完成，最大容量: {max_size}, 优先级: {enable_priority}")
    
    def put(self, event: Event, priority: int = 0, max_retries: int = 3) -> bool:
        """
        将事件加入队列
        
        Args:
            event: 要加入的事件
            priority: 事件优先级
            max_retries: 最大重试次数
            
        Returns:
            bool: 是否成功加入队列
        """
        with self._lock:
            if self.is_full():
                self._stats['dropped'] += 1
                logger.warning(f"队列已满，丢弃事件: {event}")
                return False
            
            queued_event = QueuedEvent(
                event=event,
                priority=priority,
                max_retries=max_retries
            )
            
            if self._enable_priority:
                heapq.heappush(self._queue, queued_event)
            else:
                self._queue.append(queued_event)
            
            self._stats['enqueued'] += 1
            logger.debug(f"事件已加入队列: {event}")
            return True
    
    def get(self, timeout: Optional[float] = None) -> Optional[QueuedEvent]:
        """
        从队列获取事件
        
        Args:
            timeout: 超时时间（秒）
            
        Returns:
            QueuedEvent: 队列中的事件，如果队列为空则返回None
        """
        start_time = time.time()
        
        while True:
            with self._lock:
                if self._queue:
                    if self._enable_priority:
                        queued_event = heapq.heappop(self._queue)
                    else:
                        queued_event = self._queue.popleft()
                    
                    self._stats['dequeued'] += 1
                    logger.debug(f"从队列获取事件: {queued_event.event}")
                    return queued_event
            
            # 检查超时
            if timeout is not None and time.time() - start_time > timeout:
                return None
            
            # 短暂休眠
            time.sleep(0.001)
    
    def get_batch(self, batch_size: int = 10) -> List[QueuedEvent]:
        """
        批量获取事件
        
        Args:
            batch_size: 批量大小
            
        Returns:
            List[QueuedEvent]: 事件列表
        """
        events = []
        
        with self._lock:
            for _ in range(min(batch_size, len(self._queue))):
                if self._queue:
                    if self._enable_priority:
                        events.append(heapq.heappop(self._queue))
                    else:
                        events.append(self._queue.popleft())
            
            self._stats['dequeued'] += len(events)
        
        logger.debug(f"批量获取 {len(events)} 个事件")
        return events
    
    def retry_event(self, queued_event: QueuedEvent) -> bool:
        """
        重试事件
        
        Args:
            queued_event: 要重试的事件
            
        Returns:
            bool: 是否成功重新加入队列
        """
        if queued_event.retry_count >= queued_event.max_retries:
            logger.warning(f"事件重试次数超限，丢弃: {queued_event.event}")
            return False
        
        queued_event.retry_count += 1
        queued_event.timestamp = datetime.now()
        
        with self._lock:
            if self.is_full():
                logger.warning(f"队列已满，无法重试事件: {queued_event.event}")
                return False
            
            if self._enable_priority:
                heapq.heappush(self._queue, queued_event)
            else:
                self._queue.append(queued_event)
            
            self._stats['retries'] += 1
        
        logger.debug(f"事件重试 ({queued_event.retry_count}/{queued_event.max_retries}): {queued_event.event}")
        return True
    
    def size(self) -> int:
        """获取队列大小"""
        with self._lock:
            return len(self._queue)
    
    def is_empty(self) -> bool:
        """检查队列是否为空"""
        return self.size() == 0
    
    def is_full(self) -> bool:
        """检查队列是否已满"""
        return self.size() >= self._max_size
    
    def clear(self) -> int:
        """清空队列"""
        with self._lock:
            count = len(self._queue)
            if self._enable_priority:
                self._queue.clear()
            else:
                self._queue.clear()
            
            logger.info(f"队列已清空，清除 {count} 个事件")
            return count
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            return {
                **self._stats,
                'current_size': len(self._queue),
                'max_size': self._max_size,
                'is_full': self.is_full(),
                'is_empty': self.is_empty()
            }


class EventLoop:
    """
    事件循环
    
    管理事件的调度和分发，支持同步和异步处理。
    """
    
    def __init__(
        self,
        event_queue: EventQueue,
        max_workers: int = 4,
        batch_size: int = 10,
        process_interval: float = 0.001
    ):
        """
        初始化事件循环
        
        Args:
            event_queue: 事件队列
            max_workers: 最大工作线程数
            batch_size: 批处理大小
            process_interval: 处理间隔（秒）
        """
        self._event_queue = event_queue
        self._max_workers = max_workers
        self._batch_size = batch_size
        self._process_interval = process_interval
        self._running = False
        self._executor = ThreadPoolExecutor(max_workers=max_workers)
        self._handlers: List[Callable[[Event], None]] = []
        self._async_handlers: List[Callable[[Event], Any]] = []
        self._loop: Optional[asyncio.AbstractEventLoop] = None
        self._stats = {
            'events_processed': 0,
            'events_failed': 0,
            'processing_time_total': 0.0,
            'avg_processing_time': 0.0
        }
        
        logger.info(f"事件循环初始化完成，工作线程数: {max_workers}, 批处理大小: {batch_size}")
    
    def add_handler(self, handler: Callable[[Event], None]) -> None:
        """添加同步事件处理器"""
        self._handlers.append(handler)
        logger.info(f"添加同步事件处理器: {handler.__name__}")
    
    def add_async_handler(self, handler: Callable[[Event], Any]) -> None:
        """添加异步事件处理器"""
        self._async_handlers.append(handler)
        logger.info(f"添加异步事件处理器: {handler.__name__}")
    
    def start(self) -> None:
        """启动事件循环"""
        if self._running:
            logger.warning("事件循环已在运行")
            return
        
        self._running = True
        
        # 启动同步处理线程
        threading.Thread(target=self._run_sync_loop, daemon=True).start()
        
        # 启动异步处理线程
        if self._async_handlers:
            threading.Thread(target=self._run_async_loop, daemon=True).start()
        
        logger.info("事件循环已启动")
    
    def stop(self) -> None:
        """停止事件循环"""
        self._running = False
        
        # 等待处理完成
        while not self._event_queue.is_empty():
            time.sleep(0.1)
        
        self._executor.shutdown(wait=True)
        
        if self._loop:
            self._loop.call_soon_threadsafe(self._loop.stop)
        
        logger.info("事件循环已停止")
    
    def _run_sync_loop(self) -> None:
        """运行同步事件循环"""
        while self._running:
            try:
                # 批量获取事件
                events = self._event_queue.get_batch(self._batch_size)
                
                if events:
                    # 并行处理事件
                    futures = []
                    for queued_event in events:
                        future = self._executor.submit(self._process_event_sync, queued_event)
                        futures.append(future)
                    
                    # 等待所有任务完成
                    for future in futures:
                        try:
                            future.result(timeout=1.0)
                        except Exception as e:
                            logger.error(f"同步事件处理失败: {e}")
                            self._stats['events_failed'] += 1
                else:
                    time.sleep(self._process_interval)
                    
            except Exception as e:
                logger.error(f"同步事件循环异常: {e}")
    
    def _run_async_loop(self) -> None:
        """运行异步事件循环"""
        try:
            self._loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self._loop)
            
            async def async_processor():
                while self._running:
                    try:
                        # 获取事件
                        queued_event = self._event_queue.get(timeout=self._process_interval)
                        
                        if queued_event:
                            await self._process_event_async(queued_event)
                        else:
                            await asyncio.sleep(self._process_interval)
                            
                    except Exception as e:
                        logger.error(f"异步事件处理异常: {e}")
                        self._stats['events_failed'] += 1
            
            self._loop.run_until_complete(async_processor())
            
        except Exception as e:
            logger.error(f"异步事件循环异常: {e}")
        finally:
            if self._loop:
                self._loop.close()
    
    def _process_event_sync(self, queued_event: QueuedEvent) -> None:
        """同步处理事件"""
        start_time = time.time()
        
        try:
            for handler in self._handlers:
                try:
                    handler(queued_event.event)
                except Exception as e:
                    logger.error(f"同步处理器执行失败: {e}")
                    # 重试事件
                    if not self._event_queue.retry_event(queued_event):
                        self._stats['events_failed'] += 1
                    return
            
            self._stats['events_processed'] += 1
            
        except Exception as e:
            logger.error(f"同步事件处理失败: {e}")
            self._stats['events_failed'] += 1
        finally:
            processing_time = time.time() - start_time
            self._stats['processing_time_total'] += processing_time
            self._stats['avg_processing_time'] = (
                self._stats['processing_time_total'] / 
                max(1, self._stats['events_processed'])
            )
    
    async def _process_event_async(self, queued_event: QueuedEvent) -> None:
        """异步处理事件"""
        start_time = time.time()
        
        try:
            tasks = []
            for handler in self._async_handlers:
                task = asyncio.create_task(handler(queued_event.event))
                tasks.append(task)
            
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
            
            self._stats['events_processed'] += 1
            
        except Exception as e:
            logger.error(f"异步事件处理失败: {e}")
            # 重试事件
            if not self._event_queue.retry_event(queued_event):
                self._stats['events_failed'] += 1
        finally:
            processing_time = time.time() - start_time
            self._stats['processing_time_total'] += processing_time
            self._stats['avg_processing_time'] = (
                self._stats['processing_time_total'] / 
                max(1, self._stats['events_processed'])
            )
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self._stats,
            'is_running': self._running,
            'handlers_count': len(self._handlers),
            'async_handlers_count': len(self._async_handlers),
            'queue_stats': self._event_queue.get_stats()
        }
    
    def __enter__(self):
        """上下文管理器入口"""
        self.start()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.stop()
