"""
组合事件

定义投资组合相关的事件类型，包括：
- 基础组合事件
- 仓位事件
- 余额事件
- 风险事件
"""

from datetime import datetime
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from decimal import Decimal
from enum import Enum

from .base import Event, EventType


class PositionSide(Enum):
    """仓位方向枚举"""
    
    LONG = "LONG"
    SHORT = "SHORT"
    FLAT = "FLAT"  # 空仓


@dataclass
class PortfolioEvent(Event):
    """
    基础组合事件
    
    所有投资组合相关事件的基础类。
    """
    
    portfolio_id: str
    account_id: str = ""
    event_type: EventType = field(init=False)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'event_id': self.event_id,
            'event_type': self.event_type.value,
            'timestamp': self.timestamp.isoformat(),
            'source': self.source,
            'metadata': self.metadata,
            'portfolio_id': self.portfolio_id,
            'account_id': self.account_id
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PortfolioEvent':
        """从字典创建事件实例"""
        return cls(
            event_id=data['event_id'],
            timestamp=datetime.fromisoformat(data['timestamp']),
            source=data.get('source'),
            metadata=data.get('metadata', {}),
            portfolio_id=data['portfolio_id'],
            account_id=data.get('account_id', '')
        )


@dataclass
class PositionEvent(PortfolioEvent):
    """
    仓位事件
    
    当仓位发生变化时触发的事件，包括开仓、平仓、加仓、减仓等。
    """
    
    symbol: str
    side: PositionSide
    quantity: int
    avg_price: Decimal
    market_price: Decimal
    unrealized_pnl: Decimal = Decimal('0')
    realized_pnl: Decimal = Decimal('0')
    commission: Decimal = Decimal('0')
    strategy_id: Optional[str] = None
    event_type: EventType = EventType.POSITION_OPENED
    
    def __post_init__(self):
        """验证仓位数据的有效性"""
        super().__post_init__()
        
        if self.quantity < 0:
            raise ValueError("仓位数量不能为负数")
        
        if self.avg_price <= 0:
            raise ValueError("平均价格必须大于0")
        
        if self.market_price <= 0:
            raise ValueError("市场价格必须大于0")
        
        if self.commission < 0:
            raise ValueError("手续费不能为负数")
    
    @property
    def market_value(self) -> Decimal:
        """市值"""
        return self.market_price * self.quantity
    
    @property
    def cost_basis(self) -> Decimal:
        """成本基础"""
        return self.avg_price * self.quantity
    
    @property
    def total_pnl(self) -> Decimal:
        """总盈亏"""
        return self.unrealized_pnl + self.realized_pnl
    
    @property
    def pnl_percentage(self) -> Decimal:
        """盈亏百分比"""
        if self.cost_basis == 0:
            return Decimal('0')
        return (self.total_pnl / self.cost_basis) * 100
    
    @property
    def is_long(self) -> bool:
        """是否为多头仓位"""
        return self.side == PositionSide.LONG
    
    @property
    def is_short(self) -> bool:
        """是否为空头仓位"""
        return self.side == PositionSide.SHORT
    
    @property
    def is_flat(self) -> bool:
        """是否为空仓"""
        return self.side == PositionSide.FLAT or self.quantity == 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        base_dict = super().to_dict()
        base_dict.update({
            'symbol': self.symbol,
            'side': self.side.value,
            'quantity': self.quantity,
            'avg_price': float(self.avg_price),
            'market_price': float(self.market_price),
            'unrealized_pnl': float(self.unrealized_pnl),
            'realized_pnl': float(self.realized_pnl),
            'commission': float(self.commission),
            'strategy_id': self.strategy_id
        })
        return base_dict
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PositionEvent':
        """从字典创建事件实例"""
        return cls(
            event_id=data['event_id'],
            timestamp=datetime.fromisoformat(data['timestamp']),
            source=data.get('source'),
            metadata=data.get('metadata', {}),
            portfolio_id=data['portfolio_id'],
            account_id=data.get('account_id', ''),
            symbol=data['symbol'],
            side=PositionSide(data['side']),
            quantity=data['quantity'],
            avg_price=Decimal(str(data['avg_price'])),
            market_price=Decimal(str(data['market_price'])),
            unrealized_pnl=Decimal(str(data['unrealized_pnl'])),
            realized_pnl=Decimal(str(data['realized_pnl'])),
            commission=Decimal(str(data['commission'])),
            strategy_id=data.get('strategy_id')
        )


@dataclass
class BalanceEvent(PortfolioEvent):
    """
    余额事件
    
    当账户余额发生变化时触发的事件。
    """
    
    currency: str
    total_balance: Decimal
    available_balance: Decimal
    frozen_balance: Decimal = Decimal('0')
    margin_used: Decimal = Decimal('0')
    margin_available: Decimal = Decimal('0')
    event_type: EventType = EventType.BALANCE_UPDATED
    
    def __post_init__(self):
        """验证余额数据的有效性"""
        super().__post_init__()
        
        if self.total_balance < 0:
            raise ValueError("总余额不能为负数")
        
        if self.available_balance < 0:
            raise ValueError("可用余额不能为负数")
        
        if self.frozen_balance < 0:
            raise ValueError("冻结余额不能为负数")
        
        if self.margin_used < 0:
            raise ValueError("已用保证金不能为负数")
        
        if self.margin_available < 0:
            raise ValueError("可用保证金不能为负数")
        
        # 验证余额关系
        if self.available_balance + self.frozen_balance > self.total_balance:
            raise ValueError("可用余额 + 冻结余额不能超过总余额")
    
    @property
    def utilization_rate(self) -> Decimal:
        """资金利用率"""
        if self.total_balance == 0:
            return Decimal('0')
        return (self.frozen_balance / self.total_balance) * 100
    
    @property
    def margin_ratio(self) -> Decimal:
        """保证金比率"""
        if self.margin_available == 0:
            return Decimal('0')
        return (self.margin_used / (self.margin_used + self.margin_available)) * 100
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        base_dict = super().to_dict()
        base_dict.update({
            'currency': self.currency,
            'total_balance': float(self.total_balance),
            'available_balance': float(self.available_balance),
            'frozen_balance': float(self.frozen_balance),
            'margin_used': float(self.margin_used),
            'margin_available': float(self.margin_available)
        })
        return base_dict
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BalanceEvent':
        """从字典创建事件实例"""
        return cls(
            event_id=data['event_id'],
            timestamp=datetime.fromisoformat(data['timestamp']),
            source=data.get('source'),
            metadata=data.get('metadata', {}),
            portfolio_id=data['portfolio_id'],
            account_id=data.get('account_id', ''),
            currency=data['currency'],
            total_balance=Decimal(str(data['total_balance'])),
            available_balance=Decimal(str(data['available_balance'])),
            frozen_balance=Decimal(str(data['frozen_balance'])),
            margin_used=Decimal(str(data['margin_used'])),
            margin_available=Decimal(str(data['margin_available']))
        )


@dataclass
class PortfolioPerformanceEvent(PortfolioEvent):
    """
    组合绩效事件
    
    定期更新的投资组合绩效指标事件。
    """
    
    total_value: Decimal
    total_pnl: Decimal
    daily_pnl: Decimal
    total_return: Decimal
    daily_return: Decimal
    max_drawdown: Decimal
    sharpe_ratio: Optional[Decimal] = None
    win_rate: Optional[Decimal] = None
    total_trades: int = 0
    event_type: EventType = field(default=EventType.BALANCE_UPDATED, init=False)
    
    def __post_init__(self):
        """设置事件类型"""
        super().__post_init__()
        # 使用自定义事件类型，这里暂时使用BALANCE_UPDATED
        # 在实际应用中可以扩展EventType枚举
    
    @property
    def profit_factor(self) -> Optional[Decimal]:
        """盈利因子"""
        # 这里需要更多数据来计算，暂时返回None
        return None
    
    @property
    def is_profitable(self) -> bool:
        """是否盈利"""
        return self.total_pnl > 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        base_dict = super().to_dict()
        base_dict.update({
            'total_value': float(self.total_value),
            'total_pnl': float(self.total_pnl),
            'daily_pnl': float(self.daily_pnl),
            'total_return': float(self.total_return),
            'daily_return': float(self.daily_return),
            'max_drawdown': float(self.max_drawdown),
            'sharpe_ratio': float(self.sharpe_ratio) if self.sharpe_ratio else None,
            'win_rate': float(self.win_rate) if self.win_rate else None,
            'total_trades': self.total_trades
        })
        return base_dict
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PortfolioPerformanceEvent':
        """从字典创建事件实例"""
        return cls(
            event_id=data['event_id'],
            timestamp=datetime.fromisoformat(data['timestamp']),
            source=data.get('source'),
            metadata=data.get('metadata', {}),
            portfolio_id=data['portfolio_id'],
            account_id=data.get('account_id', ''),
            total_value=Decimal(str(data['total_value'])),
            total_pnl=Decimal(str(data['total_pnl'])),
            daily_pnl=Decimal(str(data['daily_pnl'])),
            total_return=Decimal(str(data['total_return'])),
            daily_return=Decimal(str(data['daily_return'])),
            max_drawdown=Decimal(str(data['max_drawdown'])),
            sharpe_ratio=Decimal(str(data['sharpe_ratio'])) if data.get('sharpe_ratio') else None,
            win_rate=Decimal(str(data['win_rate'])) if data.get('win_rate') else None,
            total_trades=data.get('total_trades', 0)
        )


@dataclass
class RiskEvent(PortfolioEvent):
    """
    风险事件
    
    当投资组合触发风险规则时产生的事件。
    """
    
    risk_type: str  # 风险类型
    risk_level: str  # 风险级别：low, medium, high, critical
    current_value: Decimal
    threshold_value: Decimal
    risk_message: str
    action_required: str = ""  # 建议采取的行动
    affected_symbols: list[str] = field(default_factory=list)
    event_type: EventType = EventType.RISK_VIOLATION
    
    def __post_init__(self):
        """验证风险事件数据"""
        super().__post_init__()
        
        if self.risk_level not in ['low', 'medium', 'high', 'critical']:
            raise ValueError("风险级别必须是 low, medium, high, critical 之一")
    
    @property
    def risk_ratio(self) -> Decimal:
        """风险比率"""
        if self.threshold_value == 0:
            return Decimal('inf') if self.current_value != 0 else Decimal('0')
        return self.current_value / self.threshold_value
    
    @property
    def is_critical(self) -> bool:
        """是否为严重风险"""
        return self.risk_level == 'critical'
    
    @property
    def requires_action(self) -> bool:
        """是否需要采取行动"""
        return bool(self.action_required.strip())
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        base_dict = super().to_dict()
        base_dict.update({
            'risk_type': self.risk_type,
            'risk_level': self.risk_level,
            'current_value': float(self.current_value),
            'threshold_value': float(self.threshold_value),
            'risk_message': self.risk_message,
            'action_required': self.action_required,
            'affected_symbols': self.affected_symbols
        })
        return base_dict
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RiskEvent':
        """从字典创建事件实例"""
        return cls(
            event_id=data['event_id'],
            timestamp=datetime.fromisoformat(data['timestamp']),
            source=data.get('source'),
            metadata=data.get('metadata', {}),
            portfolio_id=data['portfolio_id'],
            account_id=data.get('account_id', ''),
            risk_type=data['risk_type'],
            risk_level=data['risk_level'],
            current_value=Decimal(str(data['current_value'])),
            threshold_value=Decimal(str(data['threshold_value'])),
            risk_message=data['risk_message'],
            action_required=data.get('action_required', ''),
            affected_symbols=data.get('affected_symbols', [])
        )
