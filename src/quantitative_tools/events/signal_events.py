"""
信号事件

定义策略信号相关的事件类型，包括：
- 基础信号事件
- 策略信号事件
- 技术指标信号
- 风险信号
"""

from datetime import datetime
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, field
from decimal import Decimal
from enum import Enum

from .base import Event, EventType


class SignalType(Enum):
    """信号类型枚举"""
    
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"
    CLOSE_LONG = "CLOSE_LONG"
    CLOSE_SHORT = "CLOSE_SHORT"
    STOP_LOSS = "STOP_LOSS"
    TAKE_PROFIT = "TAKE_PROFIT"


class SignalStrength(Enum):
    """信号强度枚举"""
    
    WEAK = 1
    MODERATE = 2
    STRONG = 3
    VERY_STRONG = 4


@dataclass
class SignalEvent(Event):
    """
    基础信号事件
    
    所有交易信号事件的基础类，包含信号的基本属性。
    """
    
    symbol: str
    signal_type: SignalType
    strength: SignalStrength = SignalStrength.MODERATE
    confidence: float = 0.5  # 信号置信度 0-1
    target_price: Optional[Decimal] = None
    stop_loss: Optional[Decimal] = None
    take_profit: Optional[Decimal] = None
    quantity: Optional[int] = None
    event_type: EventType = EventType.SIGNAL
    
    def __post_init__(self):
        """验证信号数据的有效性"""
        super().__post_init__()
        
        if not 0 <= self.confidence <= 1:
            raise ValueError("信号置信度必须在0-1之间")
        
        if self.quantity is not None and self.quantity <= 0:
            raise ValueError("交易数量必须大于0")
    
    @property
    def is_entry_signal(self) -> bool:
        """是否为入场信号"""
        return self.signal_type in [SignalType.BUY, SignalType.SELL]
    
    @property
    def is_exit_signal(self) -> bool:
        """是否为出场信号"""
        return self.signal_type in [
            SignalType.CLOSE_LONG, 
            SignalType.CLOSE_SHORT,
            SignalType.STOP_LOSS,
            SignalType.TAKE_PROFIT
        ]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'event_id': self.event_id,
            'event_type': self.event_type.value,
            'timestamp': self.timestamp.isoformat(),
            'source': self.source,
            'metadata': self.metadata,
            'symbol': self.symbol,
            'signal_type': self.signal_type.value,
            'strength': self.strength.value,
            'confidence': self.confidence,
            'target_price': float(self.target_price) if self.target_price else None,
            'stop_loss': float(self.stop_loss) if self.stop_loss else None,
            'take_profit': float(self.take_profit) if self.take_profit else None,
            'quantity': self.quantity
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SignalEvent':
        """从字典创建事件实例"""
        return cls(
            event_id=data['event_id'],
            timestamp=datetime.fromisoformat(data['timestamp']),
            source=data.get('source'),
            metadata=data.get('metadata', {}),
            symbol=data['symbol'],
            signal_type=SignalType(data['signal_type']),
            strength=SignalStrength(data['strength']),
            confidence=data['confidence'],
            target_price=Decimal(str(data['target_price'])) if data.get('target_price') else None,
            stop_loss=Decimal(str(data['stop_loss'])) if data.get('stop_loss') else None,
            take_profit=Decimal(str(data['take_profit'])) if data.get('take_profit') else None,
            quantity=data.get('quantity')
        )


@dataclass
class StrategySignal(SignalEvent):
    """
    策略信号事件
    
    由具体策略生成的信号事件，包含策略相关的详细信息。
    """
    
    strategy_id: str
    strategy_name: str
    indicator_values: Dict[str, float] = field(default_factory=dict)
    reasoning: str = ""  # 信号产生的原因说明
    event_type: EventType = EventType.STRATEGY_SIGNAL
    
    def add_indicator(self, name: str, value: float) -> None:
        """添加技术指标值"""
        self.indicator_values[name] = value
    
    def get_indicator(self, name: str) -> Optional[float]:
        """获取技术指标值"""
        return self.indicator_values.get(name)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        base_dict = super().to_dict()
        base_dict.update({
            'strategy_id': self.strategy_id,
            'strategy_name': self.strategy_name,
            'indicator_values': self.indicator_values,
            'reasoning': self.reasoning
        })
        return base_dict
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'StrategySignal':
        """从字典创建事件实例"""
        return cls(
            event_id=data['event_id'],
            timestamp=datetime.fromisoformat(data['timestamp']),
            source=data.get('source'),
            metadata=data.get('metadata', {}),
            symbol=data['symbol'],
            signal_type=SignalType(data['signal_type']),
            strength=SignalStrength(data['strength']),
            confidence=data['confidence'],
            target_price=Decimal(str(data['target_price'])) if data.get('target_price') else None,
            stop_loss=Decimal(str(data['stop_loss'])) if data.get('stop_loss') else None,
            take_profit=Decimal(str(data['take_profit'])) if data.get('take_profit') else None,
            quantity=data.get('quantity'),
            strategy_id=data['strategy_id'],
            strategy_name=data['strategy_name'],
            indicator_values=data.get('indicator_values', {}),
            reasoning=data.get('reasoning', '')
        )


@dataclass
class TechnicalIndicatorSignal(StrategySignal):
    """
    技术指标信号事件
    
    基于技术指标生成的信号事件，包含指标的详细计算结果。
    """
    
    indicator_name: str
    indicator_period: int
    current_value: float
    previous_value: Optional[float] = None
    threshold_upper: Optional[float] = None
    threshold_lower: Optional[float] = None
    
    def __post_init__(self):
        """验证技术指标信号的有效性"""
        super().__post_init__()
        
        if self.indicator_period <= 0:
            raise ValueError("指标周期必须大于0")
    
    @property
    def value_change(self) -> Optional[float]:
        """指标值变化"""
        if self.previous_value is not None:
            return self.current_value - self.previous_value
        return None
    
    @property
    def value_change_pct(self) -> Optional[float]:
        """指标值变化百分比"""
        if self.previous_value is not None and self.previous_value != 0:
            return (self.current_value - self.previous_value) / abs(self.previous_value) * 100
        return None
    
    @property
    def is_overbought(self) -> bool:
        """是否超买"""
        return (self.threshold_upper is not None and 
                self.current_value > self.threshold_upper)
    
    @property
    def is_oversold(self) -> bool:
        """是否超卖"""
        return (self.threshold_lower is not None and 
                self.current_value < self.threshold_lower)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        base_dict = super().to_dict()
        base_dict.update({
            'indicator_name': self.indicator_name,
            'indicator_period': self.indicator_period,
            'current_value': self.current_value,
            'previous_value': self.previous_value,
            'threshold_upper': self.threshold_upper,
            'threshold_lower': self.threshold_lower
        })
        return base_dict
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TechnicalIndicatorSignal':
        """从字典创建事件实例"""
        return cls(
            event_id=data['event_id'],
            timestamp=datetime.fromisoformat(data['timestamp']),
            source=data.get('source'),
            metadata=data.get('metadata', {}),
            symbol=data['symbol'],
            signal_type=SignalType(data['signal_type']),
            strength=SignalStrength(data['strength']),
            confidence=data['confidence'],
            target_price=Decimal(str(data['target_price'])) if data.get('target_price') else None,
            stop_loss=Decimal(str(data['stop_loss'])) if data.get('stop_loss') else None,
            take_profit=Decimal(str(data['take_profit'])) if data.get('take_profit') else None,
            quantity=data.get('quantity'),
            strategy_id=data['strategy_id'],
            strategy_name=data['strategy_name'],
            indicator_values=data.get('indicator_values', {}),
            reasoning=data.get('reasoning', ''),
            indicator_name=data['indicator_name'],
            indicator_period=data['indicator_period'],
            current_value=data['current_value'],
            previous_value=data.get('previous_value'),
            threshold_upper=data.get('threshold_upper'),
            threshold_lower=data.get('threshold_lower')
        )


@dataclass
class RiskSignal(SignalEvent):
    """
    风险信号事件
    
    由风险管理模块生成的信号事件，用于风险控制和预警。
    """
    
    risk_type: str  # 风险类型：position_limit, drawdown, volatility等
    risk_level: str  # 风险级别：low, medium, high, critical
    current_value: float
    threshold_value: float
    action_required: str  # 建议采取的行动
    event_type: EventType = EventType.SIGNAL
    
    def __post_init__(self):
        """验证风险信号的有效性"""
        super().__post_init__()
        
        if self.risk_level not in ['low', 'medium', 'high', 'critical']:
            raise ValueError("风险级别必须是 low, medium, high, critical 之一")
    
    @property
    def risk_ratio(self) -> float:
        """风险比率"""
        if self.threshold_value == 0:
            return float('inf') if self.current_value != 0 else 0
        return self.current_value / self.threshold_value
    
    @property
    def is_critical(self) -> bool:
        """是否为严重风险"""
        return self.risk_level == 'critical'
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        base_dict = super().to_dict()
        base_dict.update({
            'risk_type': self.risk_type,
            'risk_level': self.risk_level,
            'current_value': self.current_value,
            'threshold_value': self.threshold_value,
            'action_required': self.action_required
        })
        return base_dict
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RiskSignal':
        """从字典创建事件实例"""
        return cls(
            event_id=data['event_id'],
            timestamp=datetime.fromisoformat(data['timestamp']),
            source=data.get('source'),
            metadata=data.get('metadata', {}),
            symbol=data['symbol'],
            signal_type=SignalType(data['signal_type']),
            strength=SignalStrength(data['strength']),
            confidence=data['confidence'],
            target_price=Decimal(str(data['target_price'])) if data.get('target_price') else None,
            stop_loss=Decimal(str(data['stop_loss'])) if data.get('stop_loss') else None,
            take_profit=Decimal(str(data['take_profit'])) if data.get('take_profit') else None,
            quantity=data.get('quantity'),
            risk_type=data['risk_type'],
            risk_level=data['risk_level'],
            current_value=data['current_value'],
            threshold_value=data['threshold_value'],
            action_required=data['action_required']
        )
