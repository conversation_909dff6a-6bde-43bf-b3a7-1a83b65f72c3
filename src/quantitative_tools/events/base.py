"""
事件系统基础类

定义事件驱动架构的核心抽象类和接口，包括：
- 事件基类
- 事件类型枚举
- 事件处理器接口
- 时间戳管理
"""

import uuid
from abc import ABC, abstractmethod
from datetime import datetime
from enum import Enum
from typing import Any, Dict, Optional, Union
from dataclasses import dataclass, field


class EventType(Enum):
    """事件类型枚举"""
    
    # 市场数据事件
    MARKET_DATA = "market_data"
    BAR_DATA = "bar_data"
    TICK_DATA = "tick_data"
    
    # 策略信号事件
    SIGNAL = "signal"
    STRATEGY_SIGNAL = "strategy_signal"
    
    # 订单事件
    ORDER_SUBMITTED = "order_submitted"
    ORDER_ACCEPTED = "order_accepted"
    ORDER_REJECTED = "order_rejected"
    ORDER_FILLED = "order_filled"
    ORDER_PARTIALLY_FILLED = "order_partially_filled"
    ORDER_CANCELLED = "order_cancelled"
    ORDER_EXPIRED = "order_expired"
    
    # 组合事件
    POSITION_OPENED = "position_opened"
    POSITION_CLOSED = "position_closed"
    POSITION_UPDATED = "position_updated"
    BALANCE_UPDATED = "balance_updated"
    
    # 风险事件
    RISK_VIOLATION = "risk_violation"
    RISK_WARNING = "risk_warning"
    
    # 系统事件
    SYSTEM_START = "system_start"
    SYSTEM_STOP = "system_stop"
    HEARTBEAT = "heartbeat"


@dataclass
class Event(ABC):
    """
    事件基类
    
    所有事件的基础抽象类，定义了事件的基本属性和接口。
    每个事件都有唯一的ID、类型、时间戳等基本信息。
    """
    
    event_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    event_type: EventType = field(init=False)
    timestamp: datetime = field(default_factory=datetime.now)
    source: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """事件初始化后处理"""
        if not hasattr(self, 'event_type') or self.event_type is None:
            raise ValueError("事件类型不能为空")
    
    @abstractmethod
    def to_dict(self) -> Dict[str, Any]:
        """将事件转换为字典格式"""
        pass
    
    @classmethod
    @abstractmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Event':
        """从字典创建事件实例"""
        pass
    
    def __str__(self) -> str:
        """事件字符串表示"""
        return f"{self.__class__.__name__}(id={self.event_id[:8]}, type={self.event_type.value}, time={self.timestamp})"
    
    def __repr__(self) -> str:
        """事件详细表示"""
        return self.__str__()


class EventHandler(ABC):
    """
    事件处理器接口
    
    定义事件处理器的标准接口，所有事件处理器都应该实现此接口。
    支持同步和异步事件处理。
    """
    
    @abstractmethod
    def handle_event(self, event: Event) -> None:
        """
        处理事件
        
        Args:
            event: 要处理的事件
        """
        pass
    
    @abstractmethod
    def can_handle(self, event_type: EventType) -> bool:
        """
        检查是否可以处理指定类型的事件
        
        Args:
            event_type: 事件类型
            
        Returns:
            bool: 是否可以处理
        """
        pass
    
    @property
    @abstractmethod
    def handler_name(self) -> str:
        """处理器名称"""
        pass


@dataclass
class TimestampedData:
    """
    带时间戳的数据基类
    
    为所有需要时间戳的数据提供统一的时间管理接口。
    支持事件时间和系统时间的区分。
    """
    
    ts_event: datetime  # 事件发生时间
    ts_init: datetime = field(default_factory=datetime.now)  # 数据初始化时间
    
    def __post_init__(self):
        """确保时间戳的有效性"""
        if self.ts_event > self.ts_init:
            # 如果事件时间晚于初始化时间，调整初始化时间
            self.ts_init = datetime.now()


class EventPriority(Enum):
    """事件优先级枚举"""
    
    LOW = 0
    NORMAL = 1
    HIGH = 2
    CRITICAL = 3


@dataclass
class PriorityEvent(Event):
    """
    带优先级的事件
    
    扩展基础事件，添加优先级支持，用于事件队列的优先级排序。
    """
    
    priority: EventPriority = EventPriority.NORMAL
    
    def __lt__(self, other: 'PriorityEvent') -> bool:
        """支持优先级比较，用于优先队列排序"""
        if not isinstance(other, PriorityEvent):
            return NotImplemented
        
        # 优先级高的事件排在前面
        if self.priority.value != other.priority.value:
            return self.priority.value > other.priority.value
        
        # 优先级相同时，按时间戳排序
        return self.timestamp < other.timestamp


class EventFilter(ABC):
    """
    事件过滤器接口
    
    用于在事件处理前进行过滤，支持复杂的事件筛选逻辑。
    """
    
    @abstractmethod
    def should_process(self, event: Event) -> bool:
        """
        判断是否应该处理该事件
        
        Args:
            event: 待判断的事件
            
        Returns:
            bool: 是否应该处理
        """
        pass


class CompositeEventFilter(EventFilter):
    """
    复合事件过滤器
    
    支持多个过滤器的组合，可以实现AND、OR等逻辑组合。
    """
    
    def __init__(self, filters: list[EventFilter], logic: str = "AND"):
        """
        初始化复合过滤器
        
        Args:
            filters: 过滤器列表
            logic: 组合逻辑，"AND" 或 "OR"
        """
        self.filters = filters
        self.logic = logic.upper()
        
        if self.logic not in ["AND", "OR"]:
            raise ValueError("逻辑操作符必须是 'AND' 或 'OR'")
    
    def should_process(self, event: Event) -> bool:
        """根据组合逻辑判断是否处理事件"""
        if not self.filters:
            return True
        
        if self.logic == "AND":
            return all(f.should_process(event) for f in self.filters)
        else:  # OR
            return any(f.should_process(event) for f in self.filters)


class EventTypeFilter(EventFilter):
    """
    事件类型过滤器
    
    根据事件类型进行过滤。
    """
    
    def __init__(self, allowed_types: set[EventType]):
        """
        初始化类型过滤器
        
        Args:
            allowed_types: 允许的事件类型集合
        """
        self.allowed_types = allowed_types
    
    def should_process(self, event: Event) -> bool:
        """检查事件类型是否在允许列表中"""
        return event.event_type in self.allowed_types


class EventSourceFilter(EventFilter):
    """
    事件源过滤器
    
    根据事件来源进行过滤。
    """
    
    def __init__(self, allowed_sources: set[str]):
        """
        初始化源过滤器
        
        Args:
            allowed_sources: 允许的事件源集合
        """
        self.allowed_sources = allowed_sources
    
    def should_process(self, event: Event) -> bool:
        """检查事件源是否在允许列表中"""
        return event.source in self.allowed_sources if event.source else True
