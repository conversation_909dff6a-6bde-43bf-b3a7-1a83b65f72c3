"""
消息总线

实现事件驱动架构的核心消息传递机制，包括：
- 发布/订阅模式
- 事件路由和分发
- 异步消息处理
- 事件过滤和优先级
"""

import asyncio
import logging
import threading
from collections import defaultdict, deque
from typing import Dict, List, Set, Callable, Optional, Any, Union
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass, field
from datetime import datetime

from .base import Event, EventType, EventHandler, EventFilter, PriorityEvent

logger = logging.getLogger(__name__)


@dataclass
class Subscription:
    """订阅信息"""
    
    handler: Union[EventHandler, Callable[[Event], None]]
    event_types: Set[EventType]
    priority: int = 0
    filters: List[EventFilter] = field(default_factory=list)
    is_async: bool = False
    
    def can_handle(self, event: Event) -> bool:
        """检查是否可以处理指定事件"""
        # 检查事件类型
        if self.event_types and event.event_type not in self.event_types:
            return False
        
        # 检查过滤器
        for filter_obj in self.filters:
            if not filter_obj.should_process(event):
                return False
        
        return True


class MessageBus:
    """
    消息总线
    
    事件驱动架构的核心组件，负责事件的发布、订阅和分发。
    支持同步和异步事件处理，以及事件过滤和优先级。
    """
    
    def __init__(self, max_workers: int = 4, enable_async: bool = True):
        """
        初始化消息总线
        
        Args:
            max_workers: 线程池最大工作线程数
            enable_async: 是否启用异步处理
        """
        self._subscriptions: Dict[str, List[Subscription]] = defaultdict(list)
        self._event_queue: deque = deque()
        self._running = False
        self._lock = threading.RLock()
        self._executor = ThreadPoolExecutor(max_workers=max_workers)
        self._enable_async = enable_async
        self._loop: Optional[asyncio.AbstractEventLoop] = None
        self._stats = {
            'events_published': 0,
            'events_processed': 0,
            'events_failed': 0,
            'handlers_count': 0
        }
        
        logger.info(f"消息总线初始化完成，最大工作线程数: {max_workers}")
    
    def subscribe(
        self,
        handler: Union[EventHandler, Callable[[Event], None]],
        event_types: Union[EventType, List[EventType], None] = None,
        priority: int = 0,
        filters: Optional[List[EventFilter]] = None,
        handler_name: Optional[str] = None
    ) -> str:
        """
        订阅事件
        
        Args:
            handler: 事件处理器或处理函数
            event_types: 要订阅的事件类型，None表示订阅所有类型
            priority: 处理优先级，数值越大优先级越高
            filters: 事件过滤器列表
            handler_name: 处理器名称，用于标识和管理
            
        Returns:
            str: 订阅ID
        """
        with self._lock:
            # 处理事件类型
            if event_types is None:
                types_set = set()  # 空集合表示订阅所有类型
            elif isinstance(event_types, EventType):
                types_set = {event_types}
            else:
                types_set = set(event_types)
            
            # 确定处理器名称
            if handler_name is None:
                if hasattr(handler, 'handler_name'):
                    handler_name = handler.handler_name
                elif hasattr(handler, '__name__'):
                    handler_name = handler.__name__
                else:
                    handler_name = f"handler_{len(self._subscriptions)}"
            
            # 检查是否为异步处理器
            is_async = asyncio.iscoroutinefunction(handler)
            
            # 创建订阅
            subscription = Subscription(
                handler=handler,
                event_types=types_set,
                priority=priority,
                filters=filters or [],
                is_async=is_async
            )
            
            self._subscriptions[handler_name].append(subscription)
            self._stats['handlers_count'] += 1
            
            logger.info(f"订阅成功: {handler_name}, 事件类型: {types_set}, 优先级: {priority}")
            return handler_name
    
    def unsubscribe(self, handler_name: str) -> bool:
        """
        取消订阅
        
        Args:
            handler_name: 处理器名称
            
        Returns:
            bool: 是否成功取消订阅
        """
        with self._lock:
            if handler_name in self._subscriptions:
                count = len(self._subscriptions[handler_name])
                del self._subscriptions[handler_name]
                self._stats['handlers_count'] -= count
                logger.info(f"取消订阅成功: {handler_name}")
                return True
            
            logger.warning(f"取消订阅失败，未找到处理器: {handler_name}")
            return False
    
    def publish(self, event: Event, immediate: bool = False) -> None:
        """
        发布事件
        
        Args:
            event: 要发布的事件
            immediate: 是否立即处理，True表示同步处理，False表示加入队列异步处理
        """
        self._stats['events_published'] += 1
        
        if immediate:
            self._process_event_sync(event)
        else:
            with self._lock:
                self._event_queue.append(event)
            
            logger.debug(f"事件已加入队列: {event}")
    
    def publish_async(self, event: Event) -> asyncio.Task:
        """
        异步发布事件
        
        Args:
            event: 要发布的事件
            
        Returns:
            asyncio.Task: 异步任务
        """
        if not self._enable_async:
            raise RuntimeError("异步处理未启用")
        
        return asyncio.create_task(self._process_event_async(event))
    
    def start(self) -> None:
        """启动消息总线"""
        if self._running:
            logger.warning("消息总线已在运行")
            return
        
        self._running = True
        
        if self._enable_async:
            # 启动异步事件循环
            threading.Thread(target=self._run_async_loop, daemon=True).start()
        
        # 启动同步事件处理线程
        threading.Thread(target=self._process_events, daemon=True).start()
        
        logger.info("消息总线已启动")
    
    def stop(self) -> None:
        """停止消息总线"""
        self._running = False
        
        # 等待队列中的事件处理完成
        while self._event_queue:
            threading.Event().wait(0.1)
        
        self._executor.shutdown(wait=True)
        logger.info("消息总线已停止")
    
    def _process_events(self) -> None:
        """处理事件队列（同步）"""
        while self._running:
            try:
                if self._event_queue:
                    with self._lock:
                        if self._event_queue:
                            event = self._event_queue.popleft()
                        else:
                            continue
                    
                    self._process_event_sync(event)
                else:
                    threading.Event().wait(0.01)  # 短暂休眠
                    
            except Exception as e:
                logger.error(f"事件处理线程异常: {e}")
                self._stats['events_failed'] += 1
    
    def _process_event_sync(self, event: Event) -> None:
        """同步处理单个事件"""
        try:
            handlers = self._get_matching_handlers(event)
            
            # 按优先级排序
            handlers.sort(key=lambda x: x.priority, reverse=True)
            
            for subscription in handlers:
                try:
                    if subscription.is_async:
                        # 异步处理器在线程池中执行
                        if self._loop:
                            asyncio.run_coroutine_threadsafe(
                                subscription.handler(event), self._loop
                            )
                    else:
                        # 同步处理器直接执行
                        if isinstance(subscription.handler, EventHandler):
                            subscription.handler.handle_event(event)
                        else:
                            subscription.handler(event)
                    
                    self._stats['events_processed'] += 1
                    
                except Exception as e:
                    logger.error(f"事件处理器执行失败: {e}")
                    self._stats['events_failed'] += 1
                    
        except Exception as e:
            logger.error(f"事件处理失败: {e}")
            self._stats['events_failed'] += 1
    
    async def _process_event_async(self, event: Event) -> None:
        """异步处理单个事件"""
        try:
            handlers = self._get_matching_handlers(event)
            
            # 按优先级排序
            handlers.sort(key=lambda x: x.priority, reverse=True)
            
            # 并发执行所有处理器
            tasks = []
            for subscription in handlers:
                try:
                    if subscription.is_async:
                        tasks.append(subscription.handler(event))
                    else:
                        # 同步处理器在线程池中执行
                        if isinstance(subscription.handler, EventHandler):
                            task = asyncio.get_event_loop().run_in_executor(
                                self._executor, subscription.handler.handle_event, event
                            )
                        else:
                            task = asyncio.get_event_loop().run_in_executor(
                                self._executor, subscription.handler, event
                            )
                        tasks.append(task)
                        
                except Exception as e:
                    logger.error(f"创建事件处理任务失败: {e}")
                    self._stats['events_failed'] += 1
            
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
                self._stats['events_processed'] += len(tasks)
                
        except Exception as e:
            logger.error(f"异步事件处理失败: {e}")
            self._stats['events_failed'] += 1
    
    def _get_matching_handlers(self, event: Event) -> List[Subscription]:
        """获取匹配的事件处理器"""
        matching_handlers = []
        
        with self._lock:
            for handler_name, subscriptions in self._subscriptions.items():
                for subscription in subscriptions:
                    if subscription.can_handle(event):
                        matching_handlers.append(subscription)
        
        return matching_handlers
    
    def _run_async_loop(self) -> None:
        """运行异步事件循环"""
        try:
            self._loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self._loop)
            self._loop.run_forever()
        except Exception as e:
            logger.error(f"异步事件循环异常: {e}")
        finally:
            if self._loop:
                self._loop.close()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            return {
                **self._stats,
                'queue_size': len(self._event_queue),
                'subscriptions_count': len(self._subscriptions),
                'is_running': self._running
            }
    
    def clear_queue(self) -> int:
        """清空事件队列"""
        with self._lock:
            count = len(self._event_queue)
            self._event_queue.clear()
            logger.info(f"已清空事件队列，清除 {count} 个事件")
            return count
    
    def __enter__(self):
        """上下文管理器入口"""
        self.start()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.stop()


# 全局消息总线实例
message_bus = MessageBus()
