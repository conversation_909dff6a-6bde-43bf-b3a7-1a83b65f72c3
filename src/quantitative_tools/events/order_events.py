"""
订单事件

定义订单相关的事件类型，包括：
- 基础订单事件
- 订单提交事件
- 订单成交事件
- 订单取消事件
"""

from datetime import datetime
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from decimal import Decimal
from enum import Enum

from .base import Event, EventType


class OrderSide(Enum):
    """订单方向枚举"""
    
    BUY = "BUY"
    SELL = "SELL"


class OrderType(Enum):
    """订单类型枚举"""
    
    MARKET = "MARKET"  # 市价单
    LIMIT = "LIMIT"    # 限价单
    STOP = "STOP"      # 止损单
    STOP_LIMIT = "STOP_LIMIT"  # 止损限价单


class OrderStatus(Enum):
    """订单状态枚举"""
    
    PENDING = "PENDING"        # 待提交
    SUBMITTED = "SUBMITTED"    # 已提交
    ACCEPTED = "ACCEPTED"      # 已接受
    REJECTED = "REJECTED"      # 已拒绝
    PARTIALLY_FILLED = "PARTIALLY_FILLED"  # 部分成交
    FILLED = "FILLED"          # 完全成交
    CANCELLED = "CANCELLED"    # 已取消
    EXPIRED = "EXPIRED"        # 已过期


class TimeInForce(Enum):
    """订单有效期枚举"""
    
    DAY = "DAY"        # 当日有效
    GTC = "GTC"        # 撤销前有效
    IOC = "IOC"        # 立即成交或取消
    FOK = "FOK"        # 全部成交或取消


@dataclass
class OrderEvent(Event):
    """
    基础订单事件
    
    所有订单相关事件的基础类，包含订单的基本属性。
    """
    
    order_id: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: int
    price: Optional[Decimal] = None
    stop_price: Optional[Decimal] = None
    time_in_force: TimeInForce = TimeInForce.DAY
    status: OrderStatus = OrderStatus.PENDING
    strategy_id: Optional[str] = None
    event_type: EventType = EventType.ORDER_SUBMITTED
    
    def __post_init__(self):
        """验证订单数据的有效性"""
        super().__post_init__()
        
        if self.quantity <= 0:
            raise ValueError("订单数量必须大于0")
        
        if self.order_type == OrderType.LIMIT and self.price is None:
            raise ValueError("限价单必须指定价格")
        
        if self.order_type in [OrderType.STOP, OrderType.STOP_LIMIT] and self.stop_price is None:
            raise ValueError("止损单必须指定止损价格")
        
        if self.price is not None and self.price <= 0:
            raise ValueError("订单价格必须大于0")
        
        if self.stop_price is not None and self.stop_price <= 0:
            raise ValueError("止损价格必须大于0")
    
    @property
    def is_buy_order(self) -> bool:
        """是否为买单"""
        return self.side == OrderSide.BUY
    
    @property
    def is_sell_order(self) -> bool:
        """是否为卖单"""
        return self.side == OrderSide.SELL
    
    @property
    def is_market_order(self) -> bool:
        """是否为市价单"""
        return self.order_type == OrderType.MARKET
    
    @property
    def is_limit_order(self) -> bool:
        """是否为限价单"""
        return self.order_type == OrderType.LIMIT
    
    @property
    def is_active(self) -> bool:
        """订单是否处于活跃状态"""
        return self.status in [
            OrderStatus.PENDING,
            OrderStatus.SUBMITTED,
            OrderStatus.ACCEPTED,
            OrderStatus.PARTIALLY_FILLED
        ]
    
    @property
    def is_completed(self) -> bool:
        """订单是否已完成"""
        return self.status in [
            OrderStatus.FILLED,
            OrderStatus.CANCELLED,
            OrderStatus.REJECTED,
            OrderStatus.EXPIRED
        ]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'event_id': self.event_id,
            'event_type': self.event_type.value,
            'timestamp': self.timestamp.isoformat(),
            'source': self.source,
            'metadata': self.metadata,
            'order_id': self.order_id,
            'symbol': self.symbol,
            'side': self.side.value,
            'order_type': self.order_type.value,
            'quantity': self.quantity,
            'price': float(self.price) if self.price else None,
            'stop_price': float(self.stop_price) if self.stop_price else None,
            'time_in_force': self.time_in_force.value,
            'status': self.status.value,
            'strategy_id': self.strategy_id
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'OrderEvent':
        """从字典创建事件实例"""
        return cls(
            event_id=data['event_id'],
            timestamp=datetime.fromisoformat(data['timestamp']),
            source=data.get('source'),
            metadata=data.get('metadata', {}),
            order_id=data['order_id'],
            symbol=data['symbol'],
            side=OrderSide(data['side']),
            order_type=OrderType(data['order_type']),
            quantity=data['quantity'],
            price=Decimal(str(data['price'])) if data.get('price') else None,
            stop_price=Decimal(str(data['stop_price'])) if data.get('stop_price') else None,
            time_in_force=TimeInForce(data['time_in_force']),
            status=OrderStatus(data['status']),
            strategy_id=data.get('strategy_id')
        )


@dataclass
class OrderSubmittedEvent(OrderEvent):
    """
    订单提交事件
    
    当订单被提交到交易所时触发的事件。
    """
    
    exchange: str = ""
    commission_rate: float = 0.0
    event_type: EventType = EventType.ORDER_SUBMITTED
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        base_dict = super().to_dict()
        base_dict.update({
            'exchange': self.exchange,
            'commission_rate': self.commission_rate
        })
        return base_dict
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'OrderSubmittedEvent':
        """从字典创建事件实例"""
        return cls(
            event_id=data['event_id'],
            timestamp=datetime.fromisoformat(data['timestamp']),
            source=data.get('source'),
            metadata=data.get('metadata', {}),
            order_id=data['order_id'],
            symbol=data['symbol'],
            side=OrderSide(data['side']),
            order_type=OrderType(data['order_type']),
            quantity=data['quantity'],
            price=Decimal(str(data['price'])) if data.get('price') else None,
            stop_price=Decimal(str(data['stop_price'])) if data.get('stop_price') else None,
            time_in_force=TimeInForce(data['time_in_force']),
            status=OrderStatus(data['status']),
            strategy_id=data.get('strategy_id'),
            exchange=data.get('exchange', ''),
            commission_rate=data.get('commission_rate', 0.0)
        )


@dataclass
class OrderFilledEvent(OrderEvent):
    """
    订单成交事件
    
    当订单完全或部分成交时触发的事件。
    """
    
    fill_price: Decimal
    fill_quantity: int
    fill_time: datetime
    commission: Decimal = Decimal('0')
    exchange: str = ""
    trade_id: Optional[str] = None
    event_type: EventType = EventType.ORDER_FILLED
    
    def __post_init__(self):
        """验证成交数据的有效性"""
        super().__post_init__()
        
        if self.fill_quantity <= 0:
            raise ValueError("成交数量必须大于0")
        
        if self.fill_quantity > self.quantity:
            raise ValueError("成交数量不能超过订单数量")
        
        if self.fill_price <= 0:
            raise ValueError("成交价格必须大于0")
        
        if self.commission < 0:
            raise ValueError("手续费不能为负数")
    
    @property
    def fill_value(self) -> Decimal:
        """成交金额"""
        return self.fill_price * self.fill_quantity
    
    @property
    def net_value(self) -> Decimal:
        """净成交金额（扣除手续费）"""
        if self.side == OrderSide.BUY:
            return self.fill_value + self.commission
        else:
            return self.fill_value - self.commission
    
    @property
    def is_partial_fill(self) -> bool:
        """是否为部分成交"""
        return self.fill_quantity < self.quantity
    
    @property
    def remaining_quantity(self) -> int:
        """剩余数量"""
        return self.quantity - self.fill_quantity
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        base_dict = super().to_dict()
        base_dict.update({
            'fill_price': float(self.fill_price),
            'fill_quantity': self.fill_quantity,
            'fill_time': self.fill_time.isoformat(),
            'commission': float(self.commission),
            'exchange': self.exchange,
            'trade_id': self.trade_id
        })
        return base_dict
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'OrderFilledEvent':
        """从字典创建事件实例"""
        return cls(
            event_id=data['event_id'],
            timestamp=datetime.fromisoformat(data['timestamp']),
            source=data.get('source'),
            metadata=data.get('metadata', {}),
            order_id=data['order_id'],
            symbol=data['symbol'],
            side=OrderSide(data['side']),
            order_type=OrderType(data['order_type']),
            quantity=data['quantity'],
            price=Decimal(str(data['price'])) if data.get('price') else None,
            stop_price=Decimal(str(data['stop_price'])) if data.get('stop_price') else None,
            time_in_force=TimeInForce(data['time_in_force']),
            status=OrderStatus(data['status']),
            strategy_id=data.get('strategy_id'),
            fill_price=Decimal(str(data['fill_price'])),
            fill_quantity=data['fill_quantity'],
            fill_time=datetime.fromisoformat(data['fill_time']),
            commission=Decimal(str(data['commission'])),
            exchange=data.get('exchange', ''),
            trade_id=data.get('trade_id')
        )


@dataclass
class OrderCancelledEvent(OrderEvent):
    """
    订单取消事件
    
    当订单被取消时触发的事件。
    """
    
    cancel_reason: str = ""
    cancelled_quantity: int = 0
    event_type: EventType = EventType.ORDER_CANCELLED
    
    def __post_init__(self):
        """验证取消数据的有效性"""
        super().__post_init__()
        
        if self.cancelled_quantity < 0:
            raise ValueError("取消数量不能为负数")
        
        if self.cancelled_quantity > self.quantity:
            raise ValueError("取消数量不能超过订单数量")
    
    @property
    def is_full_cancel(self) -> bool:
        """是否为完全取消"""
        return self.cancelled_quantity == self.quantity
    
    @property
    def is_partial_cancel(self) -> bool:
        """是否为部分取消"""
        return 0 < self.cancelled_quantity < self.quantity
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        base_dict = super().to_dict()
        base_dict.update({
            'cancel_reason': self.cancel_reason,
            'cancelled_quantity': self.cancelled_quantity
        })
        return base_dict
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'OrderCancelledEvent':
        """从字典创建事件实例"""
        return cls(
            event_id=data['event_id'],
            timestamp=datetime.fromisoformat(data['timestamp']),
            source=data.get('source'),
            metadata=data.get('metadata', {}),
            order_id=data['order_id'],
            symbol=data['symbol'],
            side=OrderSide(data['side']),
            order_type=OrderType(data['order_type']),
            quantity=data['quantity'],
            price=Decimal(str(data['price'])) if data.get('price') else None,
            stop_price=Decimal(str(data['stop_price'])) if data.get('stop_price') else None,
            time_in_force=TimeInForce(data['time_in_force']),
            status=OrderStatus(data['status']),
            strategy_id=data.get('strategy_id'),
            cancel_reason=data.get('cancel_reason', ''),
            cancelled_quantity=data.get('cancelled_quantity', 0)
        )
