"""
事件系统单元测试

测试事件驱动架构的核心组件，包括：
- 事件基类和具体事件类型
- 消息总线和事件处理
- 事件队列和事件循环
"""

import pytest
import asyncio
import threading
import time
from datetime import datetime
from decimal import Decimal
from unittest.mock import Mock, patch

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.quantitative_tools.events.base import Event, EventType, EventHandler
from src.quantitative_tools.events.market_events import BarEvent, TickEvent
from src.quantitative_tools.events.signal_events import StrategySignal, SignalType, SignalStrength
from src.quantitative_tools.events.order_events import OrderSubmittedEvent, OrderSide, OrderType
from src.quantitative_tools.events.message_bus import MessageBus
from src.quantitative_tools.events.event_queue import EventQueue, EventLoop


class TestEvent:
    """测试事件基类"""
    
    def test_bar_event_creation(self):
        """测试K线事件创建"""
        bar_event = BarEvent(
            symbol="000001.SZ",
            timestamp=datetime.now(),
            source="TestDataProvider",
            open_price=Decimal('10.0'),
            high_price=Decimal('10.5'),
            low_price=Decimal('9.8'),
            close_price=Decimal('10.2'),
            volume=1000000,
            timeframe="1D"
        )
        
        assert bar_event.symbol == "000001.SZ"
        assert bar_event.open_price == Decimal('10.0')
        assert bar_event.volume == 1000000
        assert bar_event.event_type == EventType.BAR_DATA
        
        # 测试计算属性
        expected_typical_price = (Decimal('10.5') + Decimal('9.8') + Decimal('10.2')) / 3
        assert bar_event.typical_price == expected_typical_price
        
        expected_change_pct = (Decimal('10.2') - Decimal('10.0')) / Decimal('10.0') * 100
        assert bar_event.price_change_pct == expected_change_pct
    
    def test_bar_event_validation(self):
        """测试K线事件数据验证"""
        # 测试无效价格数据
        with pytest.raises(ValueError):
            BarEvent(
                symbol="000001.SZ",
                open_price=Decimal('10.0'),
                high_price=Decimal('9.5'),  # 最高价低于开盘价
                low_price=Decimal('9.8'),
                close_price=Decimal('10.2'),
                volume=1000000
            )
        
        # 测试负成交量
        with pytest.raises(ValueError):
            BarEvent(
                symbol="000001.SZ",
                open_price=Decimal('10.0'),
                high_price=Decimal('10.5'),
                low_price=Decimal('9.8'),
                close_price=Decimal('10.2'),
                volume=-1000  # 负成交量
            )
    
    def test_tick_event_creation(self):
        """测试Tick事件创建"""
        tick_event = TickEvent(
            symbol="000001.SZ",
            timestamp=datetime.now(),
            source="TestDataProvider",
            price=Decimal('10.15'),
            size=1000,
            tick_type="TRADE",
            bid_price=Decimal('10.14'),
            ask_price=Decimal('10.16'),
            bid_size=500,
            ask_size=800
        )
        
        assert tick_event.symbol == "000001.SZ"
        assert tick_event.price == Decimal('10.15')
        assert tick_event.size == 1000
        assert tick_event.event_type == EventType.TICK_DATA
        
        # 测试计算属性
        assert tick_event.spread == Decimal('0.02')
        assert tick_event.mid_price == Decimal('10.15')
    
    def test_strategy_signal_creation(self):
        """测试策略信号事件创建"""
        signal = StrategySignal(
            symbol="000001.SZ",
            signal_type=SignalType.BUY,
            strength=SignalStrength.STRONG,
            confidence=0.85,
            target_price=Decimal('10.50'),
            stop_loss=Decimal('9.80'),
            take_profit=Decimal('11.00'),
            quantity=1000,
            strategy_id="test_strategy_001",
            strategy_name="测试策略",
            reasoning="技术指标显示买入信号"
        )
        
        assert signal.symbol == "000001.SZ"
        assert signal.signal_type == SignalType.BUY
        assert signal.confidence == 0.85
        assert signal.is_entry_signal == True
        assert signal.is_exit_signal == False
        
        # 测试添加技术指标
        signal.add_indicator("RSI", 25.5)
        signal.add_indicator("MACD", 0.15)
        
        assert signal.get_indicator("RSI") == 25.5
        assert signal.get_indicator("MACD") == 0.15
        assert signal.get_indicator("不存在的指标") is None
    
    def test_order_event_creation(self):
        """测试订单事件创建"""
        order_event = OrderSubmittedEvent(
            order_id="order_001",
            symbol="000001.SZ",
            side=OrderSide.BUY,
            order_type=OrderType.LIMIT,
            quantity=1000,
            price=Decimal('10.15'),
            exchange="SZSE",
            commission_rate=0.0003
        )
        
        assert order_event.order_id == "order_001"
        assert order_event.symbol == "000001.SZ"
        assert order_event.side == OrderSide.BUY
        assert order_event.is_buy_order == True
        assert order_event.is_limit_order == True
        assert order_event.event_type == EventType.ORDER_SUBMITTED
    
    def test_event_serialization(self):
        """测试事件序列化和反序列化"""
        original_event = BarEvent(
            symbol="000001.SZ",
            open_price=Decimal('10.0'),
            high_price=Decimal('10.5'),
            low_price=Decimal('9.8'),
            close_price=Decimal('10.2'),
            volume=1000000
        )
        
        # 序列化
        event_dict = original_event.to_dict()
        assert isinstance(event_dict, dict)
        assert event_dict['symbol'] == "000001.SZ"
        assert event_dict['volume'] == 1000000
        
        # 反序列化
        restored_event = BarEvent.from_dict(event_dict)
        assert restored_event.symbol == original_event.symbol
        assert restored_event.open_price == original_event.open_price
        assert restored_event.volume == original_event.volume


class MockEventHandler(EventHandler):
    """模拟事件处理器"""
    
    def __init__(self, name: str):
        self.name = name
        self.handled_events = []
        self.handle_count = 0
    
    def handle_event(self, event: Event) -> None:
        """处理事件"""
        self.handled_events.append(event)
        self.handle_count += 1
    
    def can_handle(self, event_type: EventType) -> bool:
        """检查是否可以处理指定类型的事件"""
        return True
    
    @property
    def handler_name(self) -> str:
        """处理器名称"""
        return self.name


class TestMessageBus:
    """测试消息总线"""
    
    def setup_method(self):
        """测试前设置"""
        self.message_bus = MessageBus(max_workers=2, enable_async=False)
        self.handler1 = MockEventHandler("handler1")
        self.handler2 = MockEventHandler("handler2")
    
    def teardown_method(self):
        """测试后清理"""
        if hasattr(self, 'message_bus'):
            self.message_bus.stop()
    
    def test_subscribe_and_publish(self):
        """测试订阅和发布"""
        # 订阅事件
        subscription_id = self.message_bus.subscribe(
            handler=self.handler1,
            event_types=[EventType.BAR_DATA],
            handler_name="test_handler"
        )
        
        assert subscription_id == "test_handler"
        
        # 启动消息总线
        self.message_bus.start()
        
        # 发布事件
        event = BarEvent(
            symbol="000001.SZ",
            open_price=Decimal('10.0'),
            high_price=Decimal('10.5'),
            low_price=Decimal('9.8'),
            close_price=Decimal('10.2'),
            volume=1000000
        )
        
        self.message_bus.publish(event, immediate=True)
        
        # 验证事件被处理
        assert self.handler1.handle_count == 1
        assert len(self.handler1.handled_events) == 1
        assert self.handler1.handled_events[0].symbol == "000001.SZ"
    
    def test_multiple_handlers(self):
        """测试多个处理器"""
        # 订阅相同事件类型
        self.message_bus.subscribe(self.handler1, [EventType.BAR_DATA])
        self.message_bus.subscribe(self.handler2, [EventType.BAR_DATA])
        
        self.message_bus.start()
        
        # 发布事件
        event = BarEvent(
            symbol="000001.SZ",
            open_price=Decimal('10.0'),
            high_price=Decimal('10.5'),
            low_price=Decimal('9.8'),
            close_price=Decimal('10.2'),
            volume=1000000
        )
        
        self.message_bus.publish(event, immediate=True)
        
        # 验证两个处理器都收到事件
        assert self.handler1.handle_count == 1
        assert self.handler2.handle_count == 1
    
    def test_event_filtering(self):
        """测试事件过滤"""
        from src.quantitative_tools.events.base import EventTypeFilter
        
        # 创建过滤器，只允许BAR_DATA事件
        filter_obj = EventTypeFilter({EventType.BAR_DATA})
        
        # 订阅时添加过滤器
        self.message_bus.subscribe(
            handler=self.handler1,
            event_types=None,  # 订阅所有类型
            filters=[filter_obj]
        )
        
        self.message_bus.start()
        
        # 发布BAR_DATA事件
        bar_event = BarEvent(
            symbol="000001.SZ",
            open_price=Decimal('10.0'),
            high_price=Decimal('10.5'),
            low_price=Decimal('9.8'),
            close_price=Decimal('10.2'),
            volume=1000000
        )
        self.message_bus.publish(bar_event, immediate=True)
        
        # 发布TICK_DATA事件
        tick_event = TickEvent(
            symbol="000001.SZ",
            price=Decimal('10.15'),
            size=1000
        )
        self.message_bus.publish(tick_event, immediate=True)
        
        # 验证只有BAR_DATA事件被处理
        assert self.handler1.handle_count == 1
        assert isinstance(self.handler1.handled_events[0], BarEvent)
    
    def test_unsubscribe(self):
        """测试取消订阅"""
        # 订阅事件
        subscription_id = self.message_bus.subscribe(
            handler=self.handler1,
            event_types=[EventType.BAR_DATA],
            handler_name="test_handler"
        )
        
        self.message_bus.start()
        
        # 发布事件
        event = BarEvent(
            symbol="000001.SZ",
            open_price=Decimal('10.0'),
            high_price=Decimal('10.5'),
            low_price=Decimal('9.8'),
            close_price=Decimal('10.2'),
            volume=1000000
        )
        
        self.message_bus.publish(event, immediate=True)
        assert self.handler1.handle_count == 1
        
        # 取消订阅
        success = self.message_bus.unsubscribe(subscription_id)
        assert success == True
        
        # 再次发布事件
        self.message_bus.publish(event, immediate=True)
        
        # 验证事件不再被处理
        assert self.handler1.handle_count == 1  # 计数没有增加
    
    def test_message_bus_stats(self):
        """测试消息总线统计"""
        self.message_bus.subscribe(self.handler1, [EventType.BAR_DATA])
        self.message_bus.start()
        
        # 发布几个事件
        for i in range(3):
            event = BarEvent(
                symbol=f"00000{i}.SZ",
                open_price=Decimal('10.0'),
                high_price=Decimal('10.5'),
                low_price=Decimal('9.8'),
                close_price=Decimal('10.2'),
                volume=1000000
            )
            self.message_bus.publish(event, immediate=True)
        
        # 获取统计信息
        stats = self.message_bus.get_stats()
        
        assert stats['events_published'] == 3
        assert stats['events_processed'] >= 3
        assert stats['subscriptions_count'] >= 1
        assert stats['is_running'] == True


class TestEventQueue:
    """测试事件队列"""
    
    def setup_method(self):
        """测试前设置"""
        self.event_queue = EventQueue(max_size=100, enable_priority=True)
    
    def test_put_and_get(self):
        """测试事件入队和出队"""
        event = BarEvent(
            symbol="000001.SZ",
            open_price=Decimal('10.0'),
            high_price=Decimal('10.5'),
            low_price=Decimal('9.8'),
            close_price=Decimal('10.2'),
            volume=1000000
        )
        
        # 入队
        success = self.event_queue.put(event, priority=1)
        assert success == True
        assert self.event_queue.size() == 1
        
        # 出队
        queued_event = self.event_queue.get()
        assert queued_event is not None
        assert queued_event.event.symbol == "000001.SZ"
        assert queued_event.priority == 1
        assert self.event_queue.size() == 0
    
    def test_priority_ordering(self):
        """测试优先级排序"""
        # 添加不同优先级的事件
        events = []
        for i, priority in enumerate([1, 3, 2]):
            event = BarEvent(
                symbol=f"00000{i}.SZ",
                open_price=Decimal('10.0'),
                high_price=Decimal('10.5'),
                low_price=Decimal('9.8'),
                close_price=Decimal('10.2'),
                volume=1000000
            )
            events.append(event)
            self.event_queue.put(event, priority=priority)
        
        # 验证按优先级顺序出队（高优先级先出）
        queued_event1 = self.event_queue.get()
        assert queued_event1.priority == 3
        assert queued_event1.event.symbol == "000001.SZ"
        
        queued_event2 = self.event_queue.get()
        assert queued_event2.priority == 2
        assert queued_event2.event.symbol == "000002.SZ"
        
        queued_event3 = self.event_queue.get()
        assert queued_event3.priority == 1
        assert queued_event3.event.symbol == "000000.SZ"
    
    def test_batch_operations(self):
        """测试批量操作"""
        # 批量添加事件
        events = []
        for i in range(5):
            event = BarEvent(
                symbol=f"00000{i}.SZ",
                open_price=Decimal('10.0'),
                high_price=Decimal('10.5'),
                low_price=Decimal('9.8'),
                close_price=Decimal('10.2'),
                volume=1000000
            )
            events.append(event)
            self.event_queue.put(event)
        
        assert self.event_queue.size() == 5
        
        # 批量获取事件
        batch = self.event_queue.get_batch(batch_size=3)
        assert len(batch) == 3
        assert self.event_queue.size() == 2
        
        # 获取剩余事件
        remaining = self.event_queue.get_batch(batch_size=10)
        assert len(remaining) == 2
        assert self.event_queue.size() == 0
    
    def test_queue_limits(self):
        """测试队列容量限制"""
        small_queue = EventQueue(max_size=2, enable_priority=False)
        
        # 添加事件直到队列满
        event1 = BarEvent(symbol="000001.SZ", open_price=Decimal('10.0'), 
                         high_price=Decimal('10.5'), low_price=Decimal('9.8'), 
                         close_price=Decimal('10.2'), volume=1000000)
        event2 = BarEvent(symbol="000002.SZ", open_price=Decimal('10.0'), 
                         high_price=Decimal('10.5'), low_price=Decimal('9.8'), 
                         close_price=Decimal('10.2'), volume=1000000)
        event3 = BarEvent(symbol="000003.SZ", open_price=Decimal('10.0'), 
                         high_price=Decimal('10.5'), low_price=Decimal('9.8'), 
                         close_price=Decimal('10.2'), volume=1000000)
        
        assert small_queue.put(event1) == True
        assert small_queue.put(event2) == True
        assert small_queue.is_full() == True
        
        # 队列满时添加事件应该失败
        assert small_queue.put(event3) == False
        assert small_queue.size() == 2


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
