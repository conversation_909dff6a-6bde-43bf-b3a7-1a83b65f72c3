"""
引擎模块单元测试

测试事件驱动回测系统的各个引擎，包括：
- 数据引擎测试
- 策略引擎测试
- 执行引擎测试
- 风险引擎测试
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from decimal import Decimal
from unittest.mock import Mock, patch, MagicMock

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.quantitative_tools.engines.data_engine import (
    DataEngine, HistoricalDataProvider, DataConfig
)
from src.quantitative_tools.engines.strategy_engine import (
    StrategyEngine, MovingAverageCrossoverStrategy, StrategyConfig
)
from src.quantitative_tools.engines.execution_engine import (
    ExecutionEngine, Portfolio, OrderManager
)
from src.quantitative_tools.engines.risk_engine import (
    RiskEngine, RiskLimits, PositionSizeRule, DrawdownRule
)
from src.quantitative_tools.events.message_bus import MessageBus
from src.quantitative_tools.events.market_events import BarEvent
from src.quantitative_tools.events.signal_events import StrategySignal, SignalType
from src.quantitative_tools.events.order_events import OrderSide, OrderType


class TestHistoricalDataProvider:
    """测试历史数据提供者"""
    
    def setup_method(self):
        """测试前设置"""
        self.data_provider = HistoricalDataProvider(data_path="test_data/")
    
    def test_generate_mock_data(self):
        """测试生成模拟数据"""
        start_date = datetime(2023, 1, 1)
        end_date = datetime(2023, 1, 31)
        
        data = self.data_provider._generate_mock_data(
            symbol="TEST001",
            start_date=start_date,
            end_date=end_date,
            timeframe="1D"
        )
        
        assert isinstance(data, pd.DataFrame)
        assert not data.empty
        assert all(col in data.columns for col in ['open', 'high', 'low', 'close', 'volume'])
        
        # 验证价格数据的合理性
        assert (data['high'] >= data['low']).all()
        assert (data['high'] >= data['open']).all()
        assert (data['high'] >= data['close']).all()
        assert (data['low'] <= data['open']).all()
        assert (data['low'] <= data['close']).all()
        assert (data['volume'] >= 0).all()
    
    def test_validate_data(self):
        """测试数据验证"""
        # 创建有效数据
        valid_data = pd.DataFrame({
            'open': [10.0, 10.2, 10.1],
            'high': [10.5, 10.6, 10.4],
            'low': [9.8, 9.9, 9.7],
            'close': [10.2, 10.1, 10.0],
            'volume': [1000, 1200, 800]
        })
        
        assert self.data_provider.validate_data(valid_data) == True
        
        # 创建无效数据（高价低于低价）
        invalid_data = pd.DataFrame({
            'open': [10.0, 10.2, 10.1],
            'high': [9.5, 10.6, 10.4],  # 第一行高价低于开盘价
            'low': [9.8, 9.9, 9.7],
            'close': [10.2, 10.1, 10.0],
            'volume': [1000, 1200, 800]
        })
        
        assert self.data_provider.validate_data(invalid_data) == False
    
    def test_clean_data(self):
        """测试数据清洗"""
        # 创建包含无效数据的DataFrame
        dirty_data = pd.DataFrame({
            'open': [10.0, 10.2, np.nan, 10.1],
            'high': [10.5, 9.0, 10.4, 10.4],  # 第二行高价低于开盘价
            'low': [9.8, 9.9, 9.7, 9.7],
            'close': [10.2, 10.1, 10.0, 10.0],
            'volume': [1000, 1200, 800, -100]  # 最后一行负成交量
        })
        
        cleaned_data = self.data_provider.clean_data(dirty_data)
        
        # 验证清洗后的数据
        assert len(cleaned_data) < len(dirty_data)  # 无效行被移除
        assert not cleaned_data.isnull().any().any()  # 没有缺失值
        assert (cleaned_data['volume'] >= 0).all()  # 没有负成交量


class TestDataEngine:
    """测试数据引擎"""
    
    def setup_method(self):
        """测试前设置"""
        self.message_bus = MessageBus(max_workers=1, enable_async=False)
        self.data_provider = HistoricalDataProvider()
        
        self.config = DataConfig(
            symbols=["TEST001", "TEST002"],
            start_date=datetime(2023, 1, 1),
            end_date=datetime(2023, 1, 31),
            timeframe="1D"
        )
        
        self.data_engine = DataEngine(
            message_bus=self.message_bus,
            data_provider=self.data_provider,
            config=self.config
        )
    
    def teardown_method(self):
        """测试后清理"""
        if hasattr(self, 'data_engine'):
            self.data_engine.stop_replay()
        if hasattr(self, 'message_bus'):
            self.message_bus.stop()
    
    def test_data_engine_initialization(self):
        """测试数据引擎初始化"""
        assert self.data_engine.config == self.config
        assert self.data_engine.data_provider == self.data_provider
        assert len(self.data_engine._data_cache) >= 0
    
    def test_get_current_data(self):
        """测试获取当前数据"""
        # 模拟设置当前时间
        test_time = datetime(2023, 1, 15)
        self.data_engine._current_time = test_time
        
        # 由于使用模拟数据，这里主要测试方法调用
        current_data = self.data_engine.get_current_data("TEST001")
        # 如果没有数据，应该返回None
        assert current_data is None or isinstance(current_data, pd.Series)


class TestMovingAverageCrossoverStrategy:
    """测试移动平均线交叉策略"""
    
    def setup_method(self):
        """测试前设置"""
        self.strategy_config = StrategyConfig(
            strategy_id="test_ma_strategy",
            strategy_name="测试移动平均线策略",
            symbols=["TEST001"],
            parameters={
                'short_window': 5,
                'long_window': 10
            }
        )
        
        self.strategy = MovingAverageCrossoverStrategy(self.strategy_config)
    
    def test_strategy_initialization(self):
        """测试策略初始化"""
        assert self.strategy.config.strategy_id == "test_ma_strategy"
        assert self.strategy.short_window == 5
        assert self.strategy.long_window == 10
        assert self.strategy.is_active == False
    
    def test_strategy_start_stop(self):
        """测试策略启动和停止"""
        assert self.strategy.is_active == False
        
        self.strategy.start()
        assert self.strategy.is_active == True
        
        self.strategy.stop()
        assert self.strategy.is_active == False
    
    def test_on_bar_data_insufficient_data(self):
        """测试数据不足时的处理"""
        self.strategy.start()
        
        # 创建测试事件
        bar_event = BarEvent(
            symbol="TEST001",
            open_price=Decimal('10.0'),
            high_price=Decimal('10.5'),
            low_price=Decimal('9.8'),
            close_price=Decimal('10.2'),
            volume=1000000
        )
        
        # 数据不足时应该返回None
        signal = self.strategy.on_bar_data(bar_event)
        assert signal is None
    
    def test_on_bar_data_with_sufficient_data(self):
        """测试有足够数据时的信号生成"""
        self.strategy.start()
        
        # 添加足够的价格历史数据
        prices = [10.0, 10.1, 10.2, 10.3, 10.4, 10.5, 10.6, 10.7, 10.8, 10.9, 11.0, 11.1]
        self.strategy._price_history["TEST001"] = prices
        
        # 创建测试事件
        bar_event = BarEvent(
            symbol="TEST001",
            open_price=Decimal('11.0'),
            high_price=Decimal('11.2'),
            low_price=Decimal('10.9'),
            close_price=Decimal('11.1'),
            volume=1000000
        )
        
        # 应该生成买入信号（价格上涨趋势）
        signal = self.strategy.on_bar_data(bar_event)
        
        if signal:  # 如果生成了信号
            assert isinstance(signal, StrategySignal)
            assert signal.symbol == "TEST001"
            assert signal.strategy_id == "test_ma_strategy"


class TestPortfolio:
    """测试投资组合"""
    
    def setup_method(self):
        """测试前设置"""
        self.portfolio = Portfolio(initial_capital=Decimal('1000000'))
    
    def test_portfolio_initialization(self):
        """测试投资组合初始化"""
        assert self.portfolio.initial_capital == Decimal('1000000')
        assert self.portfolio.cash == Decimal('1000000')
        assert len(self.portfolio.positions) == 0
        assert len(self.portfolio.trades_history) == 0
    
    def test_update_position_new_long(self):
        """测试新建多头仓位"""
        position = self.portfolio.update_position(
            symbol="TEST001",
            side=OrderSide.BUY,
            quantity=1000,
            price=Decimal('10.0'),
            commission=Decimal('3.0')
        )
        
        assert position.symbol == "TEST001"
        assert position.quantity == 1000
        assert position.avg_price == Decimal('10.0')
        assert position.commission == Decimal('3.0')
        
        # 检查现金变化
        expected_cash = Decimal('1000000') - Decimal('10.0') * 1000 - Decimal('3.0')
        assert self.portfolio.cash == expected_cash
    
    def test_update_position_add_to_long(self):
        """测试增加多头仓位"""
        # 先建立初始仓位
        self.portfolio.update_position("TEST001", OrderSide.BUY, 1000, Decimal('10.0'))
        
        # 增加仓位
        position = self.portfolio.update_position("TEST001", OrderSide.BUY, 500, Decimal('11.0'))
        
        # 验证平均价格计算
        expected_avg_price = (Decimal('10.0') * 1000 + Decimal('11.0') * 500) / 1500
        assert position.quantity == 1500
        assert position.avg_price == expected_avg_price
    
    def test_get_total_value(self):
        """测试获取总资产价值"""
        # 建立仓位
        self.portfolio.update_position("TEST001", OrderSide.BUY, 1000, Decimal('10.0'))
        
        # 更新市场价格
        self.portfolio.update_market_prices({"TEST001": Decimal('11.0')})
        
        # 计算总价值
        total_value = self.portfolio.get_total_value()
        expected_value = self.portfolio.cash + Decimal('11.0') * 1000
        assert total_value == expected_value
    
    def test_get_performance_stats(self):
        """测试获取绩效统计"""
        # 建立仓位
        self.portfolio.update_position("TEST001", OrderSide.BUY, 1000, Decimal('10.0'))
        
        stats = self.portfolio.get_performance_stats()
        
        assert 'initial_capital' in stats
        assert 'current_cash' in stats
        assert 'total_value' in stats
        assert 'total_trades' in stats
        assert stats['initial_capital'] == float(self.portfolio.initial_capital)
        assert stats['total_trades'] == 1


class TestRiskEngine:
    """测试风险引擎"""
    
    def setup_method(self):
        """测试前设置"""
        self.message_bus = MessageBus(max_workers=1, enable_async=False)
        self.risk_limits = RiskLimits(
            max_position_size=1000,
            max_daily_loss=Decimal('10000'),
            max_drawdown=Decimal('0.1')
        )
        self.risk_engine = RiskEngine(
            message_bus=self.message_bus,
            risk_limits=self.risk_limits
        )
    
    def teardown_method(self):
        """测试后清理"""
        if hasattr(self, 'message_bus'):
            self.message_bus.stop()
    
    def test_risk_engine_initialization(self):
        """测试风险引擎初始化"""
        assert self.risk_engine.risk_manager.risk_limits == self.risk_limits
        assert len(self.risk_engine.risk_manager.rules) > 0
    
    def test_position_size_rule(self):
        """测试仓位大小规则"""
        rule = PositionSizeRule(max_position_size=500)
        
        # 创建超过限制的策略信号
        signal = StrategySignal(
            symbol="TEST001",
            signal_type=SignalType.BUY,
            quantity=1000,  # 超过500的限制
            strategy_id="test_strategy",
            strategy_name="测试策略"
        )
        
        context = {'portfolio_id': 'test'}
        risk_event = rule.check_risk(signal, context)
        
        assert risk_event is not None
        assert risk_event.risk_type == 'position_size'
        assert risk_event.current_value == Decimal('1000')
        assert risk_event.threshold_value == Decimal('500')
    
    def test_drawdown_rule(self):
        """测试回撤规则"""
        rule = DrawdownRule(max_drawdown=Decimal('0.1'))
        
        # 创建触发回撤限制的上下文
        context = {
            'portfolio_id': 'test',
            'portfolio_value': Decimal('80000'),  # 当前价值
            'peak_portfolio_value': Decimal('100000')  # 峰值价值，回撤20%
        }
        
        # 创建任意事件
        signal = StrategySignal(
            symbol="TEST001",
            signal_type=SignalType.BUY,
            strategy_id="test_strategy",
            strategy_name="测试策略"
        )
        
        risk_event = rule.check_risk(signal, context)
        
        assert risk_event is not None
        assert risk_event.risk_type == 'drawdown'
        assert risk_event.current_value == Decimal('0.2')  # 20%回撤
        assert risk_event.threshold_value == Decimal('0.1')  # 10%限制


class TestOrderManager:
    """测试订单管理器"""
    
    def setup_method(self):
        """测试前设置"""
        self.order_manager = OrderManager()
    
    def test_create_order(self):
        """测试创建订单"""
        order = self.order_manager.create_order(
            symbol="TEST001",
            side=OrderSide.BUY,
            order_type=OrderType.LIMIT,
            quantity=1000,
            price=Decimal('10.0'),
            strategy_id="test_strategy"
        )
        
        assert order.symbol == "TEST001"
        assert order.side == OrderSide.BUY
        assert order.quantity == 1000
        assert order.price == Decimal('10.0')
        assert order.order_id in self.order_manager.orders
        assert order.order_id in self.order_manager.active_orders
    
    def test_fill_order(self):
        """测试订单成交"""
        # 创建订单
        order = self.order_manager.create_order(
            symbol="TEST001",
            side=OrderSide.BUY,
            order_type=OrderType.LIMIT,
            quantity=1000,
            price=Decimal('10.0')
        )
        
        # 部分成交
        success = self.order_manager.fill_order(
            order_id=order.order_id,
            fill_quantity=500,
            fill_price=Decimal('10.05'),
            commission=Decimal('1.5')
        )
        
        assert success == True
        assert order.filled_quantity == 500
        assert order.avg_fill_price == Decimal('10.05')
        assert order.commission == Decimal('1.5')
        assert not order.is_filled
        
        # 完全成交
        self.order_manager.fill_order(
            order_id=order.order_id,
            fill_quantity=500,
            fill_price=Decimal('10.10'),
            commission=Decimal('1.5')
        )
        
        assert order.is_filled == True
        assert order.order_id not in self.order_manager.active_orders
    
    def test_cancel_order(self):
        """测试取消订单"""
        # 创建订单
        order = self.order_manager.create_order(
            symbol="TEST001",
            side=OrderSide.BUY,
            order_type=OrderType.LIMIT,
            quantity=1000,
            price=Decimal('10.0')
        )
        
        # 取消订单
        success = self.order_manager.cancel_order(order.order_id, "用户取消")
        
        assert success == True
        assert order.order_id not in self.order_manager.active_orders
        
        # 尝试取消已取消的订单
        success = self.order_manager.cancel_order(order.order_id, "重复取消")
        assert success == False


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
