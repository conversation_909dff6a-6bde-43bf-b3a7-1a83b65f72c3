"""
策略功能测试模块

测试新增的量化交易策略功能，包括MACD策略、配对交易策略等。
"""

import pytest
import pandas as pd
import numpy as np
import sys
from pathlib import Path
from unittest.mock import Mock, patch

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from quantitative_tools.strategy.base import StrategyContext, StrategyState
from quantitative_tools.strategy.macd_strategy import MACDStrategy, create_macd_strategy
from quantitative_tools.strategy.pairs_trading_strategy import PairsTradingStrategy, create_pairs_trading_strategy
from quantitative_tools.strategy.registry import strategy_registry, get_strategy_registry


class TestMACDStrategy:
    """MACD策略测试"""
    
    @pytest.fixture
    def sample_price_data(self):
        """生成测试用的价格数据"""
        dates = pd.date_range('2024-01-01', periods=100, freq='D')
        np.random.seed(42)
        
        # 生成模拟价格数据（带趋势）
        base_price = 100
        returns = np.random.normal(0.001, 0.02, 100)  # 日收益率
        prices = [base_price]
        
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        return pd.DataFrame({
            'close': prices,
            'high': [p * 1.01 for p in prices],
            'low': [p * 0.99 for p in prices],
            'volume': np.random.randint(1000000, 5000000, 100)
        }, index=dates)
    
    @pytest.fixture
    def macd_context(self):
        """创建MACD策略上下文"""
        return StrategyContext(
            strategy_id="test_macd",
            name="测试MACD策略",
            description="用于测试的MACD策略",
            universe=["000001.SZ"],
            parameters={
                'fast_period': 12,
                'slow_period': 26,
                'signal_period': 9,
                'position_size': 0.8,
                'stop_loss_pct': 0.05
            },
            capital=100000
        )
    
    def test_macd_strategy_initialization(self, macd_context):
        """测试MACD策略初始化"""
        strategy = MACDStrategy(macd_context)
        
        assert strategy.context == macd_context
        assert strategy.params['fast_period'] == 12
        assert strategy.params['slow_period'] == 26
        assert strategy.params['signal_period'] == 9
        assert strategy.position == 0
        assert strategy.entry_price == 0
    
    def test_macd_strategy_initialize(self, macd_context):
        """测试策略初始化方法"""
        strategy = MACDStrategy(macd_context)
        
        # 测试正常初始化
        result = strategy.initialize()
        assert result is True
        assert strategy.state == StrategyState.INITIALIZED
        
        # 测试参数错误的情况
        macd_context.parameters['fast_period'] = 30  # 快速周期大于慢速周期
        strategy_invalid = MACDStrategy(macd_context)
        result = strategy_invalid.initialize()
        assert result is False
        assert strategy_invalid.state == StrategyState.ERROR
    
    def test_macd_calculate_indicators(self, macd_context, sample_price_data):
        """测试MACD指标计算"""
        strategy = MACDStrategy(macd_context)
        strategy.initialize()
        
        indicators = strategy.calculate_indicators(sample_price_data)
        
        assert 'macd' in indicators
        assert 'signal' in indicators
        assert 'histogram' in indicators
        assert 'macd_cross_above' in indicators
        assert 'macd_cross_below' in indicators
        
        # 检查数据类型和长度
        assert isinstance(indicators['macd'], pd.Series)
        assert len(indicators['macd']) == len(sample_price_data)
    
    def test_macd_generate_signals(self, macd_context, sample_price_data):
        """测试MACD信号生成"""
        strategy = MACDStrategy(macd_context)
        strategy.initialize()
        
        signals = strategy.generate_signals(sample_price_data)
        
        assert isinstance(signals, pd.Series)
        assert len(signals) == len(sample_price_data)
        assert all(signal in [-1, 0, 1] for signal in signals)
    
    def test_macd_on_data(self, macd_context, sample_price_data):
        """测试MACD策略数据处理"""
        strategy = MACDStrategy(macd_context)
        strategy.initialize()
        
        current_time = sample_price_data.index[-1]
        decision = strategy.on_data(sample_price_data, current_time)
        
        assert 'timestamp' in decision
        assert 'signal' in decision
        assert 'action' in decision
        assert 'quantity' in decision
        assert decision['action'] in ['buy', 'sell', 'hold']
    
    def test_create_macd_strategy(self):
        """测试MACD策略创建函数"""
        strategy = create_macd_strategy("000001.SZ", fast_period=10, slow_period=20)
        
        assert isinstance(strategy, MACDStrategy)
        assert strategy.params['fast_period'] == 10
        assert strategy.params['slow_period'] == 20
        assert "000001.SZ" in strategy.context.universe


class TestPairsTradingStrategy:
    """配对交易策略测试"""
    
    @pytest.fixture
    def sample_pairs_data(self):
        """生成测试用的配对价格数据"""
        dates = pd.date_range('2024-01-01', periods=300, freq='D')
        np.random.seed(42)
        
        # 生成两只相关股票的价格数据
        base_price_a = 100
        base_price_b = 50
        
        # 生成相关的收益率
        market_factor = np.random.normal(0, 0.01, 300)
        idiosyncratic_a = np.random.normal(0, 0.005, 300)
        idiosyncratic_b = np.random.normal(0, 0.005, 300)
        
        returns_a = 0.8 * market_factor + idiosyncratic_a
        returns_b = 0.7 * market_factor + idiosyncratic_b
        
        prices_a = [base_price_a]
        prices_b = [base_price_b]
        
        for i in range(1, 300):
            prices_a.append(prices_a[-1] * (1 + returns_a[i]))
            prices_b.append(prices_b[-1] * (1 + returns_b[i]))
        
        return pd.DataFrame({
            '601318.SH': prices_a,  # 中国平安
            '600036.SH': prices_b   # 招商银行
        }, index=dates)
    
    @pytest.fixture
    def pairs_context(self):
        """创建配对交易策略上下文"""
        return StrategyContext(
            strategy_id="test_pairs",
            name="测试配对交易策略",
            description="用于测试的配对交易策略",
            universe=["601318.SH", "600036.SH"],
            parameters={
                'lookback_period': 252,
                'entry_threshold': 2.0,
                'exit_threshold': 0.5,
                'stop_loss_threshold': 3.5,
                'position_size': 0.4
            },
            capital=100000
        )
    
    def test_pairs_strategy_initialization(self, pairs_context):
        """测试配对交易策略初始化"""
        strategy = PairsTradingStrategy(pairs_context)
        
        assert strategy.context == pairs_context
        assert strategy.stock_a == "601318.SH"
        assert strategy.stock_b == "600036.SH"
        assert strategy.params['entry_threshold'] == 2.0
        assert strategy.position_a == 0
        assert strategy.position_b == 0
    
    def test_pairs_strategy_initialize(self, pairs_context):
        """测试配对交易策略初始化方法"""
        strategy = PairsTradingStrategy(pairs_context)
        
        # 测试正常初始化
        result = strategy.initialize()
        assert result is True
        assert strategy.state == StrategyState.INITIALIZED
        
        # 测试股票对错误的情况
        pairs_context.universe = ["601318.SH"]  # 只有一只股票
        strategy_invalid = PairsTradingStrategy(pairs_context)
        result = strategy_invalid.initialize()
        assert result is False
        assert strategy_invalid.state == StrategyState.ERROR
    
    def test_pairs_calculate_cointegration(self, pairs_context, sample_pairs_data):
        """测试协整关系计算"""
        strategy = PairsTradingStrategy(pairs_context)
        strategy.initialize()
        
        price_a = sample_pairs_data['601318.SH']
        price_b = sample_pairs_data['600036.SH']
        
        coint_result = strategy.calculate_cointegration(price_a, price_b)
        
        assert 'hedge_ratio' in coint_result
        assert 'spread_mean' in coint_result
        assert 'spread_std' in coint_result
        assert 'correlation' in coint_result
        assert 'is_cointegrated' in coint_result
        
        # 检查数值合理性
        assert isinstance(coint_result['hedge_ratio'], float)
        assert coint_result['correlation'] >= -1 and coint_result['correlation'] <= 1
    
    def test_pairs_calculate_z_score(self, pairs_context):
        """测试Z-score计算"""
        strategy = PairsTradingStrategy(pairs_context)
        strategy.initialize()
        
        # 设置测试参数
        strategy.hedge_ratio = 1.5
        strategy.spread_mean = 0.1
        strategy.spread_std = 0.05
        
        z_score = strategy.calculate_z_score(100, 60)
        
        assert isinstance(z_score, float)
        # Z-score应该在合理范围内
        assert abs(z_score) < 10
    
    def test_pairs_generate_signals(self, pairs_context, sample_pairs_data):
        """测试配对交易信号生成"""
        strategy = PairsTradingStrategy(pairs_context)
        strategy.initialize()
        
        # 更新参数
        strategy.update_parameters(sample_pairs_data)
        
        signals = strategy.generate_signals(sample_pairs_data)
        
        assert 'signal_a' in signals
        assert 'signal_b' in signals
        assert 'z_score' in signals
        assert signals['signal_a'] in [-1, 0, 1]
        assert signals['signal_b'] in [-1, 0, 1]
    
    def test_create_pairs_trading_strategy(self):
        """测试配对交易策略创建函数"""
        strategy = create_pairs_trading_strategy(
            "601318.SH", "600036.SH", 
            entry_threshold=1.5, exit_threshold=0.3
        )
        
        assert isinstance(strategy, PairsTradingStrategy)
        assert strategy.params['entry_threshold'] == 1.5
        assert strategy.params['exit_threshold'] == 0.3
        assert strategy.stock_a == "601318.SH"
        assert strategy.stock_b == "600036.SH"


class TestStrategyRegistry:
    """策略注册中心测试"""
    
    def test_strategy_registry_initialization(self):
        """测试策略注册中心初始化"""
        registry = get_strategy_registry()
        
        assert registry is not None
        strategies = registry.get_all_strategies()
        assert len(strategies) > 0
        
        # 检查是否包含新策略
        assert 'macd_strategy' in strategies
        assert 'pairs_trading' in strategies
    
    def test_get_strategy_config(self):
        """测试获取策略配置"""
        registry = get_strategy_registry()
        
        # 测试获取MACD策略配置
        macd_config = registry.get_strategy_config('macd_strategy')
        assert macd_config['name'] == 'MACD策略'
        assert macd_config['category'] == 'technical'
        assert macd_config['difficulty'] == 'intermediate'
        
        # 测试获取不存在的策略
        invalid_config = registry.get_strategy_config('invalid_strategy')
        assert invalid_config == {}
    
    def test_get_strategies_by_category(self):
        """测试按分类获取策略"""
        registry = get_strategy_registry()
        
        # 测试技术指标类策略
        technical_strategies = registry.get_strategies_by_category('technical')
        assert 'macd_strategy' in technical_strategies
        
        # 测试统计套利类策略
        arbitrage_strategies = registry.get_strategies_by_category('arbitrage')
        assert 'pairs_trading' in arbitrage_strategies
    
    def test_get_strategies_by_difficulty(self):
        """测试按难度获取策略"""
        registry = get_strategy_registry()
        
        # 测试中级策略
        intermediate_strategies = registry.get_strategies_by_difficulty('intermediate')
        assert len(intermediate_strategies) > 0
        
        # 测试高级策略
        advanced_strategies = registry.get_strategies_by_difficulty('advanced')
        assert len(advanced_strategies) > 0
    
    def test_search_strategies(self):
        """测试策略搜索"""
        registry = get_strategy_registry()
        
        # 搜索MACD相关策略
        macd_results = registry.search_strategies('MACD')
        assert len(macd_results) > 0
        assert 'macd_strategy' in macd_results
        
        # 搜索配对交易相关策略
        pairs_results = registry.search_strategies('配对')
        assert len(pairs_results) > 0
        assert 'pairs_trading' in pairs_results
    
    @patch('quantitative_tools.strategy.registry.importlib.import_module')
    def test_create_strategy_instance(self, mock_import):
        """测试策略实例创建"""
        registry = get_strategy_registry()
        
        # 模拟策略类
        mock_strategy_class = Mock()
        mock_strategy_instance = Mock()
        mock_strategy_class.return_value = mock_strategy_instance
        
        mock_module = Mock()
        mock_module.MACDStrategy = mock_strategy_class
        mock_import.return_value = mock_module
        
        # 创建测试上下文
        context = StrategyContext(
            strategy_id="test",
            name="测试策略",
            description="测试",
            universe=["000001.SZ"]
        )
        
        # 测试创建策略实例
        strategy = registry.create_strategy('macd_strategy', context)
        
        assert strategy == mock_strategy_instance
        mock_strategy_class.assert_called_once_with(context)


class TestStrategyValidation:
    """策略验证测试"""
    
    def test_macd_parameter_validation(self):
        """测试MACD策略参数验证"""
        context = StrategyContext(
            strategy_id="test_macd",
            name="测试MACD策略",
            description="测试",
            universe=["000001.SZ"]
        )
        
        strategy = MACDStrategy(context)
        
        # 测试有效参数
        valid_params = {
            'fast_period': 12,
            'slow_period': 26,
            'signal_period': 9,
            'position_size': 0.8
        }
        assert strategy.validate_parameters(valid_params) is True
        
        # 测试无效参数
        invalid_params = {
            'fast_period': 30,  # 大于慢速周期
            'slow_period': 26,
            'signal_period': 9
        }
        assert strategy.validate_parameters(invalid_params) is False
    
    def test_strategy_error_handling(self):
        """测试策略错误处理"""
        context = StrategyContext(
            strategy_id="test_error",
            name="错误测试策略",
            description="测试错误处理",
            universe=["000001.SZ"]
        )
        
        strategy = MACDStrategy(context)
        
        # 测试无效数据处理
        invalid_data = pd.DataFrame({'invalid_column': [1, 2, 3]})
        current_time = pd.Timestamp.now()
        
        decision = strategy.on_data(invalid_data, current_time)
        
        assert 'error' in decision
        assert decision['action'] == 'hold'


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
