# =============================================================================
# AI量化交易工具系统 - Git忽略文件配置
# =============================================================================
# 此文件定义了不应该被Git版本控制系统跟踪的文件和目录
# 包含Python、Node.js、Docker、IDE等开发环境相关的忽略规则

# =============================================================================
# Python 相关文件
# =============================================================================

# 字节码文件 - Python编译后的缓存文件
__pycache__/
*.py[cod]
*$py.class

# C扩展模块
*.so

# 分发/打包相关
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller打包工具生成的文件
*.manifest
*.spec

# 单元测试/覆盖率报告
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# 翻译文件
*.mo
*.pot

# Django相关（如果使用）
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask相关（如果使用）
instance/
.webassets-cache

# Scrapy相关（如果使用）
.scrapy

# Sphinx文档
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook检查点
.ipynb_checkpoints

# IPython配置
profile_default/
ipython_config.py

# pyenv版本文件
.python-version

# pipenv依赖文件
Pipfile.lock

# poetry依赖锁定文件
poetry.lock

# pdm项目文件
.pdm.toml

# PEP 582相关
__pypackages__/

# Celery相关
celerybeat-schedule
celerybeat.pid

# SageMath解析文件
*.sage.py

# 虚拟环境目录
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder项目设置
.spyderproject
.spyproject

# Rope项目设置
.ropeproject

# mkdocs文档
/site

# mypy类型检查缓存
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre类型检查器
.pyre/

# pytype静态类型分析器
.pytype/

# Cython调试符号
cython_debug/

# =============================================================================
# Node.js / React 前端相关文件
# =============================================================================

# 依赖包目录
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
lib-cov

# nyc测试覆盖率
.nyc_output

# Grunt中间存储
.grunt

# Bower依赖目录
bower_components

# node-waf配置
.lock-wscript

# 编译的二进制插件
build/Release

# 依赖目录
node_modules/
jspm_packages/

# TypeScript缓存
*.tsbuildinfo

# 可选的npm缓存目录
.npm

# 可选的eslint缓存
.eslintcache

# 可选的stylelint缓存
.stylelintcache

# Microbundle缓存
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# 可选的REPL历史
.node_repl_history

# yarn完整性文件
.yarn-integrity

# dotenv环境变量文件
.env.local
.env.development.local
.env.test.local
.env.production.local

# parcel-bundler缓存
.cache
.parcel-cache

# Next.js构建输出
.next
out

# Nuxt.js构建/生成输出
.nuxt
dist

# Gatsby文件
.cache/
public

# Storybook构建输出
.out
.storybook-out

# Temporary folders
tmp/
temp/

# =============================================================================
# 数据库相关文件
# =============================================================================

# SQLite数据库文件
*.sqlite
*.sqlite3
*.db

# PostgreSQL备份文件
*.sql.gz
*.backup

# 数据库迁移临时文件
migrations/versions/*.pyc

# =============================================================================
# 日志和缓存文件
# =============================================================================

# 应用日志文件
logs/
*.log
log/

# 缓存目录和文件
cache/
*.cache
.cache/

# 临时文件
tmp/
temp/
*.tmp
*.temp

# =============================================================================
# 上传和用户生成内容
# =============================================================================

# 用户上传文件目录
uploads/
media/
static/uploads/

# 备份文件
*.bak
*.backup
*.old

# =============================================================================
# Docker 相关文件
# =============================================================================

# Docker构建缓存
.dockerignore

# =============================================================================
# IDE 和编辑器配置文件
# =============================================================================

# Visual Studio Code
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =============================================================================
# 操作系统生成的文件
# =============================================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~

# =============================================================================
# 安全和配置文件
# =============================================================================

# 环境变量和密钥文件
.env
.env.local
.env.*.local
*.key
*.pem
*.p12
*.pfx
secrets/
config/local.py
config/production.py

# SSL证书
*.crt
*.csr

# =============================================================================
# 监控和分析工具
# =============================================================================

# Prometheus数据
prometheus_data/

# Grafana数据
grafana_data/

# =============================================================================
# 项目特定忽略规则
# =============================================================================

# 量化交易相关数据文件
data/
*.csv
*.xlsx
*.json.gz
*.parquet
*.feather

# 模型文件和机器学习相关
models/
*.pkl
*.joblib
*.h5
*.pb
*.onnx
*.pt
*.pth
*.model

# 回测结果和报告
backtest_results/
reports/
results/
output/

# 性能分析文件
*.prof
*.cprof

# Jupyter Notebook输出
*.ipynb_checkpoints/
.ipynb_checkpoints/

# 量化分析工具特定文件
.quantlib/
.ta-lib/

# 财务数据缓存
financial_data/
market_data/
price_data/

# 策略配置和参数文件（敏感信息）
strategy_configs/
trading_params/
api_keys/

# 实时交易日志（可能包含敏感信息）
trading_logs/
execution_logs/

# 风险管理报告
risk_reports/
compliance_reports/

# 数据库备份文件
*.dump
*.sql.bak

# 配置文件备份
config/*.bak
config/*.backup

# 临时计算文件
calculations/
temp_calculations/

# 第三方工具生成的文件
.mypy_cache/
.pytest_cache/
.coverage
htmlcov/

# 开发工具配置
.pre-commit-config.yaml.bak
.flake8.bak

# 系统监控数据
monitoring_data/
metrics/

# 容器相关临时文件
.docker/
docker-data/

# =============================================================================
# 开发和测试环境特定文件
# =============================================================================

# 测试覆盖率报告
.coverage.*
coverage.xml
*.cover
.hypothesis/

# 性能测试结果
benchmark_results/
performance_tests/

# 集成测试数据
test_data/
mock_data/

# 开发环境配置
.env.development
.env.testing
.env.staging

# 本地开发服务器文件
.local/
local_config/

# 调试文件
debug/
debug_logs/
*.debug

# 代码质量检查报告
.bandit/
.safety/
pylint-report.txt
flake8-report.txt

# =============================================================================
# 部署和运维相关文件
# =============================================================================

# 部署脚本生成的文件
deployment_logs/
deploy_temp/

# Kubernetes配置文件（可能包含敏感信息）
k8s-secrets/
*.kubeconfig

# Terraform状态文件
*.tfstate
*.tfstate.backup
.terraform/

# Ansible相关
*.retry
.ansible/

# 服务器配置备份
server_configs/
nginx_configs/

# SSL证书和密钥文件
ssl/
certificates/
private_keys/
