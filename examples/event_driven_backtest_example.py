"""
事件驱动回测系统示例

演示如何使用事件驱动架构进行量化交易回测，包括：
- 数据加载和处理
- 策略定义和执行
- 风险管理和控制
- 回测结果分析
"""

import sys
import os
import logging
from datetime import datetime, timedelta
from decimal import Decimal

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.quantitative_tools.engines.data_engine import HistoricalDataProvider, DataConfig
from src.quantitative_tools.engines.strategy_engine import MovingAverageCrossoverStrategy, StrategyConfig
from src.quantitative_tools.engines.execution_engine import Portfolio
from src.quantitative_tools.engines.risk_engine import RiskLimits
from src.quantitative_tools.engines.backtest_engine import EventDrivenBacktester, BacktestConfig

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_sample_strategy() -> MovingAverageCrossoverStrategy:
    """创建示例策略"""
    
    # 策略配置
    strategy_config = StrategyConfig(
        strategy_id="ma_cross_001",
        strategy_name="移动平均线交叉策略",
        symbols=["000001.SZ", "000002.SZ"],
        parameters={
            'short_window': 10,  # 短期移动平均线周期
            'long_window': 30    # 长期移动平均线周期
        },
        enabled=True,
        max_position_size=1000,
        risk_limit=0.02
    )
    
    # 创建策略实例
    strategy = MovingAverageCrossoverStrategy(strategy_config)
    
    logger.info(f"创建策略: {strategy_config.strategy_name}")
    return strategy


def create_backtest_config() -> BacktestConfig:
    """创建回测配置"""
    
    config = BacktestConfig(
        start_date=datetime(2023, 1, 1),
        end_date=datetime(2023, 12, 31),
        initial_capital=Decimal('1000000'),  # 100万初始资金
        symbols=["000001.SZ", "000002.SZ"],
        timeframe="1D",
        commission_rate=0.0003,  # 万分之三手续费
        slippage_rate=0.001,     # 千分之一滑点
        data_source="csv",
        data_path="data/",
        benchmark="000300.SH"
    )
    
    logger.info(f"回测配置: {config.start_date} 到 {config.end_date}")
    return config


def create_risk_limits() -> RiskLimits:
    """创建风险限制"""
    
    risk_limits = RiskLimits(
        max_position_size=1000,              # 最大单个仓位
        max_portfolio_value=Decimal('10000000'),  # 最大组合价值
        max_daily_loss=Decimal('50000'),     # 最大日损失5万
        max_drawdown=Decimal('0.1'),         # 最大回撤10%
        max_leverage=Decimal('3.0'),         # 最大杠杆3倍
        max_concentration=Decimal('0.3'),    # 最大单品种集中度30%
        stop_loss_pct=Decimal('0.02'),       # 止损2%
        take_profit_pct=Decimal('0.05')      # 止盈5%
    )
    
    logger.info("风险限制配置完成")
    return risk_limits


def run_backtest_example():
    """运行回测示例"""
    
    try:
        logger.info("=" * 60)
        logger.info("开始事件驱动回测示例")
        logger.info("=" * 60)
        
        # 1. 创建配置
        backtest_config = create_backtest_config()
        risk_limits = create_risk_limits()
        
        # 2. 创建数据提供者
        data_provider = HistoricalDataProvider(data_path="data/")
        
        # 3. 创建策略
        strategy = create_sample_strategy()
        strategies = [strategy]
        
        # 4. 创建回测器
        backtester = EventDrivenBacktester(
            config=backtest_config,
            data_provider=data_provider,
            strategies=strategies,
            risk_limits=risk_limits
        )
        
        # 5. 定义进度回调
        def progress_callback(progress: float):
            if int(progress * 100) % 10 == 0:  # 每10%显示一次
                logger.info(f"回测进度: {progress:.1%}")
        
        # 6. 运行回测
        logger.info("开始执行回测...")
        start_time = datetime.now()
        
        result = backtester.run_backtest(progress_callback=progress_callback)
        
        end_time = datetime.now()
        logger.info(f"回测完成，耗时: {end_time - start_time}")
        
        # 7. 分析结果
        analyze_backtest_result(result)
        
        # 8. 保存结果
        save_backtest_result(result)
        
        logger.info("=" * 60)
        logger.info("事件驱动回测示例完成")
        logger.info("=" * 60)
        
    except Exception as e:
        logger.error(f"回测示例执行失败: {e}")
        raise


def analyze_backtest_result(result):
    """分析回测结果"""
    
    logger.info("\n" + "=" * 40)
    logger.info("回测结果分析")
    logger.info("=" * 40)
    
    # 基本信息
    logger.info(f"回测期间: {result.config.start_date.date()} 到 {result.config.end_date.date()}")
    logger.info(f"回测时长: {result.duration}")
    logger.info(f"交易品种: {', '.join(result.config.symbols)}")
    
    # 绩效指标
    logger.info("\n绩效指标:")
    logger.info(f"  初始资金: ¥{result.initial_capital:,.2f}")
    logger.info(f"  最终资金: ¥{result.final_capital:,.2f}")
    logger.info(f"  总收益率: {result.total_return:.2%}")
    logger.info(f"  年化收益率: {result.annual_return:.2%}")
    logger.info(f"  波动率: {result.volatility:.2%}")
    logger.info(f"  夏普比率: {result.sharpe_ratio:.3f}")
    logger.info(f"  最大回撤: {result.max_drawdown:.2%}")
    
    # 交易统计
    logger.info("\n交易统计:")
    logger.info(f"  总交易次数: {result.total_trades}")
    logger.info(f"  盈利交易: {result.winning_trades}")
    logger.info(f"  亏损交易: {result.losing_trades}")
    logger.info(f"  胜率: {result.win_rate:.2%}")
    logger.info(f"  平均盈利: ¥{result.avg_win:.2f}")
    logger.info(f"  平均亏损: ¥{result.avg_loss:.2f}")
    logger.info(f"  盈亏比: {result.profit_factor:.2f}")
    
    # 风险事件
    if result.risk_events:
        logger.info(f"\n风险事件: {len(result.risk_events)} 个")
        for i, risk_event in enumerate(result.risk_events[:5]):  # 显示前5个
            logger.info(f"  {i+1}. {risk_event['risk_type']}: {risk_event['risk_message']}")
    
    # 组合价值变化
    if len(result.portfolio_values) > 0:
        logger.info(f"\n组合价值数据点: {len(result.portfolio_values)} 个")
        logger.info(f"  起始价值: ¥{result.portfolio_values.iloc[0]:,.2f}")
        logger.info(f"  结束价值: ¥{result.portfolio_values.iloc[-1]:,.2f}")
        logger.info(f"  最高价值: ¥{result.portfolio_values.max():,.2f}")
        logger.info(f"  最低价值: ¥{result.portfolio_values.min():,.2f}")


def save_backtest_result(result):
    """保存回测结果"""
    
    try:
        # 创建结果目录
        os.makedirs("results", exist_ok=True)
        
        # 保存为JSON格式
        import json
        result_dict = result.to_dict()
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"results/backtest_result_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(result_dict, f, ensure_ascii=False, indent=2)
        
        logger.info(f"回测结果已保存到: {filename}")
        
        # 保存组合价值序列
        if len(result.portfolio_values) > 0:
            csv_filename = f"results/portfolio_values_{timestamp}.csv"
            result.portfolio_values.to_csv(csv_filename)
            logger.info(f"组合价值序列已保存到: {csv_filename}")
        
        # 保存交易历史
        if result.trades_history:
            import pandas as pd
            trades_df = pd.DataFrame(result.trades_history)
            trades_filename = f"results/trades_history_{timestamp}.csv"
            trades_df.to_csv(trades_filename, index=False)
            logger.info(f"交易历史已保存到: {trades_filename}")
        
    except Exception as e:
        logger.error(f"保存回测结果失败: {e}")


def create_sample_data():
    """创建示例数据文件"""
    
    try:
        import pandas as pd
        import numpy as np
        
        # 创建数据目录
        os.makedirs("data", exist_ok=True)
        
        # 生成示例数据
        symbols = ["000001.SZ", "000002.SZ"]
        start_date = datetime(2023, 1, 1)
        end_date = datetime(2023, 12, 31)
        
        dates = pd.date_range(start=start_date, end=end_date, freq='D')
        dates = dates[dates.weekday < 5]  # 只保留工作日
        
        for symbol in symbols:
            # 设置随机种子以确保可重复性
            np.random.seed(hash(symbol) % 2**32)
            
            # 生成价格数据
            base_price = 10 + np.random.random() * 20
            returns = np.random.normal(0.0005, 0.02, len(dates))
            
            prices = [base_price]
            for ret in returns[1:]:
                prices.append(prices[-1] * (1 + ret))
            
            # 生成OHLCV数据
            data = []
            for i, (date, close) in enumerate(zip(dates, prices)):
                if i == 0:
                    open_price = close
                else:
                    open_price = prices[i-1]
                
                # 生成高低价
                volatility = abs(np.random.normal(0, 0.01))
                high = max(open_price, close) * (1 + volatility)
                low = min(open_price, close) * (1 - volatility)
                
                # 生成成交量
                volume = int(np.random.lognormal(13, 1))
                
                data.append({
                    'date': date,
                    'open': round(open_price, 2),
                    'high': round(high, 2),
                    'low': round(low, 2),
                    'close': round(close, 2),
                    'volume': volume
                })
            
            # 保存数据
            df = pd.DataFrame(data)
            filename = f"data/{symbol}_1D.csv"
            df.to_csv(filename, index=False)
            
            logger.info(f"示例数据已创建: {filename}")
        
    except Exception as e:
        logger.error(f"创建示例数据失败: {e}")


if __name__ == "__main__":
    # 创建示例数据
    create_sample_data()
    
    # 运行回测示例
    run_backtest_example()
