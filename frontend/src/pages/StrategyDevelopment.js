import React, { useState, useEffect } from 'react';
import { Layout, Row, Col, Card, Button, Space, message, Modal } from 'antd';
import { 
  CodeOutlined, 
  BugOutlined, 
  FileTextOutlined,
  SaveOutlined,
  PlayCircleOutlined,
  SettingOutlined
} from '@ant-design/icons';
import StrategyEditor from '../components/StrategyEditor';
import StrategyDebugger from '../components/StrategyDebugger';
import StrategyTemplateLibrary from '../components/StrategyTemplateLibrary';
import { apiService } from '../services/api';

const { Content, Sider } = Layout;

/**
 * 策略开发页面
 * 
 * 集成了策略编辑器、调试器、模板库等功能
 * 提供完整的策略开发环境
 */
const StrategyDevelopment = () => {
  const [strategyCode, setStrategyCode] = useState('');
  const [strategyParams, setStrategyParams] = useState({});
  const [isDebugging, setIsDebugging] = useState(false);
  const [debugData, setDebugData] = useState({});
  const [templateLibraryVisible, setTemplateLibraryVisible] = useState(false);
  const [currentStrategy, setCurrentStrategy] = useState(null);
  const [loading, setLoading] = useState(false);

  /**
   * 组件挂载时初始化
   */
  useEffect(() => {
    // 加载默认策略代码
    const defaultCode = `def initialize(context):
    """
    策略初始化函数
    在策略开始运行前调用一次
    """
    # 设置交易标的
    context.symbol = '000001.SZ'
    
    # 设置基准
    context.benchmark = '000300.SH'
    
    # 设置手续费
    set_order_cost(OrderCost(close_tax=0.001, open_commission=0.0003,
                            close_commission=0.0003, min_commission=5), type='stock')
    
    log.info("策略初始化完成")

def handle_data(context, data):
    """
    策略主逻辑函数
    每个交易周期调用一次
    """
    # 获取当前价格
    current_price = get_current_price(context.symbol)
    
    # 记录价格
    record(price=current_price)
    
    # 在这里编写你的交易逻辑
    log.info(f"当前价格: {current_price}")

def before_trading_start(context, data):
    """
    开盘前调用函数
    在每个交易日开盘前调用
    """
    log.info("开盘前准备")
    pass`;

    setStrategyCode(defaultCode);
    setStrategyParams({
      symbol: '000001.SZ',
      capital: 100000,
      benchmark: '000300.SH',
      frequency: 'daily'
    });
  }, []);

  /**
   * 保存策略
   */
  const handleSaveStrategy = async (code, params) => {
    try {
      setLoading(true);
      
      // 这里应该调用后端API保存策略
      // const response = await apiService.post('/api/v1/strategies', {
      //   name: '我的策略',
      //   code: code,
      //   params: params
      // });
      
      // 模拟保存
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setStrategyCode(code);
      setStrategyParams(params);
      
      message.success('策略保存成功');
      
    } catch (error) {
      console.error('保存策略失败:', error);
      message.error('保存策略失败');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 运行策略
   */
  const handleRunStrategy = async (code, params) => {
    try {
      setLoading(true);
      
      // 这里应该调用后端API运行策略
      // const response = await apiService.post('/api/v1/strategies/run', {
      //   code: code,
      //   params: params
      // });
      
      // 模拟运行
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      message.success('策略启动成功');
      
      // 模拟策略运行状态
      setCurrentStrategy({
        id: 'strategy_001',
        name: '我的策略',
        status: 'running',
        startTime: new Date().toISOString()
      });
      
    } catch (error) {
      console.error('运行策略失败:', error);
      message.error('运行策略失败');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 停止策略
   */
  const handleStopStrategy = async () => {
    try {
      setLoading(true);
      
      // 这里应该调用后端API停止策略
      // await apiService.post('/api/v1/strategies/stop', {
      //   strategyId: currentStrategy.id
      // });
      
      // 模拟停止
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      message.success('策略已停止');
      
      setCurrentStrategy(null);
      
    } catch (error) {
      console.error('停止策略失败:', error);
      message.error('停止策略失败');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 开始调试
   */
  const handleStartDebug = async () => {
    try {
      setLoading(true);
      
      // 这里应该调用后端API开始调试
      // const response = await apiService.post('/api/v1/strategies/debug', {
      //   code: strategyCode,
      //   params: strategyParams
      // });
      
      // 模拟调试数据
      const mockDebugData = {
        variables: {
          'context.symbol': '000001.SZ',
          'current_price': 12.45,
          'context.benchmark': '000300.SH'
        },
        stack: [
          { function: 'handle_data', line: 25, file: 'strategy.py' },
          { function: 'get_current_price', line: 30, file: 'api.py' }
        ],
        logs: [
          { timestamp: Date.now(), level: 'info', message: '策略初始化完成' },
          { timestamp: Date.now() - 1000, level: 'info', message: '当前价格: 12.45' }
        ],
        currentLine: 25,
        performance: {
          executionTime: 150,
          memoryUsage: 45,
          functionCalls: 12,
          cpuUsage: 25
        }
      };
      
      setDebugData(mockDebugData);
      setIsDebugging(true);
      
      message.success('调试模式已启动');
      
    } catch (error) {
      console.error('启动调试失败:', error);
      message.error('启动调试失败');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 停止调试
   */
  const handleStopDebug = async () => {
    try {
      setIsDebugging(false);
      setDebugData({});
      message.success('调试模式已停止');
    } catch (error) {
      console.error('停止调试失败:', error);
      message.error('停止调试失败');
    }
  };

  /**
   * 单步调试
   */
  const handleStepDebug = async () => {
    try {
      // 这里应该调用后端API进行单步调试
      // 模拟单步执行
      const newDebugData = {
        ...debugData,
        currentLine: (debugData.currentLine || 25) + 1,
        logs: [
          ...debugData.logs,
          { timestamp: Date.now(), level: 'debug', message: `执行到第 ${(debugData.currentLine || 25) + 1} 行` }
        ]
      };
      
      setDebugData(newDebugData);
      
    } catch (error) {
      console.error('单步调试失败:', error);
      message.error('单步调试失败');
    }
  };

  /**
   * 使用模板
   */
  const handleUseTemplate = (template) => {
    setStrategyCode(template.code);
    
    // 根据模板更新参数
    const templateParams = {
      ...strategyParams,
      symbol: template.code.includes('000001.SZ') ? '000001.SZ' : strategyParams.symbol
    };
    setStrategyParams(templateParams);
    
    message.success(`已应用模板：${template.name}`);
  };

  return (
    <Layout style={{ height: '100vh', background: '#f0f2f5' }}>
      <Content style={{ padding: '24px' }}>
        <Row gutter={24} style={{ height: '100%' }}>
          {/* 左侧：策略编辑器 */}
          <Col span={16}>
            <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              {/* 工具栏 */}
              <Card 
                size="small" 
                style={{ marginBottom: '16px' }}
                bodyStyle={{ padding: '8px 16px' }}
              >
                <Space>
                  <Button 
                    type="primary" 
                    icon={<FileTextOutlined />}
                    onClick={() => setTemplateLibraryVisible(true)}
                  >
                    模板库
                  </Button>
                  
                  <Button 
                    icon={<SaveOutlined />}
                    onClick={() => handleSaveStrategy(strategyCode, strategyParams)}
                    loading={loading}
                  >
                    保存策略
                  </Button>
                  
                  <Button 
                    type="primary"
                    icon={<PlayCircleOutlined />}
                    onClick={() => handleRunStrategy(strategyCode, strategyParams)}
                    loading={loading}
                    disabled={currentStrategy?.status === 'running'}
                  >
                    {currentStrategy?.status === 'running' ? '运行中' : '运行策略'}
                  </Button>
                  
                  {currentStrategy?.status === 'running' && (
                    <Button 
                      danger
                      onClick={handleStopStrategy}
                      loading={loading}
                    >
                      停止策略
                    </Button>
                  )}
                  
                  <Button 
                    icon={<BugOutlined />}
                    onClick={isDebugging ? handleStopDebug : handleStartDebug}
                    loading={loading}
                  >
                    {isDebugging ? '停止调试' : '开始调试'}
                  </Button>
                </Space>
              </Card>
              
              {/* 策略编辑器 */}
              <div style={{ flex: 1 }}>
                <StrategyEditor
                  strategyId="current"
                  initialCode={strategyCode}
                  initialParams={strategyParams}
                  onSave={handleSaveStrategy}
                  onRun={handleRunStrategy}
                  onStop={handleStopStrategy}
                  readOnly={false}
                />
              </div>
            </div>
          </Col>
          
          {/* 右侧：调试器 */}
          <Col span={8}>
            <StrategyDebugger
              strategyCode={strategyCode}
              isDebugging={isDebugging}
              onStartDebug={handleStartDebug}
              onStopDebug={handleStopDebug}
              onStepDebug={handleStepDebug}
              debugData={debugData}
            />
          </Col>
        </Row>
      </Content>
      
      {/* 策略模板库 */}
      <StrategyTemplateLibrary
        visible={templateLibraryVisible}
        onClose={() => setTemplateLibraryVisible(false)}
        onUseTemplate={handleUseTemplate}
      />
    </Layout>
  );
};

export default StrategyDevelopment;
