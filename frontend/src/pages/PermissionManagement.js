import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Tabs, 
  Table, 
  Button, 
  Modal, 
  Form, 
  Input, 
  Select, 
  message, 
  Space, 
  Tag, 
  Popconfirm,
  Descriptions,
  Transfer,
  Row,
  Col
} from 'antd';
import { 
  UserOutlined, 
  SafetyOutlined, 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined,
  EyeOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { apiService } from '../services/api';

const { TabPane } = Tabs;
const { Option } = Select;

/**
 * 权限管理页面组件
 * 
 * 功能特性：
 * - 用户角色管理
 * - 角色权限配置
 * - 权限分配和撤销
 * - 角色创建、编辑、删除
 * - 权限查看和管理
 */
const PermissionManagement = () => {
  const [loading, setLoading] = useState(false);
  const [users, setUsers] = useState([]);
  const [roles, setRoles] = useState([]);
  const [permissions, setPermissions] = useState([]);
  const [selectedUser, setSelectedUser] = useState(null);
  const [selectedRole, setSelectedRole] = useState(null);
  
  // 模态框状态
  const [userRoleModalVisible, setUserRoleModalVisible] = useState(false);
  const [roleModalVisible, setRoleModalVisible] = useState(false);
  const [roleDetailModalVisible, setRoleDetailModalVisible] = useState(false);
  const [editingRole, setEditingRole] = useState(null);
  
  // 表单实例
  const [userRoleForm] = Form.useForm();
  const [roleForm] = Form.useForm();

  /**
   * 组件挂载时加载数据
   */
  useEffect(() => {
    loadInitialData();
  }, []);

  /**
   * 加载初始数据
   */
  const loadInitialData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        loadUsers(),
        loadRoles(),
        loadPermissions()
      ]);
    } catch (error) {
      console.error('加载数据失败:', error);
      message.error('加载数据失败');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 加载用户列表
   */
  const loadUsers = async () => {
    try {
      // 模拟用户数据，实际应该从API获取
      const mockUsers = [
        {
          id: 'user_1',
          username: 'admin',
          email: '<EMAIL>',
          full_name: '系统管理员',
          roles: ['admin'],
          is_active: true,
          created_at: '2024-01-01T00:00:00Z'
        },
        {
          id: 'user_2',
          username: 'trader1',
          email: '<EMAIL>',
          full_name: '交易员1',
          roles: ['trader'],
          is_active: true,
          created_at: '2024-01-02T00:00:00Z'
        },
        {
          id: 'user_3',
          username: 'analyst1',
          email: '<EMAIL>',
          full_name: '分析师1',
          roles: ['analyst'],
          is_active: true,
          created_at: '2024-01-03T00:00:00Z'
        }
      ];
      setUsers(mockUsers);
    } catch (error) {
      console.error('加载用户列表失败:', error);
    }
  };

  /**
   * 加载角色列表
   */
  const loadRoles = async () => {
    try {
      const response = await apiService.get('/api/v1/roles');
      if (response.data?.roles) {
        setRoles(response.data.roles);
      }
    } catch (error) {
      console.error('加载角色列表失败:', error);
    }
  };

  /**
   * 加载权限列表
   */
  const loadPermissions = async () => {
    try {
      const response = await apiService.get('/api/v1/permissions');
      if (response.data?.permissions) {
        setPermissions(response.data.permissions);
      }
    } catch (error) {
      console.error('加载权限列表失败:', error);
    }
  };

  /**
   * 为用户分配角色
   */
  const assignRoleToUser = async (values) => {
    try {
      setLoading(true);
      
      await apiService.post('/api/v1/users/assign-role', {
        user_id: values.user_id,
        role_id: values.role_id,
        expires_at: values.expires_at
      });
      
      message.success('角色分配成功');
      setUserRoleModalVisible(false);
      userRoleForm.resetFields();
      await loadUsers();
      
    } catch (error) {
      console.error('分配角色失败:', error);
      message.error(error.response?.data?.detail || '分配角色失败');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 撤销用户角色
   */
  const revokeUserRole = async (userId, roleId) => {
    try {
      setLoading(true);
      
      await apiService.post('/api/v1/users/revoke-role', null, {
        params: { user_id: userId, role_id: roleId }
      });
      
      message.success('角色撤销成功');
      await loadUsers();
      
    } catch (error) {
      console.error('撤销角色失败:', error);
      message.error(error.response?.data?.detail || '撤销角色失败');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 创建或更新角色
   */
  const saveRole = async (values) => {
    try {
      setLoading(true);
      
      if (editingRole) {
        // 更新角色
        await apiService.put(`/api/v1/roles/${editingRole.id}`, values);
        message.success('角色更新成功');
      } else {
        // 创建角色
        await apiService.post('/api/v1/roles', values);
        message.success('角色创建成功');
      }
      
      setRoleModalVisible(false);
      roleForm.resetFields();
      setEditingRole(null);
      await loadRoles();
      
    } catch (error) {
      console.error('保存角色失败:', error);
      message.error(error.response?.data?.detail || '保存角色失败');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 删除角色
   */
  const deleteRole = async (roleId) => {
    try {
      setLoading(true);
      
      await apiService.delete(`/api/v1/roles/${roleId}`);
      message.success('角色删除成功');
      await loadRoles();
      
    } catch (error) {
      console.error('删除角色失败:', error);
      message.error(error.response?.data?.detail || '删除角色失败');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 查看角色详情
   */
  const viewRoleDetails = async (roleId) => {
    try {
      const response = await apiService.get(`/api/v1/roles/${roleId}`);
      if (response.data?.role) {
        setSelectedRole(response.data.role);
        setRoleDetailModalVisible(true);
      }
    } catch (error) {
      console.error('获取角色详情失败:', error);
      message.error('获取角色详情失败');
    }
  };

  /**
   * 用户表格列定义
   */
  const userColumns = [
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      render: (text) => <strong>{text}</strong>
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email'
    },
    {
      title: '真实姓名',
      dataIndex: 'full_name',
      key: 'full_name'
    },
    {
      title: '角色',
      dataIndex: 'roles',
      key: 'roles',
      render: (roles) => (
        <Space>
          {roles.map(role => (
            <Tag key={role} color="blue">{role}</Tag>
          ))}
        </Space>
      )
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '激活' : '禁用'}
        </Tag>
      )
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button 
            type="link" 
            icon={<SettingOutlined />}
            onClick={() => {
              setSelectedUser(record);
              setUserRoleModalVisible(true);
            }}
          >
            管理角色
          </Button>
        </Space>
      )
    }
  ];

  /**
   * 角色表格列定义
   */
  const roleColumns = [
    {
      title: '角色ID',
      dataIndex: 'id',
      key: 'id',
      render: (text) => <code>{text}</code>
    },
    {
      title: '角色名称',
      dataIndex: 'name',
      key: 'name',
      render: (text) => <strong>{text}</strong>
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: '权限数量',
      dataIndex: 'permissions',
      key: 'permissions',
      render: (permissions) => (
        <Tag color="blue">{permissions.length} 个权限</Tag>
      )
    },
    {
      title: '类型',
      dataIndex: 'is_system_role',
      key: 'is_system_role',
      render: (isSystemRole) => (
        <Tag color={isSystemRole ? 'orange' : 'green'}>
          {isSystemRole ? '系统角色' : '自定义角色'}
        </Tag>
      )
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button 
            type="link" 
            icon={<EyeOutlined />}
            onClick={() => viewRoleDetails(record.id)}
          >
            查看
          </Button>
          {!record.is_system_role && (
            <>
              <Button 
                type="link" 
                icon={<EditOutlined />}
                onClick={() => {
                  setEditingRole(record);
                  roleForm.setFieldsValue(record);
                  setRoleModalVisible(true);
                }}
              >
                编辑
              </Button>
              <Popconfirm
                title="确定要删除这个角色吗？"
                onConfirm={() => deleteRole(record.id)}
                okText="确定"
                cancelText="取消"
              >
                <Button 
                  type="link" 
                  danger 
                  icon={<DeleteOutlined />}
                >
                  删除
                </Button>
              </Popconfirm>
            </>
          )}
        </Space>
      )
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card title="权限管理" extra={
        <Button 
          type="primary" 
          icon={<PlusOutlined />}
          onClick={() => {
            setEditingRole(null);
            roleForm.resetFields();
            setRoleModalVisible(true);
          }}
        >
          创建角色
        </Button>
      }>
        <Tabs defaultActiveKey="users">
          <TabPane 
            tab={<span><UserOutlined />用户管理</span>} 
            key="users"
          >
            <Table
              columns={userColumns}
              dataSource={users}
              rowKey="id"
              loading={loading}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 个用户`
              }}
            />
          </TabPane>
          
          <TabPane 
            tab={<span><SafetyOutlined />角色管理</span>} 
            key="roles"
          >
            <Table
              columns={roleColumns}
              dataSource={roles}
              rowKey="id"
              loading={loading}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 个角色`
              }}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* 用户角色管理模态框 */}
      <Modal
        title="管理用户角色"
        visible={userRoleModalVisible}
        onCancel={() => {
          setUserRoleModalVisible(false);
          userRoleForm.resetFields();
        }}
        footer={null}
        width={600}
      >
        {selectedUser && (
          <div>
            <Descriptions title="用户信息" bordered size="small">
              <Descriptions.Item label="用户名">{selectedUser.username}</Descriptions.Item>
              <Descriptions.Item label="邮箱">{selectedUser.email}</Descriptions.Item>
              <Descriptions.Item label="真实姓名">{selectedUser.full_name}</Descriptions.Item>
            </Descriptions>
            
            <div style={{ marginTop: '16px' }}>
              <h4>当前角色</h4>
              <Space wrap>
                {selectedUser.roles.map(role => (
                  <Tag 
                    key={role} 
                    color="blue" 
                    closable
                    onClose={() => revokeUserRole(selectedUser.id, role)}
                  >
                    {role}
                  </Tag>
                ))}
              </Space>
            </div>
            
            <Form
              form={userRoleForm}
              layout="vertical"
              onFinish={assignRoleToUser}
              style={{ marginTop: '16px' }}
            >
              <Form.Item name="user_id" initialValue={selectedUser.id} hidden>
                <Input />
              </Form.Item>
              
              <Form.Item
                name="role_id"
                label="分配角色"
                rules={[{ required: true, message: '请选择角色' }]}
              >
                <Select placeholder="选择要分配的角色">
                  {roles.filter(role => !selectedUser.roles.includes(role.id)).map(role => (
                    <Option key={role.id} value={role.id}>
                      {role.name} - {role.description}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
              
              <Form.Item>
                <Button type="primary" htmlType="submit" loading={loading}>
                  分配角色
                </Button>
              </Form.Item>
            </Form>
          </div>
        )}
      </Modal>

      {/* 角色创建/编辑模态框 */}
      <Modal
        title={editingRole ? '编辑角色' : '创建角色'}
        visible={roleModalVisible}
        onCancel={() => {
          setRoleModalVisible(false);
          roleForm.resetFields();
          setEditingRole(null);
        }}
        footer={null}
        width={800}
      >
        <Form
          form={roleForm}
          layout="vertical"
          onFinish={saveRole}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="id"
                label="角色ID"
                rules={[
                  { required: true, message: '请输入角色ID' },
                  { pattern: /^[a-z_]+$/, message: '角色ID只能包含小写字母和下划线' }
                ]}
              >
                <Input placeholder="例如: custom_trader" disabled={!!editingRole} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="name"
                label="角色名称"
                rules={[{ required: true, message: '请输入角色名称' }]}
              >
                <Input placeholder="例如: 自定义交易员" />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item
            name="description"
            label="角色描述"
            rules={[{ required: true, message: '请输入角色描述' }]}
          >
            <Input.TextArea rows={3} placeholder="描述角色的职责和用途" />
          </Form.Item>
          
          <Form.Item
            name="permissions"
            label="权限配置"
            rules={[{ required: true, message: '请选择权限' }]}
          >
            <Select
              mode="multiple"
              placeholder="选择角色权限"
              optionFilterProp="children"
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {permissions.map(permission => (
                <Option key={permission.id} value={permission.id}>
                  {permission.name} - {permission.description}
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                {editingRole ? '更新角色' : '创建角色'}
              </Button>
              <Button onClick={() => {
                setRoleModalVisible(false);
                roleForm.resetFields();
                setEditingRole(null);
              }}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 角色详情模态框 */}
      <Modal
        title="角色详情"
        visible={roleDetailModalVisible}
        onCancel={() => setRoleDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setRoleDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={700}
      >
        {selectedRole && (
          <div>
            <Descriptions title="基本信息" bordered>
              <Descriptions.Item label="角色ID">{selectedRole.id}</Descriptions.Item>
              <Descriptions.Item label="角色名称">{selectedRole.name}</Descriptions.Item>
              <Descriptions.Item label="类型">
                {selectedRole.is_system_role ? '系统角色' : '自定义角色'}
              </Descriptions.Item>
              <Descriptions.Item label="状态" span={3}>
                {selectedRole.is_active ? '激活' : '禁用'}
              </Descriptions.Item>
              <Descriptions.Item label="描述" span={3}>
                {selectedRole.description}
              </Descriptions.Item>
            </Descriptions>
            
            <div style={{ marginTop: '16px' }}>
              <h4>权限列表</h4>
              <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
                <Space direction="vertical" style={{ width: '100%' }}>
                  {selectedRole.permissions.map(permissionId => {
                    const permission = permissions.find(p => p.id === permissionId);
                    return permission ? (
                      <Card key={permissionId} size="small">
                        <div>
                          <strong>{permission.name}</strong>
                          <Tag color="blue" style={{ marginLeft: '8px' }}>
                            {permission.resource_type}:{permission.action}
                          </Tag>
                        </div>
                        <div style={{ color: '#666', fontSize: '12px', marginTop: '4px' }}>
                          {permission.description}
                        </div>
                      </Card>
                    ) : (
                      <Tag key={permissionId} color="red">
                        {permissionId} (权限不存在)
                      </Tag>
                    );
                  })}
                </Space>
              </div>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default PermissionManagement;
