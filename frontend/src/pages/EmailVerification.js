import React, { useState, useEffect } from 'react';
import { Card, Button, Result, Spin, message, Input, Form } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined, MailOutlined, ReloadOutlined } from '@ant-design/icons';
import { apiService } from '../services/api';

/**
 * 邮箱验证页面组件
 * 
 * 功能特性：
 * - 自动验证URL中的token参数
 * - 显示验证结果状态
 * - 支持重新发送验证邮件
 * - 验证成功后跳转到登录页面
 */
const EmailVerification = () => {
  const [verificationStatus, setVerificationStatus] = useState('loading'); // loading, success, error
  const [loading, setLoading] = useState(false);
  const [resendLoading, setResendLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [showResendForm, setShowResendForm] = useState(false);

  /**
   * 从URL参数中获取验证token
   */
  const getTokenFromUrl = () => {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('token');
  };

  /**
   * 验证邮箱token
   */
  const verifyEmailToken = async (token) => {
    try {
      setLoading(true);
      
      const response = await apiService.post('/api/v1/users/verify-email', {
        token: token
      });
      
      if (response.data?.verified) {
        setVerificationStatus('success');
        message.success('邮箱验证成功！');
        
        // 3秒后自动跳转到登录页面
        setTimeout(() => {
          window.location.href = '/login';
        }, 3000);
      }
      
    } catch (error) {
      console.error('邮箱验证失败:', error);
      setVerificationStatus('error');
      
      if (error.response?.data?.detail) {
        setErrorMessage(error.response.data.detail);
      } else {
        setErrorMessage('验证失败，请重试');
      }
    } finally {
      setLoading(false);
    }
  };

  /**
   * 重新发送验证邮件
   */
  const resendVerificationEmail = async (values) => {
    try {
      setResendLoading(true);
      
      const response = await apiService.post('/api/v1/users/resend-verification', {
        email: values.email
      });
      
      if (response.data) {
        message.success('验证邮件已重新发送，请查收邮箱');
        setShowResendForm(false);
      }
      
    } catch (error) {
      console.error('重新发送验证邮件失败:', error);
      
      if (error.response?.data?.detail) {
        message.error(error.response.data.detail);
      } else {
        message.error('发送失败，请稍后重试');
      }
    } finally {
      setResendLoading(false);
    }
  };

  /**
   * 组件挂载时自动验证token
   */
  useEffect(() => {
    const token = getTokenFromUrl();
    
    if (token) {
      verifyEmailToken(token);
    } else {
      setVerificationStatus('error');
      setErrorMessage('缺少验证参数，请检查验证链接是否完整');
    }
  }, []);

  /**
   * 渲染加载状态
   */
  const renderLoading = () => (
    <div style={{
      height: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center'
    }}>
      <Card style={{ width: 400, textAlign: 'center' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16, fontSize: '16px' }}>
          正在验证邮箱...
        </div>
      </Card>
    </div>
  );

  /**
   * 渲染验证成功状态
   */
  const renderSuccess = () => (
    <div style={{
      height: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center'
    }}>
      <Card style={{ width: 450, textAlign: 'center' }}>
        <Result
          icon={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
          title="邮箱验证成功！"
          subTitle="您的邮箱已成功验证，现在可以正常使用所有功能了。"
          extra={[
            <Button 
              type="primary" 
              key="login"
              onClick={() => window.location.href = '/login'}
            >
              立即登录
            </Button>
          ]}
        />
        <div style={{ 
          color: '#666', 
          fontSize: '14px',
          marginTop: '16px'
        }}>
          页面将在3秒后自动跳转到登录页面...
        </div>
      </Card>
    </div>
  );

  /**
   * 渲染验证失败状态
   */
  const renderError = () => (
    <div style={{
      height: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center'
    }}>
      <Card style={{ width: 450, textAlign: 'center' }}>
        <Result
          icon={<CloseCircleOutlined style={{ color: '#ff4d4f' }} />}
          title="邮箱验证失败"
          subTitle={errorMessage || '验证链接无效或已过期'}
          extra={[
            <Button 
              key="resend"
              icon={<ReloadOutlined />}
              onClick={() => setShowResendForm(true)}
            >
              重新发送验证邮件
            </Button>,
            <Button 
              type="primary" 
              key="login"
              onClick={() => window.location.href = '/login'}
            >
              返回登录
            </Button>
          ]}
        />
        
        {/* 重新发送验证邮件表单 */}
        {showResendForm && (
          <div style={{ 
            marginTop: '24px',
            padding: '20px',
            backgroundColor: '#f6f8fa',
            borderRadius: '8px',
            textAlign: 'left'
          }}>
            <h4 style={{ marginBottom: '16px' }}>重新发送验证邮件</h4>
            <Form onFinish={resendVerificationEmail} layout="vertical">
              <Form.Item
                name="email"
                label="邮箱地址"
                rules={[
                  { required: true, message: '请输入邮箱地址' },
                  { type: 'email', message: '请输入有效的邮箱地址' }
                ]}
              >
                <Input 
                  prefix={<MailOutlined />}
                  placeholder="请输入注册时使用的邮箱地址"
                />
              </Form.Item>
              
              <Form.Item style={{ marginBottom: 0 }}>
                <div style={{ display: 'flex', gap: '8px' }}>
                  <Button 
                    type="primary" 
                    htmlType="submit"
                    loading={resendLoading}
                    style={{ flex: 1 }}
                  >
                    发送验证邮件
                  </Button>
                  <Button 
                    onClick={() => setShowResendForm(false)}
                    style={{ flex: 1 }}
                  >
                    取消
                  </Button>
                </div>
              </Form.Item>
            </Form>
          </div>
        )}
        
        {/* 帮助信息 */}
        <div style={{ 
          marginTop: '20px',
          padding: '16px',
          backgroundColor: '#e6f7ff',
          borderRadius: '6px',
          fontSize: '13px',
          color: '#666'
        }}>
          <p style={{ margin: 0 }}>
            <strong>验证失败的可能原因：</strong>
          </p>
          <ul style={{ margin: '8px 0 0 0', paddingLeft: '20px' }}>
            <li>验证链接已过期（有效期24小时）</li>
            <li>验证链接已被使用</li>
            <li>验证链接格式不正确</li>
          </ul>
        </div>
      </Card>
    </div>
  );

  // 根据验证状态渲染不同内容
  if (loading || verificationStatus === 'loading') {
    return renderLoading();
  }
  
  if (verificationStatus === 'success') {
    return renderSuccess();
  }
  
  return renderError();
};

export default EmailVerification;
