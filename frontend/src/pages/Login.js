import React, { useState } from 'react';
import { Form, Input, Button, Card, message, Spin, Checkbox } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined } from '@ant-design/icons';
import { apiService } from '../services/api';

/**
 * 登录页面组件
 *
 * 功能特性：
 * - 用户名/密码登录
 * - 记住登录状态功能
 * - 自动填充上次登录信息
 * - 登录状态持久化
 * - 响应式设计
 */
const Login = ({ onLogin }) => {
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  /**
   * 组件挂载时恢复保存的登录信息
   */
  React.useEffect(() => {
    const savedUsername = localStorage.getItem('saved_username');
    const rememberMe = localStorage.getItem('remember_me') === 'true';

    if (savedUsername && rememberMe) {
      form.setFieldsValue({
        username: savedUsername,
        remember: true
      });
    }
  }, [form]);

  /**
   * 处理登录表单提交
   * @param {Object} values - 表单数据
   */
  const onFinish = async (values) => {
    try {
      setLoading(true);

      // 调用登录API
      const response = await apiService.login({
        username: values.username,
        password: values.password,
        remember_me: values.remember || false
      });

      if (response.data) {
        const { access_token, token_type, expires_in, refresh_token } = response.data;

        // 保存访问令牌
        localStorage.setItem('token', access_token);
        localStorage.setItem('token_type', token_type);
        localStorage.setItem('token_expires_in', expires_in.toString());

        // 如果有刷新令牌，也保存起来
        if (refresh_token) {
          localStorage.setItem('refresh_token', refresh_token);
        }

        // 处理记住登录状态
        if (values.remember) {
          localStorage.setItem('saved_username', values.username);
          localStorage.setItem('remember_me', 'true');

          // 设置长期过期时间（30天）
          const longTermExpiry = new Date();
          longTermExpiry.setDate(longTermExpiry.getDate() + 30);
          localStorage.setItem('long_term_expiry', longTermExpiry.toISOString());
        } else {
          // 清除保存的登录信息
          localStorage.removeItem('saved_username');
          localStorage.removeItem('remember_me');
          localStorage.removeItem('long_term_expiry');
        }

        // 获取用户信息
        try {
          const userResponse = await apiService.getUserInfo();
          if (userResponse.data) {
            localStorage.setItem('user', JSON.stringify(userResponse.data));
          }
        } catch (userError) {
          console.warn('获取用户信息失败:', userError);
        }

        message.success('登录成功');

        // 通知父组件登录成功
        if (onLogin) {
          onLogin();
        }
      }

    } catch (error) {
      console.error('登录失败:', error);

      // 处理不同类型的错误
      if (error.response?.data?.detail) {
        message.error(error.response.data.detail);
      } else if (error.response?.status === 401) {
        message.error('用户名或密码错误');
      } else if (error.response?.status === 403) {
        message.error('账号已被禁用，请联系管理员');
      } else {
        message.error('登录失败，请稍后重试');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{
      height: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      padding: '20px'
    }}>
      <Card
        title={
          <div style={{ textAlign: 'center' }}>
            <MailOutlined style={{ fontSize: '24px', marginRight: '8px' }} />
            AI量化交易工具
          </div>
        }
        style={{
          width: '100%',
          maxWidth: 400,
          boxShadow: '0 8px 24px rgba(0,0,0,0.15)',
          borderRadius: '8px'
        }}
      >
        <Form
          form={form}
          name="login"
          onFinish={onFinish}
          autoComplete="off"
          size="large"
          layout="vertical"
        >
          {/* 用户名输入 */}
          <Form.Item
            name="username"
            label="用户名"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="请输入用户名"
              autoComplete="username"
            />
          </Form.Item>

          {/* 密码输入 */}
          <Form.Item
            name="password"
            label="密码"
            rules={[{ required: true, message: '请输入密码' }]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="请输入密码"
              autoComplete="current-password"
            />
          </Form.Item>

          {/* 记住登录状态 */}
          <Form.Item name="remember" valuePropName="checked">
            <Checkbox>记住登录状态</Checkbox>
          </Form.Item>

          {/* 登录按钮 */}
          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              style={{ width: '100%', height: '45px' }}
            >
              {loading ? <Spin size="small" /> : '登录'}
            </Button>
          </Form.Item>
        </Form>

        {/* 注册和忘记密码链接 */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          marginTop: '16px'
        }}>
          <Button type="link" onClick={() => window.location.href = '/register'}>
            注册新账号
          </Button>
          <Button type="link" onClick={() => message.info('请联系管理员重置密码')}>
            忘记密码？
          </Button>
        </div>

        {/* 提示信息 */}
        <div style={{
          textAlign: 'center',
          color: '#666',
          fontSize: '12px',
          marginTop: '20px',
          padding: '12px',
          backgroundColor: '#f6f8fa',
          borderRadius: '4px'
        }}>
          <p style={{ margin: 0 }}>
            🔒 选择"记住登录状态"可在30天内免登录
          </p>
          <p style={{ margin: '4px 0 0 0' }}>
            首次登录请先注册账号并完成邮箱验证
          </p>
        </div>
      </Card>
    </div>
  );
};

export default Login;
