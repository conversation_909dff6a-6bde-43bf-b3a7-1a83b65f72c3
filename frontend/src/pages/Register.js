import React, { useState } from 'react';
import { Form, Input, Button, Card, message, Spin, Checkbox } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined, UserAddOutlined } from '@ant-design/icons';
import { apiService } from '../services/api';

/**
 * 用户注册页面组件
 * 
 * 功能特性：
 * - 用户信息输入和验证
 * - 密码强度检查
 * - 邮箱格式验证
 * - 注册成功后自动发送验证邮件
 * - 响应式设计
 */
const Register = ({ onRegisterSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  /**
   * 处理用户注册表单提交
   * @param {Object} values - 表单数据
   */
  const onFinish = async (values) => {
    try {
      setLoading(true);
      
      // 调用注册API
      const response = await apiService.post('/api/v1/users/register', {
        username: values.username,
        email: values.email,
        password: values.password,
        full_name: values.fullName
      });
      
      if (response.data) {
        message.success('注册成功！验证邮件已发送到您的邮箱，请查收并完成验证。');
        
        // 清空表单
        form.resetFields();
        
        // 回调通知父组件注册成功
        if (onRegisterSuccess) {
          onRegisterSuccess(response.data);
        }
      }
      
    } catch (error) {
      console.error('注册失败:', error);
      
      // 处理不同类型的错误
      if (error.response?.data?.detail) {
        message.error(error.response.data.detail);
      } else if (error.response?.status === 400) {
        message.error('注册信息有误，请检查后重试');
      } else {
        message.error('注册失败，请稍后重试');
      }
    } finally {
      setLoading(false);
    }
  };

  /**
   * 验证密码强度
   * @param {string} password - 密码
   * @returns {boolean} - 是否符合强度要求
   */
  const validatePassword = (password) => {
    if (!password) return false;
    
    // 密码强度要求：至少8位，包含字母和数字
    const hasLetter = /[a-zA-Z]/.test(password);
    const hasNumber = /\d/.test(password);
    const hasMinLength = password.length >= 8;
    
    return hasLetter && hasNumber && hasMinLength;
  };

  /**
   * 自定义密码验证规则
   */
  const passwordValidator = (_, value) => {
    if (!value) {
      return Promise.reject(new Error('请输入密码'));
    }
    
    if (!validatePassword(value)) {
      return Promise.reject(new Error('密码至少8位，需包含字母和数字'));
    }
    
    return Promise.resolve();
  };

  /**
   * 确认密码验证规则
   */
  const confirmPasswordValidator = (_, value) => {
    if (!value) {
      return Promise.reject(new Error('请确认密码'));
    }
    
    if (value !== form.getFieldValue('password')) {
      return Promise.reject(new Error('两次输入的密码不一致'));
    }
    
    return Promise.resolve();
  };

  return (
    <div style={{
      height: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      padding: '20px'
    }}>
      <Card
        title={
          <div style={{ textAlign: 'center' }}>
            <UserAddOutlined style={{ fontSize: '24px', marginRight: '8px' }} />
            用户注册
          </div>
        }
        style={{ 
          width: '100%', 
          maxWidth: 450, 
          boxShadow: '0 8px 24px rgba(0,0,0,0.15)',
          borderRadius: '8px'
        }}
      >
        <Form
          form={form}
          name="register"
          onFinish={onFinish}
          autoComplete="off"
          size="large"
          layout="vertical"
        >
          {/* 用户名输入 */}
          <Form.Item
            name="username"
            label="用户名"
            rules={[
              { required: true, message: '请输入用户名' },
              { min: 3, message: '用户名至少3个字符' },
              { max: 20, message: '用户名最多20个字符' },
              { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' }
            ]}
          >
            <Input 
              prefix={<UserOutlined />} 
              placeholder="请输入用户名（3-20个字符）" 
            />
          </Form.Item>

          {/* 邮箱输入 */}
          <Form.Item
            name="email"
            label="邮箱地址"
            rules={[
              { required: true, message: '请输入邮箱地址' },
              { type: 'email', message: '请输入有效的邮箱地址' }
            ]}
          >
            <Input 
              prefix={<MailOutlined />} 
              placeholder="请输入邮箱地址" 
            />
          </Form.Item>

          {/* 真实姓名输入（可选） */}
          <Form.Item
            name="fullName"
            label="真实姓名（可选）"
            rules={[
              { max: 50, message: '姓名最多50个字符' }
            ]}
          >
            <Input 
              prefix={<UserOutlined />} 
              placeholder="请输入真实姓名（可选）" 
            />
          </Form.Item>

          {/* 密码输入 */}
          <Form.Item
            name="password"
            label="密码"
            rules={[{ validator: passwordValidator }]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="请输入密码（至少8位，包含字母和数字）"
            />
          </Form.Item>

          {/* 确认密码 */}
          <Form.Item
            name="confirmPassword"
            label="确认密码"
            dependencies={['password']}
            rules={[{ validator: confirmPasswordValidator }]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="请再次输入密码"
            />
          </Form.Item>

          {/* 用户协议 */}
          <Form.Item
            name="agreement"
            valuePropName="checked"
            rules={[
              { 
                validator: (_, value) => 
                  value ? Promise.resolve() : Promise.reject(new Error('请阅读并同意用户协议'))
              }
            ]}
          >
            <Checkbox>
              我已阅读并同意 <a href="#" onClick={(e) => e.preventDefault()}>《用户协议》</a> 和 <a href="#" onClick={(e) => e.preventDefault()}>《隐私政策》</a>
            </Checkbox>
          </Form.Item>

          {/* 注册按钮 */}
          <Form.Item>
            <Button 
              type="primary" 
              htmlType="submit" 
              loading={loading}
              style={{ width: '100%', height: '45px' }}
            >
              {loading ? <Spin size="small" /> : '注册账号'}
            </Button>
          </Form.Item>
        </Form>
        
        {/* 登录链接 */}
        <div style={{ textAlign: 'center', marginTop: '16px' }}>
          <span style={{ color: '#666' }}>已有账号？</span>
          <Button type="link" onClick={() => window.location.href = '/login'}>
            立即登录
          </Button>
        </div>
        
        {/* 提示信息 */}
        <div style={{ 
          textAlign: 'center', 
          color: '#666', 
          fontSize: '12px',
          marginTop: '16px',
          padding: '12px',
          backgroundColor: '#f6f8fa',
          borderRadius: '4px'
        }}>
          <p style={{ margin: 0 }}>
            📧 注册成功后，系统将向您的邮箱发送验证邮件
          </p>
          <p style={{ margin: '4px 0 0 0' }}>
            请及时查收并完成邮箱验证以激活账号
          </p>
        </div>
      </Card>
    </div>
  );
};

export default Register;
