import React, { useState, useRef, useEffect } from 'react';
import { Card, Button, Space, message, Modal, Form, Input, Select, Tooltip, Divider, Dropdown, Menu } from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  SaveOutlined,
  SettingOutlined,
  BugOutlined,
  FormatPainterOutlined,
  FileTextOutlined,
  QuestionCircleOutlined,
  DownOutlined,
  CodeOutlined
} from '@ant-design/icons';
import Editor from '@monaco-editor/react';
import { configurePythonSyntaxHighlighting, getPythonCodeTemplates } from '../utils/pythonSyntax';
import { configureAutoCompletion } from '../utils/autoCompletion';
import { configureSyntaxValidation } from '../utils/syntaxValidator';
import { configureCodeFormatter, formatPythonCode } from '../utils/codeFormatter';

const { Option } = Select;

/**
 * 策略编辑器组件
 * 
 * 功能特性：
 * - Monaco Editor集成
 * - Python语法高亮
 * - 代码自动补全
 * - 语法错误检查
 * - 代码格式化
 * - 策略参数配置
 * - 实时保存
 * - 调试功能
 */
const StrategyEditor = ({ 
  strategyId, 
  initialCode = '', 
  initialParams = {},
  onSave,
  onRun,
  onStop,
  readOnly = false 
}) => {
  const [code, setCode] = useState(initialCode);
  const [params, setParams] = useState(initialParams);
  const [loading, setLoading] = useState(false);
  const [isRunning, setIsRunning] = useState(false);
  const [paramModalVisible, setParamModalVisible] = useState(false);
  const [helpModalVisible, setHelpModalVisible] = useState(false);
  
  const editorRef = useRef(null);
  const [paramForm] = Form.useForm();

  /**
   * 编辑器挂载时的配置
   */
  const handleEditorDidMount = (editor, monaco) => {
    editorRef.current = editor;

    // 配置Python语法高亮
    configurePythonSyntaxHighlighting(monaco);

    // 配置自动补全
    configureAutoCompletion(monaco);

    // 配置语法检查
    configureSyntaxValidation(monaco);

    // 配置代码格式化
    configureCodeFormatter(monaco);

    // 添加快捷键
    addKeyboardShortcuts(editor, monaco);
  };

  /**
   * 插入代码模板
   */
  const insertTemplate = (templateKey) => {
    const templates = getPythonCodeTemplates();
    const template = templates[templateKey];

    if (template && editorRef.current) {
      const editor = editorRef.current;
      const selection = editor.getSelection();
      const range = selection || {
        startLineNumber: 1,
        startColumn: 1,
        endLineNumber: 1,
        endColumn: 1
      };

      editor.executeEdits('insert-template', [{
        range: range,
        text: template.code
      }]);

      editor.focus();
      message.success(`已插入${template.label}模板`);
    }
  };





  /**
   * 添加键盘快捷键
   */
  const addKeyboardShortcuts = (editor, monaco) => {
    // Ctrl+S 保存
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
      handleSave();
    });
    
    // Ctrl+Shift+F 格式化代码
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyMod.Shift | monaco.KeyCode.KeyF, () => {
      formatCode();
    });
    
    // F5 运行策略
    editor.addCommand(monaco.KeyCode.F5, () => {
      handleRun();
    });
    
    // Shift+F5 停止策略
    editor.addCommand(monaco.KeyMod.Shift | monaco.KeyCode.F5, () => {
      handleStop();
    });
  };

  /**
   * 代码变化处理
   */
  const handleCodeChange = (value) => {
    setCode(value);
    
    // 自动保存（防抖）
    if (autoSaveTimer.current) {
      clearTimeout(autoSaveTimer.current);
    }
    
    autoSaveTimer.current = setTimeout(() => {
      if (onSave && !readOnly) {
        onSave(value, params);
      }
    }, 2000); // 2秒后自动保存
  };

  const autoSaveTimer = useRef(null);

  /**
   * 保存策略
   */
  const handleSave = async () => {
    if (readOnly) {
      message.warning('只读模式，无法保存');
      return;
    }
    
    try {
      setLoading(true);
      if (onSave) {
        await onSave(code, params);
        message.success('策略保存成功');
      }
    } catch (error) {
      console.error('保存策略失败:', error);
      message.error('保存策略失败');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 运行策略
   */
  const handleRun = async () => {
    if (readOnly) {
      message.warning('只读模式，无法运行');
      return;
    }
    
    try {
      setLoading(true);
      if (onRun) {
        await onRun(code, params);
        setIsRunning(true);
        message.success('策略启动成功');
      }
    } catch (error) {
      console.error('运行策略失败:', error);
      message.error('运行策略失败');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 停止策略
   */
  const handleStop = async () => {
    try {
      setLoading(true);
      if (onStop) {
        await onStop();
        setIsRunning(false);
        message.success('策略已停止');
      }
    } catch (error) {
      console.error('停止策略失败:', error);
      message.error('停止策略失败');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 格式化代码
   */
  const formatCode = () => {
    if (editorRef.current) {
      try {
        // 使用Monaco Editor内置的格式化功能
        editorRef.current.getAction('editor.action.formatDocument').run();
        message.success('代码格式化完成');
      } catch (error) {
        // 如果内置格式化失败，使用自定义格式化器
        try {
          const currentCode = editorRef.current.getValue();
          const formattedCode = formatPythonCode(currentCode);
          editorRef.current.setValue(formattedCode);
          message.success('代码格式化完成');
        } catch (formatError) {
          console.error('代码格式化失败:', formatError);
          message.error('代码格式化失败');
        }
      }
    }
  };

  /**
   * 配置策略参数
   */
  const handleParamConfig = () => {
    paramForm.setFieldsValue(params);
    setParamModalVisible(true);
  };

  /**
   * 保存策略参数
   */
  const handleParamSave = (values) => {
    setParams(values);
    setParamModalVisible(false);
    message.success('参数配置已保存');
  };

  // 清理定时器
  useEffect(() => {
    return () => {
      if (autoSaveTimer.current) {
        clearTimeout(autoSaveTimer.current);
      }
    };
  }, []);

  return (
    <Card
      title="策略编辑器"
      extra={
        <Space>
          <Tooltip title="代码模板">
            <Dropdown
              overlay={
                <Menu onClick={({ key }) => insertTemplate(key)}>
                  <Menu.Item key="strategy_basic" icon={<CodeOutlined />}>
                    基础策略模板
                  </Menu.Item>
                  <Menu.Item key="ma_strategy" icon={<CodeOutlined />}>
                    移动平均策略
                  </Menu.Item>
                  <Menu.Item key="rsi_strategy" icon={<CodeOutlined />}>
                    RSI策略
                  </Menu.Item>
                </Menu>
              }
              disabled={readOnly}
            >
              <Button icon={<FileTextOutlined />}>
                模板 <DownOutlined />
              </Button>
            </Dropdown>
          </Tooltip>

          <Tooltip title="策略参数配置">
            <Button
              icon={<SettingOutlined />}
              onClick={handleParamConfig}
              disabled={readOnly}
            >
              参数
            </Button>
          </Tooltip>

          <Tooltip title="格式化代码 (Ctrl+Shift+F)">
            <Button
              icon={<FormatPainterOutlined />}
              onClick={formatCode}
            >
              格式化
            </Button>
          </Tooltip>

          <Tooltip title="策略帮助文档">
            <Button
              icon={<QuestionCircleOutlined />}
              onClick={() => setHelpModalVisible(true)}
            >
              帮助
            </Button>
          </Tooltip>

          <Divider type="vertical" />

          <Tooltip title="保存策略 (Ctrl+S)">
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={handleSave}
              loading={loading}
              disabled={readOnly}
            >
              保存
            </Button>
          </Tooltip>

          {!isRunning ? (
            <Tooltip title="运行策略 (F5)">
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                onClick={handleRun}
                loading={loading}
                disabled={readOnly}
                style={{ backgroundColor: '#52c41a', borderColor: '#52c41a' }}
              >
                运行
              </Button>
            </Tooltip>
          ) : (
            <Tooltip title="停止策略 (Shift+F5)">
              <Button
                danger
                icon={<PauseCircleOutlined />}
                onClick={handleStop}
                loading={loading}
              >
                停止
              </Button>
            </Tooltip>
          )}
        </Space>
      }
      bodyStyle={{ padding: 0 }}
    >
      <div style={{ height: '600px', border: '1px solid #d9d9d9' }}>
        <Editor
          height="100%"
          defaultLanguage="python"
          value={code}
          onChange={handleCodeChange}
          onMount={handleEditorDidMount}
          options={{
            theme: 'pythonTheme',
            fontSize: 14,
            fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
            lineNumbers: 'on',
            roundedSelection: false,
            scrollBeyondLastLine: false,
            automaticLayout: true,
            minimap: { enabled: true },
            wordWrap: 'on',
            tabSize: 4,
            insertSpaces: true,
            detectIndentation: false,
            folding: true,
            foldingStrategy: 'indentation',
            showFoldingControls: 'always',
            unfoldOnClickAfterEndOfLine: false,
            contextmenu: true,
            mouseWheelZoom: true,
            readOnly: readOnly,
            // 启用智能提示
            quickSuggestions: {
              other: true,
              comments: false,
              strings: false
            },
            parameterHints: {
              enabled: true
            },
            suggestOnTriggerCharacters: true,
            acceptSuggestionOnEnter: 'on',
            tabCompletion: 'on',
            wordBasedSuggestions: true,
            // 启用语法检查
            renderValidationDecorations: 'on'
          }}
        />
      </div>

      {/* 策略参数配置模态框 */}
      <Modal
        title="策略参数配置"
        visible={paramModalVisible}
        onCancel={() => setParamModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={paramForm}
          layout="vertical"
          onFinish={handleParamSave}
          initialValues={params}
        >
          <Form.Item
            name="symbol"
            label="交易标的"
            rules={[{ required: true, message: '请输入交易标的' }]}
          >
            <Input placeholder="例如: 000001.SZ" />
          </Form.Item>
          
          <Form.Item
            name="capital"
            label="初始资金"
            rules={[{ required: true, message: '请输入初始资金' }]}
          >
            <Input type="number" placeholder="例如: 100000" addonAfter="元" />
          </Form.Item>
          
          <Form.Item
            name="benchmark"
            label="基准标的"
          >
            <Input placeholder="例如: 000300.SH" />
          </Form.Item>
          
          <Form.Item
            name="frequency"
            label="运行频率"
            rules={[{ required: true, message: '请选择运行频率' }]}
          >
            <Select placeholder="选择运行频率">
              <Option value="daily">日频</Option>
              <Option value="minute">分钟频</Option>
              <Option value="tick">tick频</Option>
            </Select>
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                保存配置
              </Button>
              <Button onClick={() => setParamModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 帮助文档模态框 */}
      <Modal
        title="策略开发帮助"
        visible={helpModalVisible}
        onCancel={() => setHelpModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setHelpModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        <div style={{ maxHeight: '500px', overflowY: 'auto' }}>
          <h3>策略开发指南</h3>
          
          <h4>1. 基本结构</h4>
          <pre style={{ backgroundColor: '#f6f8fa', padding: '12px', borderRadius: '4px' }}>
{`def initialize(context):
    """策略初始化函数"""
    pass

def handle_data(context, data):
    """策略主逻辑函数"""
    pass

def before_trading_start(context, data):
    """开盘前调用函数"""
    pass`}
          </pre>
          
          <h4>2. 常用API函数</h4>
          <ul>
            <li><code>order_shares(symbol, shares)</code> - 按股数下单</li>
            <li><code>order_value(symbol, value)</code> - 按金额下单</li>
            <li><code>get_current_price(symbol)</code> - 获取当前价格</li>
            <li><code>get_history(symbols, fields, bar_count)</code> - 获取历史数据</li>
          </ul>
          
          <h4>3. 技术指标</h4>
          <ul>
            <li><code>sma(data, window)</code> - 简单移动平均线</li>
            <li><code>ema(data, window)</code> - 指数移动平均线</li>
            <li><code>rsi(data, window)</code> - 相对强弱指数</li>
            <li><code>macd(data, fast, slow, signal)</code> - MACD指标</li>
          </ul>
          
          <h4>4. 快捷键</h4>
          <ul>
            <li><kbd>Ctrl+S</kbd> - 保存策略</li>
            <li><kbd>Ctrl+Shift+F</kbd> - 格式化代码</li>
            <li><kbd>F5</kbd> - 运行策略</li>
            <li><kbd>Shift+F5</kbd> - 停止策略</li>
          </ul>
          
          <h4>5. 示例策略</h4>
          <pre style={{ backgroundColor: '#f6f8fa', padding: '12px', borderRadius: '4px' }}>
{`def initialize(context):
    # 设置交易标的
    context.symbol = '000001.SZ'
    # 设置移动平均线周期
    context.short_window = 5
    context.long_window = 20

def handle_data(context, data):
    # 获取历史价格数据
    prices = get_history([context.symbol], 'close', context.long_window)
    
    # 计算移动平均线
    short_ma = sma(prices[context.symbol], context.short_window)
    long_ma = sma(prices[context.symbol], context.long_window)
    
    # 交易逻辑
    if short_ma[-1] > long_ma[-1]:
        # 金叉买入
        order_value(context.symbol, 10000)
    elif short_ma[-1] < long_ma[-1]:
        # 死叉卖出
        order_shares(context.symbol, -100)`}
          </pre>
        </div>
      </Modal>
    </Card>
  );
};

export default StrategyEditor;
