import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Tabs, 
  Table, 
  Button, 
  Space, 
  Tag, 
  Collapse, 
  Typography, 
  Alert,
  Input,
  Select,
  Row,
  Col,
  Statistic,
  Progress
} from 'antd';
import { 
  BugOutlined, 
  PlayCircleOutlined, 
  PauseCircleOutlined,
  StepForwardOutlined,
  ReloadOutlined,
  EyeOutlined,
  WarningOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';

const { TabPane } = Tabs;
const { Panel } = Collapse;
const { Text, Paragraph } = Typography;
const { Option } = Select;

/**
 * 策略调试器组件
 * 
 * 功能特性：
 * - 断点设置和管理
 * - 变量监控
 * - 执行步骤跟踪
 * - 日志输出
 * - 性能分析
 * - 错误诊断
 */
const StrategyDebugger = ({ 
  strategyCode, 
  isDebugging, 
  onStartDebug, 
  onStopDebug, 
  onStepDebug,
  debugData = {} 
}) => {
  const [breakpoints, setBreakpoints] = useState([]);
  const [watchVariables, setWatchVariables] = useState([]);
  const [executionStack, setExecutionStack] = useState([]);
  const [debugLogs, setDebugLogs] = useState([]);
  const [currentLine, setCurrentLine] = useState(null);
  const [variableValues, setVariableValues] = useState({});
  const [performanceData, setPerformanceData] = useState({});
  const [newWatchVariable, setNewWatchVariable] = useState('');

  /**
   * 组件挂载时初始化调试数据
   */
  useEffect(() => {
    if (debugData) {
      setVariableValues(debugData.variables || {});
      setExecutionStack(debugData.stack || []);
      setDebugLogs(debugData.logs || []);
      setCurrentLine(debugData.currentLine || null);
      setPerformanceData(debugData.performance || {});
    }
  }, [debugData]);

  /**
   * 添加断点
   */
  const addBreakpoint = (lineNumber) => {
    if (!breakpoints.includes(lineNumber)) {
      setBreakpoints([...breakpoints, lineNumber].sort((a, b) => a - b));
    }
  };

  /**
   * 移除断点
   */
  const removeBreakpoint = (lineNumber) => {
    setBreakpoints(breakpoints.filter(bp => bp !== lineNumber));
  };

  /**
   * 切换断点
   */
  const toggleBreakpoint = (lineNumber) => {
    if (breakpoints.includes(lineNumber)) {
      removeBreakpoint(lineNumber);
    } else {
      addBreakpoint(lineNumber);
    }
  };

  /**
   * 添加监控变量
   */
  const addWatchVariable = () => {
    if (newWatchVariable && !watchVariables.includes(newWatchVariable)) {
      setWatchVariables([...watchVariables, newWatchVariable]);
      setNewWatchVariable('');
    }
  };

  /**
   * 移除监控变量
   */
  const removeWatchVariable = (variable) => {
    setWatchVariables(watchVariables.filter(v => v !== variable));
  };

  /**
   * 清除所有断点
   */
  const clearAllBreakpoints = () => {
    setBreakpoints([]);
  };

  /**
   * 清除调试日志
   */
  const clearDebugLogs = () => {
    setDebugLogs([]);
  };

  /**
   * 断点表格列定义
   */
  const breakpointColumns = [
    {
      title: '行号',
      dataIndex: 'line',
      key: 'line',
      width: 80,
      render: (line) => <Tag color="red">{line}</Tag>
    },
    {
      title: '状态',
      dataIndex: 'enabled',
      key: 'enabled',
      width: 80,
      render: (enabled) => (
        <Tag color={enabled ? 'green' : 'gray'}>
          {enabled ? '启用' : '禁用'}
        </Tag>
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 100,
      render: (_, record) => (
        <Button 
          type="link" 
          size="small"
          onClick={() => removeBreakpoint(record.line)}
        >
          删除
        </Button>
      )
    }
  ];

  /**
   * 监控变量表格列定义
   */
  const watchVariableColumns = [
    {
      title: '变量名',
      dataIndex: 'name',
      key: 'name',
      width: 120,
      render: (name) => <Text code>{name}</Text>
    },
    {
      title: '值',
      dataIndex: 'value',
      key: 'value',
      render: (value, record) => {
        const actualValue = variableValues[record.name];
        return (
          <Text style={{ 
            color: actualValue !== undefined ? '#000' : '#999' 
          }}>
            {actualValue !== undefined ? String(actualValue) : '未定义'}
          </Text>
        );
      }
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 80,
      render: (_, record) => {
        const actualValue = variableValues[record.name];
        const type = actualValue !== undefined ? typeof actualValue : 'undefined';
        return <Tag color="blue">{type}</Tag>;
      }
    },
    {
      title: '操作',
      key: 'actions',
      width: 80,
      render: (_, record) => (
        <Button 
          type="link" 
          size="small"
          onClick={() => removeWatchVariable(record.name)}
        >
          删除
        </Button>
      )
    }
  ];

  /**
   * 执行栈表格列定义
   */
  const stackColumns = [
    {
      title: '函数',
      dataIndex: 'function',
      key: 'function',
      render: (func) => <Text code>{func}</Text>
    },
    {
      title: '行号',
      dataIndex: 'line',
      key: 'line',
      width: 80
    },
    {
      title: '文件',
      dataIndex: 'file',
      key: 'file',
      width: 150,
      render: (file) => <Text type="secondary">{file}</Text>
    }
  ];

  /**
   * 日志表格列定义
   */
  const logColumns = [
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 120,
      render: (timestamp) => new Date(timestamp).toLocaleTimeString()
    },
    {
      title: '级别',
      dataIndex: 'level',
      key: 'level',
      width: 80,
      render: (level) => {
        const colors = {
          'error': 'red',
          'warning': 'orange',
          'info': 'blue',
          'debug': 'green'
        };
        return <Tag color={colors[level] || 'default'}>{level.toUpperCase()}</Tag>;
      }
    },
    {
      title: '消息',
      dataIndex: 'message',
      key: 'message',
      render: (message) => <Text>{message}</Text>
    }
  ];

  // 准备表格数据
  const breakpointData = breakpoints.map(line => ({
    key: line,
    line,
    enabled: true
  }));

  const watchVariableData = watchVariables.map(name => ({
    key: name,
    name
  }));

  return (
    <Card
      title={
        <Space>
          <BugOutlined />
          策略调试器
          {isDebugging && <Tag color="green">调试中</Tag>}
        </Space>
      }
      extra={
        <Space>
          {!isDebugging ? (
            <Button 
              type="primary" 
              icon={<PlayCircleOutlined />}
              onClick={onStartDebug}
            >
              开始调试
            </Button>
          ) : (
            <>
              <Button 
                icon={<StepForwardOutlined />}
                onClick={onStepDebug}
              >
                单步执行
              </Button>
              <Button 
                danger
                icon={<PauseCircleOutlined />}
                onClick={onStopDebug}
              >
                停止调试
              </Button>
            </>
          )}
        </Space>
      }
    >
      <Tabs defaultActiveKey="breakpoints">
        {/* 断点管理 */}
        <TabPane tab="断点" key="breakpoints">
          <Space direction="vertical" style={{ width: '100%' }}>
            <Row gutter={16}>
              <Col span={12}>
                <Input
                  placeholder="输入行号添加断点"
                  type="number"
                  onPressEnter={(e) => {
                    const line = parseInt(e.target.value);
                    if (line > 0) {
                      addBreakpoint(line);
                      e.target.value = '';
                    }
                  }}
                />
              </Col>
              <Col span={12}>
                <Button 
                  danger 
                  onClick={clearAllBreakpoints}
                  disabled={breakpoints.length === 0}
                >
                  清除所有断点
                </Button>
              </Col>
            </Row>
            
            <Table
              columns={breakpointColumns}
              dataSource={breakpointData}
              size="small"
              pagination={false}
              locale={{ emptyText: '暂无断点' }}
            />
          </Space>
        </TabPane>

        {/* 变量监控 */}
        <TabPane tab="变量监控" key="variables">
          <Space direction="vertical" style={{ width: '100%' }}>
            <Row gutter={16}>
              <Col span={16}>
                <Input
                  placeholder="输入变量名进行监控"
                  value={newWatchVariable}
                  onChange={(e) => setNewWatchVariable(e.target.value)}
                  onPressEnter={addWatchVariable}
                />
              </Col>
              <Col span={8}>
                <Button 
                  type="primary" 
                  onClick={addWatchVariable}
                  disabled={!newWatchVariable}
                >
                  添加监控
                </Button>
              </Col>
            </Row>
            
            <Table
              columns={watchVariableColumns}
              dataSource={watchVariableData}
              size="small"
              pagination={false}
              locale={{ emptyText: '暂无监控变量' }}
            />
          </Space>
        </TabPane>

        {/* 执行栈 */}
        <TabPane tab="执行栈" key="stack">
          <Table
            columns={stackColumns}
            dataSource={executionStack}
            size="small"
            pagination={false}
            locale={{ emptyText: '暂无执行栈信息' }}
          />
        </TabPane>

        {/* 调试日志 */}
        <TabPane tab="调试日志" key="logs">
          <Space direction="vertical" style={{ width: '100%' }}>
            <Row justify="end">
              <Button 
                icon={<ReloadOutlined />}
                onClick={clearDebugLogs}
                disabled={debugLogs.length === 0}
              >
                清除日志
              </Button>
            </Row>
            
            <Table
              columns={logColumns}
              dataSource={debugLogs}
              size="small"
              pagination={{ pageSize: 10 }}
              locale={{ emptyText: '暂无调试日志' }}
            />
          </Space>
        </TabPane>

        {/* 性能分析 */}
        <TabPane tab="性能分析" key="performance">
          <Row gutter={16}>
            <Col span={6}>
              <Statistic
                title="执行时间"
                value={performanceData.executionTime || 0}
                suffix="ms"
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="内存使用"
                value={performanceData.memoryUsage || 0}
                suffix="MB"
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="函数调用"
                value={performanceData.functionCalls || 0}
                suffix="次"
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="CPU使用率"
                value={performanceData.cpuUsage || 0}
                suffix="%"
              />
            </Col>
          </Row>
          
          {performanceData.cpuUsage && (
            <div style={{ marginTop: '16px' }}>
              <Text strong>CPU使用率</Text>
              <Progress 
                percent={performanceData.cpuUsage} 
                status={performanceData.cpuUsage > 80 ? 'exception' : 'normal'}
              />
            </div>
          )}
          
          {performanceData.memoryUsage && (
            <div style={{ marginTop: '16px' }}>
              <Text strong>内存使用</Text>
              <Progress 
                percent={(performanceData.memoryUsage / 1024) * 100} 
                format={() => `${performanceData.memoryUsage}MB`}
                status={performanceData.memoryUsage > 512 ? 'exception' : 'normal'}
              />
            </div>
          )}
        </TabPane>
      </Tabs>

      {/* 当前执行状态 */}
      {isDebugging && currentLine && (
        <Alert
          message={`当前执行到第 ${currentLine} 行`}
          type="info"
          icon={<InfoCircleOutlined />}
          style={{ marginTop: '16px' }}
          showIcon
        />
      )}
    </Card>
  );
};

export default StrategyDebugger;
