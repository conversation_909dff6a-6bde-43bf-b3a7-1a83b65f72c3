import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Button, 
  Modal, 
  Input, 
  Select, 
  Tag, 
  Space, 
  Typography,
  Divider,
  Rate,
  Avatar,
  Tooltip,
  message
} from 'antd';
import {
  FileTextOutlined,
  EyeOutlined,
  DownloadOutlined,
  StarOutlined,
  UserOutlined,
  CalendarOutlined,
  CodeOutlined,
  TrophyOutlined,
  RocketOutlined,
  BulbOutlined,
  LineChartOutlined,
  SwapOutlined,
  FundOutlined,
  AppstoreOutlined,
  RobotOutlined
} from '@ant-design/icons';

const { Search } = Input;
const { Option } = Select;
const { Text, Paragraph, Title } = Typography;

/**
 * 策略模板库组件
 * 
 * 功能特性：
 * - 策略模板浏览
 * - 模板分类筛选
 * - 模板搜索
 * - 模板预览
 * - 模板使用
 * - 模板评分
 */
const StrategyTemplateLibrary = ({ onUseTemplate, visible, onClose }) => {
  const [templates, setTemplates] = useState([]);
  const [filteredTemplates, setFilteredTemplates] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchKeyword, setSearchKeyword] = useState('');
  const [previewTemplate, setPreviewTemplate] = useState(null);
  const [previewVisible, setPreviewVisible] = useState(false);

  /**
   * 策略模板数据
   */
  const strategyTemplates = [
    {
      id: 'ma_cross',
      name: '双均线交叉策略',
      description: '基于短期和长期移动平均线交叉的经典策略，适合趋势跟踪',
      category: 'trend',
      difficulty: 'beginner',
      rating: 4.5,
      downloads: 1250,
      author: '量化专家',
      createTime: '2024-01-15',
      tags: ['移动平均', '趋势跟踪', '经典策略'],
      code: `def initialize(context):
    """双均线交叉策略初始化"""
    context.symbol = '000001.SZ'
    context.short_window = 5
    context.long_window = 20
    
    # 设置基准
    context.benchmark = '000300.SH'
    
    # 设置手续费
    set_order_cost(OrderCost(close_tax=0.001, open_commission=0.0003,
                            close_commission=0.0003, min_commission=5), type='stock')

def handle_data(context, data):
    """策略主逻辑"""
    # 获取历史价格数据
    prices = get_history([context.symbol], 'close', context.long_window)
    
    # 计算移动平均线
    short_ma = sma(prices[context.symbol], context.short_window)
    long_ma = sma(prices[context.symbol], context.long_window)
    
    # 获取当前持仓
    current_position = get_portfolio().positions[context.symbol].amount
    
    # 交易逻辑
    if short_ma[-1] > long_ma[-1] and current_position == 0:
        # 金叉买入
        order_value(context.symbol, 10000)
        log.info(f"买入信号: 短期均线{short_ma[-1]:.2f} > 长期均线{long_ma[-1]:.2f}")
    elif short_ma[-1] < long_ma[-1] and current_position > 0:
        # 死叉卖出
        order_target_shares(context.symbol, 0)
        log.info(f"卖出信号: 短期均线{short_ma[-1]:.2f} < 长期均线{long_ma[-1]:.2f}")
    
    # 记录数据
    record(short_ma=short_ma[-1], long_ma=long_ma[-1])`,
      performance: {
        annualReturn: '15.2%',
        sharpeRatio: '1.35',
        maxDrawdown: '8.5%',
        winRate: '62%'
      }
    },
    {
      id: 'rsi_reversal',
      name: 'RSI反转策略',
      description: '基于RSI指标的超买超卖反转策略，适合震荡市场',
      category: 'reversal',
      difficulty: 'intermediate',
      rating: 4.2,
      downloads: 890,
      author: '技术分析师',
      createTime: '2024-01-20',
      tags: ['RSI', '反转', '超买超卖'],
      code: `def initialize(context):
    """RSI反转策略初始化"""
    context.symbol = '000001.SZ'
    context.rsi_window = 14
    context.rsi_upper = 70
    context.rsi_lower = 30
    context.position_size = 0.3  # 仓位大小

def handle_data(context, data):
    """策略主逻辑"""
    # 获取历史价格数据
    prices = get_history([context.symbol], 'close', context.rsi_window + 1)
    
    # 计算RSI指标
    rsi_value = rsi(prices[context.symbol], context.rsi_window)
    current_rsi = rsi_value[-1]
    
    # 获取当前持仓
    current_position = get_portfolio().positions[context.symbol].amount
    total_value = get_portfolio().total_value
    
    # 交易逻辑
    if current_rsi < context.rsi_lower and current_position == 0:
        # RSI超卖，买入
        order_value(context.symbol, total_value * context.position_size)
        log.info(f"RSI超卖买入信号: RSI={current_rsi:.2f}")
    elif current_rsi > context.rsi_upper and current_position > 0:
        # RSI超买，卖出
        order_target_shares(context.symbol, 0)
        log.info(f"RSI超买卖出信号: RSI={current_rsi:.2f}")
    
    # 记录数据
    record(rsi=current_rsi)`,
      performance: {
        annualReturn: '12.8%',
        sharpeRatio: '1.18',
        maxDrawdown: '12.3%',
        winRate: '58%'
      }
    },
    {
      id: 'bollinger_bands',
      name: '布林带策略',
      description: '基于布林带的突破和回归策略，适合波动性交易',
      category: 'volatility',
      difficulty: 'intermediate',
      rating: 4.0,
      downloads: 675,
      author: '波动率专家',
      createTime: '2024-01-25',
      tags: ['布林带', '波动率', '突破'],
      code: `def initialize(context):
    """布林带策略初始化"""
    context.symbol = '000001.SZ'
    context.window = 20
    context.std_dev = 2
    context.position_size = 0.5

def handle_data(context, data):
    """策略主逻辑"""
    # 获取历史价格数据
    prices = get_history([context.symbol], 'close', context.window)
    current_price = get_current_price(context.symbol)
    
    # 计算布林带
    upper_band, middle_band, lower_band = bollinger_bands(
        prices[context.symbol], context.window, context.std_dev
    )
    
    # 获取当前持仓
    current_position = get_portfolio().positions[context.symbol].amount
    total_value = get_portfolio().total_value
    
    # 交易逻辑
    if current_price < lower_band[-1] and current_position == 0:
        # 价格跌破下轨，买入
        order_value(context.symbol, total_value * context.position_size)
        log.info(f"布林带下轨买入: 价格{current_price:.2f} < 下轨{lower_band[-1]:.2f}")
    elif current_price > upper_band[-1] and current_position > 0:
        # 价格突破上轨，卖出
        order_target_shares(context.symbol, 0)
        log.info(f"布林带上轨卖出: 价格{current_price:.2f} > 上轨{upper_band[-1]:.2f}")
    
    # 记录数据
    record(price=current_price, upper=upper_band[-1], 
           middle=middle_band[-1], lower=lower_band[-1])`,
      performance: {
        annualReturn: '18.5%',
        sharpeRatio: '1.42',
        maxDrawdown: '15.2%',
        winRate: '55%'
      }
    },
    {
      id: 'momentum',
      name: '动量策略',
      description: '基于价格动量的趋势跟踪策略，适合强势股票',
      category: 'momentum',
      difficulty: 'advanced',
      rating: 4.3,
      downloads: 520,
      author: '动量交易员',
      createTime: '2024-02-01',
      tags: ['动量', '趋势', '强势股'],
      code: `def initialize(context):
    """动量策略初始化"""
    context.symbols = ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH']
    context.momentum_window = 20
    context.top_n = 2  # 选择动量最强的N只股票
    context.rebalance_freq = 5  # 每5天调仓一次
    context.day_count = 0

def handle_data(context, data):
    """策略主逻辑"""
    context.day_count += 1
    
    # 每N天调仓一次
    if context.day_count % context.rebalance_freq != 0:
        return
    
    # 计算各股票的动量
    momentum_scores = {}
    for symbol in context.symbols:
        prices = get_history([symbol], 'close', context.momentum_window)
        if len(prices) >= context.momentum_window:
            # 计算动量得分（价格变化率）
            momentum = (prices[symbol][-1] / prices[symbol][0] - 1) * 100
            momentum_scores[symbol] = momentum
    
    # 选择动量最强的股票
    sorted_stocks = sorted(momentum_scores.items(), 
                          key=lambda x: x[1], reverse=True)
    selected_stocks = [stock[0] for stock in sorted_stocks[:context.top_n]]
    
    # 清仓所有持仓
    for symbol in context.symbols:
        order_target_shares(symbol, 0)
    
    # 等权重买入选中的股票
    total_value = get_portfolio().total_value
    position_value = total_value / len(selected_stocks)
    
    for symbol in selected_stocks:
        order_value(symbol, position_value)
        log.info(f"买入动量股票: {symbol}, 动量得分: {momentum_scores[symbol]:.2f}%")
    
    # 记录数据
    record(selected_count=len(selected_stocks))`,
      performance: {
        annualReturn: '22.1%',
        sharpeRatio: '1.58',
        maxDrawdown: '18.7%',
        winRate: '48%'
      }
    },
    {
      id: 'mean_reversion',
      name: '均值回归策略',
      description: '基于价格均值回归的策略，适合稳定的大盘股',
      category: 'reversal',
      difficulty: 'beginner',
      rating: 3.8,
      downloads: 430,
      author: '稳健投资者',
      createTime: '2024-02-05',
      tags: ['均值回归', '稳健', '大盘股'],
      code: `def initialize(context):
    """均值回归策略初始化"""
    context.symbol = '000300.SH'  # 沪深300ETF
    context.window = 30
    context.threshold = 0.02  # 偏离阈值2%

def handle_data(context, data):
    """策略主逻辑"""
    # 获取历史价格数据
    prices = get_history([context.symbol], 'close', context.window)
    current_price = get_current_price(context.symbol)
    
    # 计算移动平均价格
    mean_price = prices[context.symbol].mean()
    
    # 计算价格偏离度
    deviation = (current_price - mean_price) / mean_price
    
    # 获取当前持仓
    current_position = get_portfolio().positions[context.symbol].amount
    
    # 交易逻辑
    if deviation < -context.threshold and current_position == 0:
        # 价格低于均值，买入
        order_value(context.symbol, get_portfolio().total_value * 0.8)
        log.info(f"均值回归买入: 偏离度{deviation:.2%}")
    elif deviation > context.threshold and current_position > 0:
        # 价格高于均值，卖出
        order_target_shares(context.symbol, 0)
        log.info(f"均值回归卖出: 偏离度{deviation:.2%}")
    
    # 记录数据
    record(price=current_price, mean_price=mean_price, deviation=deviation)`,
      performance: {
        annualReturn: '9.5%',
        sharpeRatio: '0.95',
        maxDrawdown: '6.8%',
        winRate: '65%'
      }
    },
    {
      id: 'macd_strategy',
      name: 'MACD策略',
      description: '基于MACD指标的金叉死叉信号进行交易，经典的趋势跟踪策略',
      category: 'technical',
      difficulty: 'intermediate',
      rating: 4.4,
      downloads: 980,
      author: '技术分析专家',
      createTime: '2024-02-10',
      tags: ['MACD', '趋势跟踪', '技术指标'],
      code: `def initialize(context):
    """MACD策略初始化"""
    context.symbol = '000001.SZ'
    context.fast_period = 12
    context.slow_period = 26
    context.signal_period = 9

    # 设置基准
    context.benchmark = '000300.SH'

    # 设置手续费
    set_order_cost(OrderCost(close_tax=0.001, open_commission=0.0003,
                            close_commission=0.0003, min_commission=5), type='stock')

def handle_data(context, data):
    """策略主逻辑"""
    # 获取历史价格数据
    prices = get_history([context.symbol], 'close', context.slow_period + context.signal_period)

    # 计算MACD指标
    macd, signal, histogram = calculate_macd(
        prices[context.symbol],
        context.fast_period,
        context.slow_period,
        context.signal_period
    )

    # 获取当前持仓
    current_position = get_portfolio().positions[context.symbol].amount

    # 交易逻辑
    if macd[-1] > signal[-1] and macd[-2] <= signal[-2] and current_position == 0:
        # MACD金叉买入
        order_value(context.symbol, get_portfolio().total_value * 0.8)
        log.info(f"MACD金叉买入: MACD={macd[-1]:.4f}, Signal={signal[-1]:.4f}")
    elif macd[-1] < signal[-1] and macd[-2] >= signal[-2] and current_position > 0:
        # MACD死叉卖出
        order_target_shares(context.symbol, 0)
        log.info(f"MACD死叉卖出: MACD={macd[-1]:.4f}, Signal={signal[-1]:.4f}")

    # 记录数据
    record(macd=macd[-1], signal=signal[-1], histogram=histogram[-1])`,
      performance: {
        annualReturn: '16.8%',
        sharpeRatio: '1.28',
        maxDrawdown: '11.2%',
        winRate: '59%'
      }
    },
    {
      id: 'pairs_trading',
      name: '配对交易策略',
      description: '基于统计套利的配对交易策略，通过价差回归获取稳定收益',
      category: 'arbitrage',
      difficulty: 'advanced',
      rating: 4.6,
      downloads: 650,
      author: '量化套利专家',
      createTime: '2024-02-15',
      tags: ['配对交易', '统计套利', '市场中性'],
      code: `def initialize(context):
    """配对交易策略初始化"""
    context.stock_a = '601318.SH'  # 中国平安
    context.stock_b = '600036.SH'  # 招商银行
    context.lookback_period = 252
    context.entry_threshold = 2.0
    context.exit_threshold = 0.5
    context.position_size = 0.4

    # 协整关系参数
    context.hedge_ratio = 0
    context.spread_mean = 0
    context.spread_std = 0

def before_trading_start(context, data):
    """每日开盘前执行"""
    # 获取历史价格数据
    prices_a = get_history([context.stock_a], 'close', context.lookback_period)
    prices_b = get_history([context.stock_b], 'close', context.lookback_period)

    # 计算对冲比率和价差统计量
    log_a = np.log(prices_a[context.stock_a])
    log_b = np.log(prices_b[context.stock_b])

    # 线性回归计算对冲比率
    from sklearn.linear_model import LinearRegression
    model = LinearRegression()
    model.fit(log_b.values.reshape(-1, 1), log_a.values)
    context.hedge_ratio = model.coef_[0]

    # 计算价差
    spread = log_a - context.hedge_ratio * log_b
    context.spread_mean = spread.mean()
    context.spread_std = spread.std()

def handle_data(context, data):
    """策略主逻辑"""
    # 获取当前价格
    price_a = get_current_price(context.stock_a)
    price_b = get_current_price(context.stock_b)

    # 计算当前价差和Z-score
    current_spread = np.log(price_a) - context.hedge_ratio * np.log(price_b)
    z_score = (current_spread - context.spread_mean) / context.spread_std

    # 获取当前持仓
    position_a = get_portfolio().positions[context.stock_a].amount
    position_b = get_portfolio().positions[context.stock_b].amount

    # 交易逻辑
    if abs(z_score) > context.entry_threshold and position_a == 0:
        # 开仓
        total_value = get_portfolio().total_value * context.position_size

        if z_score > 0:
            # 价差过大，卖出A买入B
            order_value(context.stock_a, -total_value / 2)
            order_value(context.stock_b, total_value / 2 * context.hedge_ratio)
            log.info(f"开仓: 卖出{context.stock_a}, 买入{context.stock_b}, Z-score={z_score:.2f}")
        else:
            # 价差过小，买入A卖出B
            order_value(context.stock_a, total_value / 2)
            order_value(context.stock_b, -total_value / 2 * context.hedge_ratio)
            log.info(f"开仓: 买入{context.stock_a}, 卖出{context.stock_b}, Z-score={z_score:.2f}")

    elif abs(z_score) < context.exit_threshold and position_a != 0:
        # 平仓
        order_target_shares(context.stock_a, 0)
        order_target_shares(context.stock_b, 0)
        log.info(f"平仓: Z-score回归到{z_score:.2f}")

    # 记录数据
    record(z_score=z_score, spread=current_spread)`,
      performance: {
        annualReturn: '12.5%',
        sharpeRatio: '1.51',
        maxDrawdown: '4.2%',
        winRate: '72%'
      }
    },
    {
      id: 'multi_factor',
      name: '多因子模型策略',
      description: '基于价值、成长、质量等多个因子的量化选股策略',
      category: 'factor',
      difficulty: 'advanced',
      rating: 4.7,
      downloads: 420,
      author: '因子投资专家',
      createTime: '2024-02-20',
      tags: ['多因子', '量化选股', '因子投资'],
      code: `def initialize(context):
    """多因子模型策略初始化"""
    context.stock_pool = get_index_stocks('000300.SH')  # 沪深300成分股
    context.factor_weights = {
        'value': 0.25,
        'growth': 0.20,
        'quality': 0.20,
        'momentum': 0.15,
        'low_vol': 0.20
    }
    context.rebalance_freq = 30  # 30天调仓一次
    context.top_stocks_pct = 0.2  # 选择前20%的股票
    context.day_count = 0

def calculate_factors(context, data):
    """计算因子得分"""
    factor_scores = {}

    for stock in context.stock_pool:
        # 获取基本面数据
        fundamental = get_fundamentals(stock)
        prices = get_history([stock], 'close', 252)

        scores = {}

        # 价值因子
        scores['value'] = (
            1 / fundamental['pe_ratio'] * 0.4 +
            1 / fundamental['pb_ratio'] * 0.3 +
            fundamental['dividend_yield'] * 0.3
        )

        # 成长因子
        scores['growth'] = (
            fundamental['revenue_growth'] * 0.4 +
            fundamental['earnings_growth'] * 0.4 +
            fundamental['roe_growth'] * 0.2
        )

        # 质量因子
        scores['quality'] = (
            fundamental['roe'] * 0.4 +
            fundamental['roa'] * 0.3 +
            fundamental['gross_margin'] * 0.3
        )

        # 动量因子
        returns_1m = prices[stock].pct_change(20).iloc[-1]
        returns_3m = prices[stock].pct_change(60).iloc[-1]
        scores['momentum'] = returns_1m * 0.3 + returns_3m * 0.7

        # 低波动因子
        volatility = prices[stock].pct_change().rolling(60).std().iloc[-1]
        scores['low_vol'] = -volatility  # 波动率越低越好

        # 计算综合得分
        total_score = sum(
            scores[factor] * context.factor_weights[factor]
            for factor in context.factor_weights
        )

        factor_scores[stock] = total_score

    return factor_scores

def handle_data(context, data):
    """策略主逻辑"""
    context.day_count += 1

    # 每N天调仓一次
    if context.day_count % context.rebalance_freq != 0:
        return

    # 计算因子得分
    factor_scores = calculate_factors(context, data)

    # 选择得分最高的股票
    sorted_stocks = sorted(factor_scores.items(), key=lambda x: x[1], reverse=True)
    n_stocks = int(len(sorted_stocks) * context.top_stocks_pct)
    selected_stocks = [stock for stock, score in sorted_stocks[:n_stocks]]

    # 清仓所有持仓
    for stock in context.stock_pool:
        order_target_shares(stock, 0)

    # 等权重买入选中的股票
    if selected_stocks:
        weight_per_stock = 1.0 / len(selected_stocks)
        total_value = get_portfolio().total_value

        for stock in selected_stocks:
            order_value(stock, total_value * weight_per_stock)
            log.info(f"买入: {stock}, 因子得分: {factor_scores[stock]:.4f}")

    # 记录数据
    record(selected_count=len(selected_stocks))`,
      performance: {
        annualReturn: '18.6%',
        sharpeRatio: '1.35',
        maxDrawdown: '15.2%',
        winRate: '58%'
      }
    },
    {
      id: 'grid_trading',
      name: '网格交易策略',
      description: '在震荡市场中通过网格买卖获取价差收益的机械化交易策略',
      category: 'grid',
      difficulty: 'intermediate',
      rating: 4.1,
      downloads: 780,
      author: '网格交易专家',
      createTime: '2024-02-25',
      tags: ['网格交易', '震荡市场', '机械化'],
      code: `def initialize(context):
    """网格交易策略初始化"""
    context.symbol = '000001.SZ'
    context.grid_levels = 10  # 网格层数
    context.grid_spacing = 0.02  # 网格间距2%
    context.base_quantity = 1000  # 基础交易数量
    context.max_position = 10000  # 最大持仓

    # 网格参数
    context.grid_points = []
    context.grid_positions = {}  # 记录各网格点的持仓
    context.center_price = 0

def before_trading_start(context, data):
    """每日开盘前设置网格"""
    # 获取历史价格，确定网格中心价格
    prices = get_history([context.symbol], 'close', 20)
    context.center_price = prices[context.symbol].mean()

    # 计算网格点位
    context.grid_points = []
    for i in range(-context.grid_levels//2, context.grid_levels//2 + 1):
        grid_price = context.center_price * (1 + i * context.grid_spacing)
        context.grid_points.append(round(grid_price, 2))

    context.grid_points.sort()
    log.info(f"网格设置: 中心价格={context.center_price:.2f}, 网格点={len(context.grid_points)}个")

def handle_data(context, data):
    """策略主逻辑"""
    current_price = get_current_price(context.symbol)
    current_position = get_portfolio().positions[context.symbol].amount

    # 检查网格触发
    for i, grid_price in enumerate(context.grid_points):
        # 买入网格：价格下跌到网格点且该点无持仓
        if (current_price <= grid_price and
            grid_price not in context.grid_positions and
            current_position < context.max_position):

            # 计算交易数量（可以根据网格层级调整）
            quantity = context.base_quantity

            # 执行买入
            order(context.symbol, quantity)
            context.grid_positions[grid_price] = quantity
            log.info(f"网格买入: 价格={grid_price:.2f}, 数量={quantity}")

        # 卖出网格：价格上涨到网格点且该点有持仓
        elif (current_price >= grid_price and
              grid_price in context.grid_positions):

            # 获取该网格点的持仓数量
            quantity = context.grid_positions[grid_price]

            # 执行卖出
            order(context.symbol, -quantity)
            del context.grid_positions[grid_price]
            log.info(f"网格卖出: 价格={grid_price:.2f}, 数量={quantity}")

    # 风险控制：价格偏离网格范围太远时重新设置
    price_deviation = abs(current_price - context.center_price) / context.center_price
    if price_deviation > context.grid_spacing * context.grid_levels:
        log.warning(f"价格偏离过大，重新设置网格: 当前价格={current_price:.2f}")
        # 清空网格持仓记录，重新设置
        context.grid_positions = {}

    # 记录数据
    record(price=current_price, grid_positions=len(context.grid_positions))`,
      performance: {
        annualReturn: '14.2%',
        sharpeRatio: '1.20',
        maxDrawdown: '8.5%',
        winRate: '78%'
      }
    },
    {
      id: 'svm_strategy',
      name: '支持向量机策略',
      description: '基于机器学习SVM算法进行价格方向预测的智能交易策略',
      category: 'ml',
      difficulty: 'advanced',
      rating: 4.5,
      downloads: 320,
      author: '机器学习专家',
      createTime: '2024-03-01',
      tags: ['机器学习', 'SVM', '价格预测'],
      code: `def initialize(context):
    """SVM策略初始化"""
    context.symbol = '000001.SZ'
    context.lookback_period = 60  # 特征计算回看期
    context.prediction_horizon = 5  # 预测未来5日
    context.retrain_freq = 30  # 30天重训练一次
    context.confidence_threshold = 0.6  # 预测置信度阈值

    # 模型相关
    context.model = None
    context.scaler = None
    context.feature_names = []
    context.day_count = 0

def calculate_features(context, data):
    """计算技术指标特征"""
    prices = get_history([context.symbol], ['close', 'high', 'low', 'volume'],
                        context.lookback_period + 20)

    features = {}

    # 价格特征
    features['returns_1d'] = prices['close'].pct_change()
    features['returns_5d'] = prices['close'].pct_change(5)
    features['returns_20d'] = prices['close'].pct_change(20)

    # 移动平均
    ma_5 = prices['close'].rolling(5).mean()
    ma_20 = prices['close'].rolling(20).mean()
    features['ma_ratio'] = ma_5 / ma_20

    # MACD
    exp1 = prices['close'].ewm(span=12).mean()
    exp2 = prices['close'].ewm(span=26).mean()
    macd = exp1 - exp2
    macd_signal = macd.ewm(span=9).mean()
    features['macd'] = macd
    features['macd_signal'] = macd_signal

    # RSI
    delta = prices['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
    rs = gain / loss
    features['rsi'] = 100 - (100 / (1 + rs))

    # 布林带位置
    bb_ma = prices['close'].rolling(20).mean()
    bb_std = prices['close'].rolling(20).std()
    features['bb_position'] = (prices['close'] - bb_ma) / (2 * bb_std)

    # 成交量特征
    features['volume_ratio'] = prices['volume'] / prices['volume'].rolling(20).mean()

    # 波动率
    features['volatility'] = features['returns_1d'].rolling(20).std()

    # 转换为DataFrame并清理
    feature_df = pd.DataFrame(features).fillna(method='ffill').fillna(0)
    return feature_df.iloc[-1].values  # 返回最新的特征值

def train_model(context, data):
    """训练SVM模型"""
    from sklearn.svm import SVC
    from sklearn.preprocessing import StandardScaler

    # 获取历史数据用于训练
    prices = get_history([context.symbol], ['close', 'high', 'low', 'volume'], 500)

    # 计算特征矩阵
    features_list = []
    labels_list = []

    for i in range(context.lookback_period, len(prices) - context.prediction_horizon):
        # 计算特征（这里简化处理）
        window_data = prices.iloc[i-context.lookback_period:i]
        features = calculate_features_for_training(window_data)

        # 计算标签（未来收益率方向）
        future_return = (prices['close'].iloc[i + context.prediction_horizon] /
                        prices['close'].iloc[i] - 1)
        label = 1 if future_return > 0.02 else (-1 if future_return < -0.02 else 0)

        features_list.append(features)
        labels_list.append(label)

    # 训练模型
    X = np.array(features_list)
    y = np.array(labels_list)

    # 数据标准化
    context.scaler = StandardScaler()
    X_scaled = context.scaler.fit_transform(X)

    # 训练SVM
    context.model = SVC(probability=True, kernel='rbf', C=1.0, gamma='scale')
    context.model.fit(X_scaled, y)

    log.info("SVM模型训练完成")

def handle_data(context, data):
    """策略主逻辑"""
    context.day_count += 1

    # 定期重训练模型
    if context.day_count % context.retrain_freq == 0 or context.model is None:
        train_model(context, data)
        return

    # 计算当前特征
    current_features = calculate_features(context, data)

    # 模型预测
    if context.model is not None and context.scaler is not None:
        features_scaled = context.scaler.transform([current_features])
        prediction = context.model.predict(features_scaled)[0]
        probabilities = context.model.predict_proba(features_scaled)[0]
        confidence = max(probabilities)

        # 获取当前持仓
        current_position = get_portfolio().positions[context.symbol].amount

        # 交易逻辑（只在高置信度时交易）
        if confidence > context.confidence_threshold:
            if prediction == 1 and current_position == 0:
                # 预测上涨，买入
                order_value(context.symbol, get_portfolio().total_value * 0.8)
                log.info(f"SVM预测买入: 预测={prediction}, 置信度={confidence:.2f}")
            elif prediction == -1 and current_position > 0:
                # 预测下跌，卖出
                order_target_shares(context.symbol, 0)
                log.info(f"SVM预测卖出: 预测={prediction}, 置信度={confidence:.2f}")

        # 记录数据
        record(prediction=prediction, confidence=confidence)`,
      performance: {
        annualReturn: '22.3%',
        sharpeRatio: '1.18',
        maxDrawdown: '13.8%',
        winRate: '57%'
      }
    }
  ];

  /**
   * 组件挂载时初始化数据
   */
  useEffect(() => {
    setTemplates(strategyTemplates);
    setFilteredTemplates(strategyTemplates);
  }, []);

  /**
   * 筛选模板
   */
  useEffect(() => {
    let filtered = templates;

    // 按分类筛选
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(template => template.category === selectedCategory);
    }

    // 按关键字搜索
    if (searchKeyword) {
      filtered = filtered.filter(template =>
        template.name.toLowerCase().includes(searchKeyword.toLowerCase()) ||
        template.description.toLowerCase().includes(searchKeyword.toLowerCase()) ||
        template.tags.some(tag => tag.toLowerCase().includes(searchKeyword.toLowerCase()))
      );
    }

    setFilteredTemplates(filtered);
  }, [templates, selectedCategory, searchKeyword]);

  /**
   * 获取分类图标
   */
  const getCategoryIcon = (category) => {
    const icons = {
      trend: <RocketOutlined />,
      reversal: <BulbOutlined />,
      momentum: <TrophyOutlined />,
      volatility: <CodeOutlined />,
      technical: <LineChartOutlined />,
      arbitrage: <SwapOutlined />,
      factor: <FundOutlined />,
      grid: <AppstoreOutlined />,
      ml: <RobotOutlined />
    };
    return icons[category] || <FileTextOutlined />;
  };

  /**
   * 获取难度颜色
   */
  const getDifficultyColor = (difficulty) => {
    const colors = {
      beginner: 'green',
      intermediate: 'orange',
      advanced: 'red'
    };
    return colors[difficulty] || 'default';
  };

  /**
   * 获取难度文本
   */
  const getDifficultyText = (difficulty) => {
    const texts = {
      beginner: '初级',
      intermediate: '中级',
      advanced: '高级'
    };
    return texts[difficulty] || difficulty;
  };

  /**
   * 预览模板
   */
  const handlePreview = (template) => {
    setPreviewTemplate(template);
    setPreviewVisible(true);
  };

  /**
   * 使用模板
   */
  const handleUseTemplate = (template) => {
    if (onUseTemplate) {
      onUseTemplate(template);
      message.success(`已应用模板：${template.name}`);
      onClose();
    }
  };

  return (
    <Modal
      title="策略模板库"
      visible={visible}
      onCancel={onClose}
      width={1200}
      footer={null}
      bodyStyle={{ padding: '20px' }}
    >
      {/* 搜索和筛选 */}
      <Row gutter={16} style={{ marginBottom: '20px' }}>
        <Col span={12}>
          <Search
            placeholder="搜索策略模板..."
            value={searchKeyword}
            onChange={(e) => setSearchKeyword(e.target.value)}
            allowClear
          />
        </Col>
        <Col span={12}>
          <Select
            style={{ width: '100%' }}
            value={selectedCategory}
            onChange={setSelectedCategory}
          >
            <Option value="all">全部分类</Option>
            <Option value="trend">趋势跟踪</Option>
            <Option value="reversal">反转策略</Option>
            <Option value="momentum">动量策略</Option>
            <Option value="volatility">波动率策略</Option>
            <Option value="technical">技术指标</Option>
            <Option value="arbitrage">统计套利</Option>
            <Option value="factor">因子投资</Option>
            <Option value="grid">网格交易</Option>
            <Option value="ml">机器学习</Option>
          </Select>
        </Col>
      </Row>

      {/* 模板列表 */}
      <Row gutter={[16, 16]}>
        {filteredTemplates.map(template => (
          <Col span={12} key={template.id}>
            <Card
              size="small"
              title={
                <Space>
                  {getCategoryIcon(template.category)}
                  <Text strong>{template.name}</Text>
                  <Tag color={getDifficultyColor(template.difficulty)}>
                    {getDifficultyText(template.difficulty)}
                  </Tag>
                </Space>
              }
              extra={
                <Space>
                  <Rate disabled defaultValue={template.rating} allowHalf />
                  <Text type="secondary">({template.downloads})</Text>
                </Space>
              }
              actions={[
                <Tooltip title="预览代码">
                  <Button 
                    type="link" 
                    icon={<EyeOutlined />}
                    onClick={() => handlePreview(template)}
                  >
                    预览
                  </Button>
                </Tooltip>,
                <Tooltip title="使用模板">
                  <Button 
                    type="link" 
                    icon={<DownloadOutlined />}
                    onClick={() => handleUseTemplate(template)}
                  >
                    使用
                  </Button>
                </Tooltip>
              ]}
            >
              <Paragraph ellipsis={{ rows: 2 }}>
                {template.description}
              </Paragraph>
              
              <Space wrap>
                {template.tags.map(tag => (
                  <Tag key={tag} size="small">{tag}</Tag>
                ))}
              </Space>
              
              <Divider style={{ margin: '12px 0' }} />
              
              <Row gutter={16}>
                <Col span={12}>
                  <Space>
                    <Avatar size="small" icon={<UserOutlined />} />
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {template.author}
                    </Text>
                  </Space>
                </Col>
                <Col span={12}>
                  <Space>
                    <CalendarOutlined style={{ fontSize: '12px' }} />
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {template.createTime}
                    </Text>
                  </Space>
                </Col>
              </Row>
              
              <Row gutter={8} style={{ marginTop: '8px' }}>
                <Col span={6}>
                  <Text type="secondary" style={{ fontSize: '11px' }}>
                    年化收益: {template.performance.annualReturn}
                  </Text>
                </Col>
                <Col span={6}>
                  <Text type="secondary" style={{ fontSize: '11px' }}>
                    夏普: {template.performance.sharpeRatio}
                  </Text>
                </Col>
                <Col span={6}>
                  <Text type="secondary" style={{ fontSize: '11px' }}>
                    回撤: {template.performance.maxDrawdown}
                  </Text>
                </Col>
                <Col span={6}>
                  <Text type="secondary" style={{ fontSize: '11px' }}>
                    胜率: {template.performance.winRate}
                  </Text>
                </Col>
              </Row>
            </Card>
          </Col>
        ))}
      </Row>

      {/* 模板预览模态框 */}
      <Modal
        title={previewTemplate?.name}
        visible={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        width={800}
        footer={[
          <Button key="cancel" onClick={() => setPreviewVisible(false)}>
            关闭
          </Button>,
          <Button 
            key="use" 
            type="primary"
            onClick={() => {
              handleUseTemplate(previewTemplate);
              setPreviewVisible(false);
            }}
          >
            使用此模板
          </Button>
        ]}
      >
        {previewTemplate && (
          <div>
            <Paragraph>{previewTemplate.description}</Paragraph>
            
            <Title level={5}>策略代码：</Title>
            <pre style={{
              backgroundColor: '#f6f8fa',
              padding: '16px',
              borderRadius: '6px',
              overflow: 'auto',
              maxHeight: '400px',
              fontSize: '12px',
              lineHeight: '1.5'
            }}>
              {previewTemplate.code}
            </pre>
            
            <Title level={5}>性能指标：</Title>
            <Row gutter={16}>
              <Col span={6}>
                <Statistic 
                  title="年化收益率" 
                  value={previewTemplate.performance.annualReturn} 
                />
              </Col>
              <Col span={6}>
                <Statistic 
                  title="夏普比率" 
                  value={previewTemplate.performance.sharpeRatio} 
                />
              </Col>
              <Col span={6}>
                <Statistic 
                  title="最大回撤" 
                  value={previewTemplate.performance.maxDrawdown} 
                />
              </Col>
              <Col span={6}>
                <Statistic 
                  title="胜率" 
                  value={previewTemplate.performance.winRate} 
                />
              </Col>
            </Row>
          </div>
        )}
      </Modal>
    </Modal>
  );
};

export default StrategyTemplateLibrary;
