/**
 * 策略模板库组件测试
 * 
 * 测试策略模板库的功能，包括策略展示、搜索、分类筛选等
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { BrowserRouter } from 'react-router-dom';
import StrategyTemplateLibrary from '../StrategyTemplateLibrary';

// Mock antd组件
jest.mock('antd', () => ({
  ...jest.requireActual('antd'),
  message: {
    success: jest.fn(),
    error: jest.fn(),
    warning: jest.fn(),
  },
}));

// 包装组件以提供Router上下文
const renderWithRouter = (component) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

describe('StrategyTemplateLibrary', () => {
  beforeEach(() => {
    // 清除所有mock调用
    jest.clearAllMocks();
  });

  test('应该正确渲染策略模板库组件', () => {
    renderWithRouter(<StrategyTemplateLibrary />);
    
    // 检查标题
    expect(screen.getByText('策略模板库')).toBeInTheDocument();
    
    // 检查搜索框
    expect(screen.getByPlaceholderText('搜索策略名称、描述或标签...')).toBeInTheDocument();
    
    // 检查分类选择器
    expect(screen.getByText('全部分类')).toBeInTheDocument();
    
    // 检查难度选择器
    expect(screen.getByText('全部难度')).toBeInTheDocument();
  });

  test('应该显示所有策略卡片', async () => {
    renderWithRouter(<StrategyTemplateLibrary />);
    
    // 等待策略卡片加载
    await waitFor(() => {
      // 检查现有策略
      expect(screen.getByText('双均线交叉策略')).toBeInTheDocument();
      expect(screen.getByText('RSI反转策略')).toBeInTheDocument();
      expect(screen.getByText('布林带策略')).toBeInTheDocument();
      expect(screen.getByText('动量策略')).toBeInTheDocument();
      expect(screen.getByText('均值回归策略')).toBeInTheDocument();
      
      // 检查新增策略
      expect(screen.getByText('MACD策略')).toBeInTheDocument();
      expect(screen.getByText('配对交易策略')).toBeInTheDocument();
      expect(screen.getByText('多因子模型策略')).toBeInTheDocument();
      expect(screen.getByText('网格交易策略')).toBeInTheDocument();
      expect(screen.getByText('支持向量机策略')).toBeInTheDocument();
    });
  });

  test('搜索功能应该正常工作', async () => {
    renderWithRouter(<StrategyTemplateLibrary />);
    
    const searchInput = screen.getByPlaceholderText('搜索策略名称、描述或标签...');
    
    // 搜索MACD策略
    fireEvent.change(searchInput, { target: { value: 'MACD' } });
    
    await waitFor(() => {
      expect(screen.getByText('MACD策略')).toBeInTheDocument();
      // 其他策略应该被过滤掉
      expect(screen.queryByText('RSI反转策略')).not.toBeInTheDocument();
    });
    
    // 清空搜索
    fireEvent.change(searchInput, { target: { value: '' } });
    
    await waitFor(() => {
      // 所有策略应该重新显示
      expect(screen.getByText('MACD策略')).toBeInTheDocument();
      expect(screen.getByText('RSI反转策略')).toBeInTheDocument();
    });
  });

  test('分类筛选应该正常工作', async () => {
    renderWithRouter(<StrategyTemplateLibrary />);
    
    // 点击分类选择器
    const categorySelect = screen.getByText('全部分类').closest('.ant-select');
    fireEvent.mouseDown(categorySelect.querySelector('.ant-select-selector'));
    
    // 选择技术指标分类
    await waitFor(() => {
      const technicalOption = screen.getByText('技术指标');
      fireEvent.click(technicalOption);
    });
    
    await waitFor(() => {
      // 应该只显示技术指标类策略
      expect(screen.getByText('MACD策略')).toBeInTheDocument();
      // 其他分类的策略应该被过滤
      expect(screen.queryByText('配对交易策略')).not.toBeInTheDocument();
    });
  });

  test('难度筛选应该正常工作', async () => {
    renderWithRouter(<StrategyTemplateLibrary />);
    
    // 点击难度选择器
    const difficultySelect = screen.getByText('全部难度').closest('.ant-select');
    fireEvent.mouseDown(difficultySelect.querySelector('.ant-select-selector'));
    
    // 选择高级难度
    await waitFor(() => {
      const advancedOption = screen.getByText('高级');
      fireEvent.click(advancedOption);
    });
    
    await waitFor(() => {
      // 应该只显示高级策略
      expect(screen.getByText('配对交易策略')).toBeInTheDocument();
      expect(screen.getByText('多因子模型策略')).toBeInTheDocument();
      expect(screen.getByText('支持向量机策略')).toBeInTheDocument();
      // 初级和中级策略应该被过滤
      expect(screen.queryByText('双均线交叉策略')).not.toBeInTheDocument();
    });
  });

  test('排序功能应该正常工作', async () => {
    renderWithRouter(<StrategyTemplateLibrary />);
    
    // 点击排序选择器
    const sortSelect = screen.getByText('按评分排序').closest('.ant-select');
    fireEvent.mouseDown(sortSelect.querySelector('.ant-select-selector'));
    
    // 选择按下载量排序
    await waitFor(() => {
      const downloadsOption = screen.getByText('按下载量排序');
      fireEvent.click(downloadsOption);
    });
    
    // 验证排序结果（这里需要根据实际数据验证）
    await waitFor(() => {
      const strategyCards = screen.getAllByTestId('strategy-card');
      expect(strategyCards.length).toBeGreaterThan(0);
    });
  });

  test('策略卡片应该显示正确信息', async () => {
    renderWithRouter(<StrategyTemplateLibrary />);
    
    await waitFor(() => {
      // 检查MACD策略卡片信息
      const macdCard = screen.getByText('MACD策略').closest('.ant-card');
      
      // 检查策略描述
      expect(macdCard).toHaveTextContent('基于MACD指标的金叉死叉信号进行交易');
      
      // 检查评分
      expect(macdCard).toHaveTextContent('4.4');
      
      // 检查下载量
      expect(macdCard).toHaveTextContent('980');
      
      // 检查标签
      expect(macdCard).toHaveTextContent('MACD');
      expect(macdCard).toHaveTextContent('趋势跟踪');
      expect(macdCard).toHaveTextContent('技术指标');
    });
  });

  test('查看策略功能应该正常工作', async () => {
    renderWithRouter(<StrategyTemplateLibrary />);
    
    await waitFor(() => {
      // 找到MACD策略的查看按钮
      const macdCard = screen.getByText('MACD策略').closest('.ant-card');
      const viewButton = macdCard.querySelector('[data-testid="view-strategy"]');
      
      // 点击查看按钮
      fireEvent.click(viewButton);
    });
    
    // 验证是否打开了策略详情（这里需要根据实际实现验证）
    // 例如检查是否显示了策略代码或详情模态框
  });

  test('下载策略功能应该正常工作', async () => {
    renderWithRouter(<StrategyTemplateLibrary />);
    
    await waitFor(() => {
      // 找到MACD策略的下载按钮
      const macdCard = screen.getByText('MACD策略').closest('.ant-card');
      const downloadButton = macdCard.querySelector('[data-testid="download-strategy"]');
      
      // 点击下载按钮
      fireEvent.click(downloadButton);
    });
    
    // 验证下载功能（这里需要根据实际实现验证）
    // 例如检查是否触发了下载或显示了成功消息
  });

  test('策略分类图标应该正确显示', async () => {
    renderWithRouter(<StrategyTemplateLibrary />);
    
    await waitFor(() => {
      // 检查不同分类的策略是否显示了正确的图标
      const strategyCards = screen.getAllByTestId('strategy-card');
      expect(strategyCards.length).toBeGreaterThan(0);
      
      // 这里可以检查特定策略的图标
      // 例如技术指标类策略应该显示LineChartOutlined图标
    });
  });

  test('响应式布局应该正常工作', () => {
    // 模拟不同屏幕尺寸
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 768, // 平板尺寸
    });
    
    renderWithRouter(<StrategyTemplateLibrary />);
    
    // 验证在小屏幕下的布局
    const container = screen.getByTestId('strategy-library-container');
    expect(container).toBeInTheDocument();
  });

  test('加载状态应该正确处理', async () => {
    renderWithRouter(<StrategyTemplateLibrary />);
    
    // 检查是否有加载指示器（如果有的话）
    // 这里需要根据实际实现来验证加载状态
    
    await waitFor(() => {
      // 验证加载完成后的状态
      expect(screen.getByText('策略模板库')).toBeInTheDocument();
    });
  });

  test('错误状态应该正确处理', async () => {
    // 模拟错误情况
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    
    // 这里可以模拟API错误或其他错误情况
    
    renderWithRouter(<StrategyTemplateLibrary />);
    
    // 验证错误处理
    await waitFor(() => {
      // 检查是否显示了错误信息或默认状态
      expect(screen.getByText('策略模板库')).toBeInTheDocument();
    });
    
    consoleSpy.mockRestore();
  });

  test('策略性能指标应该正确显示', async () => {
    renderWithRouter(<StrategyTemplateLibrary />);
    
    await waitFor(() => {
      // 检查MACD策略的性能指标
      const macdCard = screen.getByText('MACD策略').closest('.ant-card');
      
      // 检查年化收益率
      expect(macdCard).toHaveTextContent('16.8%');
      
      // 检查夏普比率
      expect(macdCard).toHaveTextContent('1.28');
      
      // 检查最大回撤
      expect(macdCard).toHaveTextContent('11.2%');
      
      // 检查胜率
      expect(macdCard).toHaveTextContent('59%');
    });
  });

  test('新增策略分类应该正确显示', async () => {
    renderWithRouter(<StrategyTemplateLibrary />);
    
    // 点击分类选择器
    const categorySelect = screen.getByText('全部分类').closest('.ant-select');
    fireEvent.mouseDown(categorySelect.querySelector('.ant-select-selector'));
    
    await waitFor(() => {
      // 检查新增的分类选项
      expect(screen.getByText('技术指标')).toBeInTheDocument();
      expect(screen.getByText('统计套利')).toBeInTheDocument();
      expect(screen.getByText('因子投资')).toBeInTheDocument();
      expect(screen.getByText('网格交易')).toBeInTheDocument();
      expect(screen.getByText('机器学习')).toBeInTheDocument();
    });
  });
});

// 集成测试
describe('StrategyTemplateLibrary Integration Tests', () => {
  test('完整的用户交互流程', async () => {
    renderWithRouter(<StrategyTemplateLibrary />);
    
    // 1. 页面加载
    await waitFor(() => {
      expect(screen.getByText('策略模板库')).toBeInTheDocument();
    });
    
    // 2. 搜索策略
    const searchInput = screen.getByPlaceholderText('搜索策略名称、描述或标签...');
    fireEvent.change(searchInput, { target: { value: '机器学习' } });
    
    await waitFor(() => {
      expect(screen.getByText('支持向量机策略')).toBeInTheDocument();
    });
    
    // 3. 查看策略详情
    const svmCard = screen.getByText('支持向量机策略').closest('.ant-card');
    const viewButton = svmCard.querySelector('[data-testid="view-strategy"]');
    fireEvent.click(viewButton);
    
    // 4. 清空搜索并按分类筛选
    fireEvent.change(searchInput, { target: { value: '' } });
    
    const categorySelect = screen.getByText('全部分类').closest('.ant-select');
    fireEvent.mouseDown(categorySelect.querySelector('.ant-select-selector'));
    
    await waitFor(() => {
      const arbitrageOption = screen.getByText('统计套利');
      fireEvent.click(arbitrageOption);
    });
    
    await waitFor(() => {
      expect(screen.getByText('配对交易策略')).toBeInTheDocument();
      expect(screen.queryByText('MACD策略')).not.toBeInTheDocument();
    });
  });
});
