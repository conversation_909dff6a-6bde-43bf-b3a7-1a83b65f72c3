/**
 * Python代码格式化器
 * 
 * 提供Python代码的格式化功能，包括缩进、空行、空格等的标准化
 * 遵循PEP 8编码规范
 */

/**
 * Python代码格式化器类
 */
export class PythonCodeFormatter {
  constructor() {
    this.indentSize = 4; // 缩进大小
    this.maxLineLength = 88; // 最大行长度（Black风格）
  }

  /**
   * 格式化Python代码
   * @param {string} code - 原始代码
   * @returns {string} - 格式化后的代码
   */
  format(code) {
    if (!code || typeof code !== 'string') {
      return code;
    }

    const lines = code.split('\n');
    const formattedLines = [];
    let indentLevel = 0;
    let inMultilineString = false;
    let multilineStringChar = '';
    let lastLineWasEmpty = false;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmedLine = line.trim();
      
      // 处理多行字符串
      if (this.isMultilineStringStart(trimmedLine)) {
        inMultilineString = true;
        multilineStringChar = this.getMultilineStringChar(trimmedLine);
      }
      
      if (inMultilineString) {
        formattedLines.push(line);
        if (this.isMultilineStringEnd(trimmedLine, multilineStringChar)) {
          inMultilineString = false;
          multilineStringChar = '';
        }
        continue;
      }

      // 跳过空行，但记录状态
      if (trimmedLine === '') {
        if (!lastLineWasEmpty) {
          formattedLines.push('');
          lastLineWasEmpty = true;
        }
        continue;
      }

      lastLineWasEmpty = false;

      // 处理注释行
      if (trimmedLine.startsWith('#')) {
        const formattedComment = this.formatComment(trimmedLine, indentLevel);
        formattedLines.push(formattedComment);
        continue;
      }

      // 计算缩进级别
      const newIndentLevel = this.calculateIndentLevel(trimmedLine, indentLevel);
      
      // 格式化代码行
      const formattedLine = this.formatCodeLine(trimmedLine, newIndentLevel);
      formattedLines.push(formattedLine);

      // 更新缩进级别
      indentLevel = this.updateIndentLevel(trimmedLine, newIndentLevel);
    }

    // 移除末尾多余的空行
    while (formattedLines.length > 0 && formattedLines[formattedLines.length - 1] === '') {
      formattedLines.pop();
    }

    // 确保文件以换行符结尾
    if (formattedLines.length > 0) {
      formattedLines.push('');
    }

    return formattedLines.join('\n');
  }

  /**
   * 检查是否是多行字符串开始
   */
  isMultilineStringStart(line) {
    return line.includes('"""') || line.includes("'''");
  }

  /**
   * 获取多行字符串字符
   */
  getMultilineStringChar(line) {
    if (line.includes('"""')) return '"""';
    if (line.includes("'''")) return "'''";
    return '';
  }

  /**
   * 检查是否是多行字符串结束
   */
  isMultilineStringEnd(line, char) {
    return char && line.includes(char) && line.indexOf(char) !== line.lastIndexOf(char);
  }

  /**
   * 格式化注释
   */
  formatComment(comment, indentLevel) {
    const indent = ' '.repeat(indentLevel * this.indentSize);
    
    // 确保注释前有一个空格
    if (comment.startsWith('#') && comment.length > 1 && comment[1] !== ' ') {
      comment = '# ' + comment.substring(1);
    }
    
    return indent + comment;
  }

  /**
   * 计算缩进级别
   */
  calculateIndentLevel(line, currentLevel) {
    // 减少缩进的关键字
    const dedentKeywords = ['elif', 'else', 'except', 'finally'];
    
    for (const keyword of dedentKeywords) {
      if (line.startsWith(keyword + ' ') || line === keyword + ':') {
        return Math.max(0, currentLevel - 1);
      }
    }
    
    return currentLevel;
  }

  /**
   * 更新缩进级别
   */
  updateIndentLevel(line, currentLevel) {
    // 增加缩进的情况
    if (line.endsWith(':')) {
      return currentLevel + 1;
    }
    
    return currentLevel;
  }

  /**
   * 格式化代码行
   */
  formatCodeLine(line, indentLevel) {
    const indent = ' '.repeat(indentLevel * this.indentSize);
    
    // 格式化操作符周围的空格
    line = this.formatOperators(line);
    
    // 格式化逗号后的空格
    line = this.formatCommas(line);
    
    // 格式化括号内的空格
    line = this.formatBrackets(line);
    
    // 格式化冒号后的空格
    line = this.formatColons(line);
    
    // 移除行尾空格
    line = line.trimEnd();
    
    return indent + line;
  }

  /**
   * 格式化操作符
   */
  formatOperators(line) {
    // 赋值操作符
    line = line.replace(/\s*=\s*/g, ' = ');
    line = line.replace(/\s*\+=\s*/g, ' += ');
    line = line.replace(/\s*-=\s*/g, ' -= ');
    line = line.replace(/\s*\*=\s*/g, ' *= ');
    line = line.replace(/\s*\/=\s*/g, ' /= ');
    
    // 比较操作符
    line = line.replace(/\s*==\s*/g, ' == ');
    line = line.replace(/\s*!=\s*/g, ' != ');
    line = line.replace(/\s*<=\s*/g, ' <= ');
    line = line.replace(/\s*>=\s*/g, ' >= ');
    line = line.replace(/\s*<\s*/g, ' < ');
    line = line.replace(/\s*>\s*/g, ' > ');
    
    // 算术操作符
    line = line.replace(/\s*\+\s*/g, ' + ');
    line = line.replace(/\s*-\s*/g, ' - ');
    line = line.replace(/\s*\*\s*/g, ' * ');
    line = line.replace(/\s*\/\s*/g, ' / ');
    line = line.replace(/\s*%\s*/g, ' % ');
    
    // 逻辑操作符
    line = line.replace(/\s+and\s+/g, ' and ');
    line = line.replace(/\s+or\s+/g, ' or ');
    line = line.replace(/\s+not\s+/g, ' not ');
    
    return line;
  }

  /**
   * 格式化逗号
   */
  formatCommas(line) {
    // 逗号后添加空格，逗号前移除空格
    return line.replace(/\s*,\s*/g, ', ');
  }

  /**
   * 格式化括号
   */
  formatBrackets(line) {
    // 移除括号内侧的空格
    line = line.replace(/\(\s+/g, '(');
    line = line.replace(/\s+\)/g, ')');
    line = line.replace(/\[\s+/g, '[');
    line = line.replace(/\s+\]/g, ']');
    line = line.replace(/\{\s+/g, '{');
    line = line.replace(/\s+\}/g, '}');
    
    return line;
  }

  /**
   * 格式化冒号
   */
  formatColons(line) {
    // 冒号前不加空格，冒号后加空格（除非在行尾）
    line = line.replace(/\s*:\s*/g, ': ');
    line = line.replace(/: $/, ':');
    
    return line;
  }

  /**
   * 检查行是否过长
   */
  isLineTooLong(line) {
    return line.length > this.maxLineLength;
  }

  /**
   * 分割过长的行
   */
  splitLongLine(line, indentLevel) {
    if (!this.isLineTooLong(line)) {
      return [line];
    }

    const indent = ' '.repeat(indentLevel * this.indentSize);
    const continuationIndent = ' '.repeat((indentLevel + 1) * this.indentSize);
    
    // 简单的行分割逻辑
    // 在实际应用中，这里应该更智能地处理
    const parts = [];
    let currentPart = '';
    const words = line.split(' ');
    
    for (const word of words) {
      if ((currentPart + ' ' + word).length > this.maxLineLength) {
        if (currentPart) {
          parts.push(currentPart.trim());
          currentPart = continuationIndent + word;
        } else {
          parts.push(word);
        }
      } else {
        currentPart += (currentPart ? ' ' : '') + word;
      }
    }
    
    if (currentPart) {
      parts.push(currentPart.trim());
    }
    
    return parts;
  }
}

/**
 * 配置Monaco Editor的代码格式化功能
 * @param {Object} monaco - Monaco Editor实例
 */
export const configureCodeFormatter = (monaco) => {
  const formatter = new PythonCodeFormatter();
  
  // 注册文档格式化提供者
  monaco.languages.registerDocumentFormattingEditProvider('python', {
    provideDocumentFormattingEdits: (model) => {
      const code = model.getValue();
      const formattedCode = formatter.format(code);
      
      return [
        {
          range: model.getFullModelRange(),
          text: formattedCode
        }
      ];
    }
  });
  
  // 注册选择区域格式化提供者
  monaco.languages.registerDocumentRangeFormattingEditProvider('python', {
    provideDocumentRangeFormattingEdits: (model, range) => {
      const selectedText = model.getValueInRange(range);
      const formattedText = formatter.format(selectedText);
      
      return [
        {
          range: range,
          text: formattedText
        }
      ];
    }
  });
  
  console.log('Python代码格式化配置完成');
};

/**
 * 快速格式化代码
 * @param {string} code - 原始代码
 * @returns {string} - 格式化后的代码
 */
export const formatPythonCode = (code) => {
  const formatter = new PythonCodeFormatter();
  return formatter.format(code);
};
