/**
 * Python语法检查器
 * 
 * 提供Python代码的语法检查功能，检测常见的语法错误
 * 包括缩进错误、语法错误、未定义变量等
 */

/**
 * Python关键字列表
 */
const PYTHON_KEYWORDS = [
  'and', 'as', 'assert', 'break', 'class', 'continue', 'def', 'del', 'elif', 'else',
  'except', 'exec', 'finally', 'for', 'from', 'global', 'if', 'import', 'in', 'is',
  'lambda', 'not', 'or', 'pass', 'print', 'raise', 'return', 'try', 'while', 'with',
  'yield', 'async', 'await', 'nonlocal', 'True', 'False', 'None'
];

/**
 * Python内置函数列表
 */
const PYTHON_BUILTINS = [
  'abs', 'all', 'any', 'bin', 'bool', 'bytearray', 'bytes', 'callable', 'chr',
  'classmethod', 'compile', 'complex', 'delattr', 'dict', 'dir', 'divmod',
  'enumerate', 'eval', 'exec', 'filter', 'float', 'format', 'frozenset',
  'getattr', 'globals', 'hasattr', 'hash', 'help', 'hex', 'id', 'input', 'int',
  'isinstance', 'issubclass', 'iter', 'len', 'list', 'locals', 'map', 'max',
  'memoryview', 'min', 'next', 'object', 'oct', 'open', 'ord', 'pow', 'print',
  'property', 'range', 'repr', 'reversed', 'round', 'set', 'setattr', 'slice',
  'sorted', 'staticmethod', 'str', 'sum', 'super', 'tuple', 'type', 'vars', 'zip'
];

/**
 * 量化交易API函数列表
 */
const QUANT_API_FUNCTIONS = [
  'initialize', 'handle_data', 'before_trading_start', 'after_trading_end',
  'order_shares', 'order_value', 'order_percent', 'order_target_shares',
  'order_target_value', 'order_target_percent', 'cancel_order',
  'get_current_price', 'get_history', 'get_fundamentals', 'get_current_data',
  'get_datetime', 'get_open_orders', 'get_portfolio', 'get_benchmark_returns',
  'sma', 'ema', 'wma', 'rsi', 'macd', 'bollinger_bands', 'stochastic',
  'williams_r', 'cci', 'atr', 'adx', 'momentum', 'roc', 'trix',
  'set_max_leverage', 'set_max_position_size', 'set_order_cost',
  'set_slippage', 'set_commission', 'record', 'log'
];

/**
 * 语法错误类型枚举
 */
export const SyntaxErrorType = {
  INDENTATION_ERROR: 'indentation_error',
  SYNTAX_ERROR: 'syntax_error',
  UNDEFINED_VARIABLE: 'undefined_variable',
  INVALID_FUNCTION: 'invalid_function',
  MISSING_COLON: 'missing_colon',
  UNMATCHED_BRACKETS: 'unmatched_brackets',
  INVALID_STRING: 'invalid_string',
  INVALID_IMPORT: 'invalid_import',
  UNREACHABLE_CODE: 'unreachable_code'
};

/**
 * 语法检查结果
 */
class SyntaxError {
  constructor(type, message, line, column, severity = 'error') {
    this.type = type;
    this.message = message;
    this.line = line;
    this.column = column;
    this.severity = severity; // 'error', 'warning', 'info'
  }
}

/**
 * Python语法检查器类
 */
export class PythonSyntaxValidator {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.definedVariables = new Set();
    this.definedFunctions = new Set();
    this.importedModules = new Set();
  }

  /**
   * 验证Python代码语法
   * @param {string} code - Python代码
   * @returns {Array} - 错误和警告列表
   */
  validate(code) {
    this.errors = [];
    this.warnings = [];
    this.definedVariables = new Set(['context', 'data', 'self']); // 预定义变量
    this.definedFunctions = new Set([...QUANT_API_FUNCTIONS, ...PYTHON_BUILTINS]);
    this.importedModules = new Set();

    const lines = code.split('\n');
    
    // 第一遍：收集定义的变量和函数
    this.collectDefinitions(lines);
    
    // 第二遍：检查语法错误
    this.checkSyntaxErrors(lines);
    
    return [...this.errors, ...this.warnings];
  }

  /**
   * 收集代码中定义的变量和函数
   * @param {Array} lines - 代码行数组
   */
  collectDefinitions(lines) {
    lines.forEach((line, index) => {
      const trimmedLine = line.trim();
      
      // 收集函数定义
      const funcMatch = trimmedLine.match(/^def\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/);
      if (funcMatch) {
        this.definedFunctions.add(funcMatch[1]);
      }
      
      // 收集类定义
      const classMatch = trimmedLine.match(/^class\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*[\(:]?/);
      if (classMatch) {
        this.definedFunctions.add(classMatch[1]);
      }
      
      // 收集变量赋值
      const assignMatch = trimmedLine.match(/^([a-zA-Z_][a-zA-Z0-9_]*)\s*=/);
      if (assignMatch) {
        this.definedVariables.add(assignMatch[1]);
      }
      
      // 收集for循环变量
      const forMatch = trimmedLine.match(/^for\s+([a-zA-Z_][a-zA-Z0-9_]*)\s+in/);
      if (forMatch) {
        this.definedVariables.add(forMatch[1]);
      }
      
      // 收集import语句
      const importMatch = trimmedLine.match(/^import\s+([a-zA-Z_][a-zA-Z0-9_.]*)/);
      if (importMatch) {
        this.importedModules.add(importMatch[1]);
      }
      
      const fromImportMatch = trimmedLine.match(/^from\s+([a-zA-Z_][a-zA-Z0-9_.]*)\s+import/);
      if (fromImportMatch) {
        this.importedModules.add(fromImportMatch[1]);
      }
    });
  }

  /**
   * 检查语法错误
   * @param {Array} lines - 代码行数组
   */
  checkSyntaxErrors(lines) {
    let bracketStack = [];
    let inString = false;
    let stringChar = '';
    let expectedIndent = 0;
    let lastLineWasColon = false;

    lines.forEach((line, index) => {
      const lineNumber = index + 1;
      const trimmedLine = line.trim();
      
      // 跳过空行和注释
      if (!trimmedLine || trimmedLine.startsWith('#')) {
        return;
      }

      // 检查缩进错误
      this.checkIndentation(line, lineNumber, expectedIndent);
      
      // 检查冒号缺失
      this.checkMissingColon(line, lineNumber);
      
      // 检查括号匹配
      this.checkBracketMatching(line, lineNumber, bracketStack);
      
      // 检查字符串语法
      this.checkStringSyntax(line, lineNumber);
      
      // 检查未定义变量
      this.checkUndefinedVariables(line, lineNumber);
      
      // 检查无效函数调用
      this.checkInvalidFunctions(line, lineNumber);
      
      // 检查不可达代码
      this.checkUnreachableCode(line, lineNumber, trimmedLine);
      
      // 更新期望缩进
      if (trimmedLine.endsWith(':')) {
        expectedIndent = this.getIndentLevel(line) + 4;
        lastLineWasColon = true;
      } else if (lastLineWasColon && this.getIndentLevel(line) <= expectedIndent - 4) {
        expectedIndent = this.getIndentLevel(line);
        lastLineWasColon = false;
      }
    });

    // 检查未匹配的括号
    if (bracketStack.length > 0) {
      this.addError(
        SyntaxErrorType.UNMATCHED_BRACKETS,
        '存在未匹配的括号',
        lines.length,
        1
      );
    }
  }

  /**
   * 检查缩进错误
   */
  checkIndentation(line, lineNumber, expectedIndent) {
    if (line.trim() === '') return;
    
    const actualIndent = this.getIndentLevel(line);
    
    // 检查制表符和空格混用
    if (line.match(/^\s*\t/) && line.match(/^\s* /)) {
      this.addWarning(
        SyntaxErrorType.INDENTATION_ERROR,
        '不要混用制表符和空格进行缩进',
        lineNumber,
        1
      );
    }
    
    // 检查制表符使用
    if (line.match(/^\s*\t/)) {
      this.addWarning(
        SyntaxErrorType.INDENTATION_ERROR,
        '建议使用空格而不是制表符进行缩进',
        lineNumber,
        1
      );
    }
    
    // 检查缩进是否为4的倍数
    if (actualIndent % 4 !== 0) {
      this.addWarning(
        SyntaxErrorType.INDENTATION_ERROR,
        '建议使用4个空格作为缩进单位',
        lineNumber,
        1
      );
    }
  }

  /**
   * 检查冒号缺失
   */
  checkMissingColon(line, lineNumber) {
    const trimmedLine = line.trim();
    
    // 检查if、for、while、def、class等语句是否缺少冒号
    const colonStatements = [
      /^if\s+.+[^:]$/,
      /^elif\s+.+[^:]$/,
      /^else\s*[^:]$/,
      /^for\s+.+[^:]$/,
      /^while\s+.+[^:]$/,
      /^def\s+[a-zA-Z_][a-zA-Z0-9_]*\s*\([^)]*\)\s*[^:]$/,
      /^class\s+[a-zA-Z_][a-zA-Z0-9_]*.*[^:]$/,
      /^try\s*[^:]$/,
      /^except.*[^:]$/,
      /^finally\s*[^:]$/,
      /^with\s+.+[^:]$/
    ];
    
    colonStatements.forEach(pattern => {
      if (pattern.test(trimmedLine)) {
        this.addError(
          SyntaxErrorType.MISSING_COLON,
          '语句末尾缺少冒号',
          lineNumber,
          line.length
        );
      }
    });
  }

  /**
   * 检查括号匹配
   */
  checkBracketMatching(line, lineNumber, bracketStack) {
    const brackets = { '(': ')', '[': ']', '{': '}' };
    const openBrackets = Object.keys(brackets);
    const closeBrackets = Object.values(brackets);
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i];
      
      if (openBrackets.includes(char)) {
        bracketStack.push({ char, line: lineNumber, column: i + 1 });
      } else if (closeBrackets.includes(char)) {
        if (bracketStack.length === 0) {
          this.addError(
            SyntaxErrorType.UNMATCHED_BRACKETS,
            `多余的闭合括号 '${char}'`,
            lineNumber,
            i + 1
          );
        } else {
          const lastOpen = bracketStack.pop();
          if (brackets[lastOpen.char] !== char) {
            this.addError(
              SyntaxErrorType.UNMATCHED_BRACKETS,
              `括号不匹配：期望 '${brackets[lastOpen.char]}'，实际 '${char}'`,
              lineNumber,
              i + 1
            );
          }
        }
      }
    }
  }

  /**
   * 检查字符串语法
   */
  checkStringSyntax(line, lineNumber) {
    // 简单的字符串语法检查
    const stringPatterns = [
      /^[^"]*"[^"]*"[^"]*$/,  // 双引号字符串
      /^[^']*'[^']*'[^']*$/   // 单引号字符串
    ];
    
    // 检查未闭合的字符串
    const unclosedString = line.match(/["'][^"']*$/);
    if (unclosedString) {
      this.addError(
        SyntaxErrorType.INVALID_STRING,
        '字符串未正确闭合',
        lineNumber,
        line.length
      );
    }
  }

  /**
   * 检查未定义变量
   */
  checkUndefinedVariables(line, lineNumber) {
    const trimmedLine = line.trim();
    
    // 跳过定义语句
    if (trimmedLine.match(/^(def|class|import|from)/)) {
      return;
    }
    
    // 查找变量使用
    const variableMatches = trimmedLine.match(/\b([a-zA-Z_][a-zA-Z0-9_]*)\b/g);
    if (variableMatches) {
      variableMatches.forEach(variable => {
        if (!PYTHON_KEYWORDS.includes(variable) &&
            !this.definedVariables.has(variable) &&
            !this.definedFunctions.has(variable) &&
            !this.importedModules.has(variable)) {
          
          // 检查是否是属性访问
          const isAttribute = trimmedLine.includes(`.${variable}`) || 
                             trimmedLine.includes(`${variable}.`);
          
          if (!isAttribute) {
            this.addWarning(
              SyntaxErrorType.UNDEFINED_VARIABLE,
              `可能使用了未定义的变量: ${variable}`,
              lineNumber,
              line.indexOf(variable) + 1
            );
          }
        }
      });
    }
  }

  /**
   * 检查无效函数调用
   */
  checkInvalidFunctions(line, lineNumber) {
    const functionCalls = line.match(/([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/g);
    if (functionCalls) {
      functionCalls.forEach(call => {
        const funcName = call.replace(/\s*\($/, '');
        if (!this.definedFunctions.has(funcName) && 
            !PYTHON_KEYWORDS.includes(funcName)) {
          
          this.addWarning(
            SyntaxErrorType.INVALID_FUNCTION,
            `可能调用了未定义的函数: ${funcName}`,
            lineNumber,
            line.indexOf(call) + 1
          );
        }
      });
    }
  }

  /**
   * 检查不可达代码
   */
  checkUnreachableCode(line, lineNumber, trimmedLine) {
    // 检查return、break、continue后的代码
    if (trimmedLine.match(/^(return|break|continue)\b/)) {
      // 标记下一行可能是不可达代码
      // 这里简化处理，实际应该更复杂
    }
  }

  /**
   * 获取行的缩进级别
   */
  getIndentLevel(line) {
    const match = line.match(/^(\s*)/);
    return match ? match[1].length : 0;
  }

  /**
   * 添加错误
   */
  addError(type, message, line, column) {
    this.errors.push(new SyntaxError(type, message, line, column, 'error'));
  }

  /**
   * 添加警告
   */
  addWarning(type, message, line, column) {
    this.warnings.push(new SyntaxError(type, message, line, column, 'warning'));
  }
}

/**
 * 配置Monaco Editor的语法验证
 * @param {Object} monaco - Monaco Editor实例
 */
export const configureSyntaxValidation = (monaco) => {
  const validator = new PythonSyntaxValidator();
  
  // 创建语法验证函数
  const validateSyntax = (model) => {
    const code = model.getValue();
    const errors = validator.validate(code);
    
    // 转换为Monaco Editor的标记格式
    const markers = errors.map(error => ({
      severity: error.severity === 'error' 
        ? monaco.MarkerSeverity.Error 
        : monaco.MarkerSeverity.Warning,
      startLineNumber: error.line,
      startColumn: error.column,
      endLineNumber: error.line,
      endColumn: error.column + 10, // 简化处理
      message: error.message,
      source: 'python-syntax-checker'
    }));
    
    // 设置标记
    monaco.editor.setModelMarkers(model, 'python-syntax', markers);
  };
  
  // 监听模型变化
  monaco.editor.onDidCreateModel((model) => {
    if (model.getLanguageId() === 'python') {
      // 初始验证
      validateSyntax(model);
      
      // 监听内容变化
      model.onDidChangeContent(() => {
        // 防抖处理
        setTimeout(() => validateSyntax(model), 500);
      });
    }
  });
  
  console.log('Python语法验证配置完成');
};
