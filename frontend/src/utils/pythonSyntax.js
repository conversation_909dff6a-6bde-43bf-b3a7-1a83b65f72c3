/**
 * Python语法高亮配置
 * 
 * 为Monaco Editor提供详细的Python语法高亮规则
 * 包括关键字、操作符、字符串、注释、数字等的高亮配置
 */

/**
 * Python关键字列表
 */
export const PYTHON_KEYWORDS = [
  'and', 'as', 'assert', 'break', 'class', 'continue', 'def', 'del', 'elif', 'else',
  'except', 'exec', 'finally', 'for', 'from', 'global', 'if', 'import', 'in', 'is',
  'lambda', 'not', 'or', 'pass', 'print', 'raise', 'return', 'try', 'while', 'with',
  'yield', 'async', 'await', 'nonlocal'
];

/**
 * Python内置函数列表
 */
export const PYTHON_BUILTINS = [
  'abs', 'all', 'any', 'bin', 'bool', 'bytearray', 'bytes', 'callable', 'chr',
  'classmethod', 'compile', 'complex', 'delattr', 'dict', 'dir', 'divmod',
  'enumerate', 'eval', 'exec', 'filter', 'float', 'format', 'frozenset',
  'getattr', 'globals', 'hasattr', 'hash', 'help', 'hex', 'id', 'input', 'int',
  'isinstance', 'issubclass', 'iter', 'len', 'list', 'locals', 'map', 'max',
  'memoryview', 'min', 'next', 'object', 'oct', 'open', 'ord', 'pow', 'print',
  'property', 'range', 'repr', 'reversed', 'round', 'set', 'setattr', 'slice',
  'sorted', 'staticmethod', 'str', 'sum', 'super', 'tuple', 'type', 'vars', 'zip'
];

/**
 * 量化交易相关的关键字和函数
 */
export const QUANT_KEYWORDS = [
  // 策略函数
  'initialize', 'handle_data', 'before_trading_start', 'after_trading_end',
  
  // 交易函数
  'order_shares', 'order_value', 'order_percent', 'order_target_shares',
  'order_target_value', 'order_target_percent', 'cancel_order',
  
  // 数据获取函数
  'get_current_price', 'get_history', 'get_fundamentals', 'get_current_data',
  'get_datetime', 'get_open_orders', 'get_portfolio', 'get_benchmark_returns',
  
  // 技术指标函数
  'sma', 'ema', 'wma', 'rsi', 'macd', 'bollinger_bands', 'stochastic',
  'williams_r', 'cci', 'atr', 'adx', 'momentum', 'roc', 'trix',
  
  // 数学和统计函数
  'mean', 'std', 'var', 'corr', 'cov', 'rolling_mean', 'rolling_std',
  'rolling_var', 'rolling_corr', 'rolling_cov', 'ewm_mean', 'ewm_std',
  
  // 风险管理函数
  'set_max_leverage', 'set_max_position_size', 'set_order_cost',
  'set_slippage', 'set_commission', 'record', 'log'
];

/**
 * Python语法高亮主题配置
 */
export const PYTHON_THEME_CONFIG = {
  base: 'vs-dark',
  inherit: true,
  rules: [
    // 关键字
    { token: 'keyword', foreground: '569CD6', fontStyle: 'bold' },
    { token: 'keyword.control', foreground: 'C586C0', fontStyle: 'bold' },
    { token: 'keyword.operator', foreground: '569CD6' },
    
    // 字符串
    { token: 'string', foreground: 'CE9178' },
    { token: 'string.escape', foreground: 'D7BA7D' },
    { token: 'string.interpolated', foreground: '9CDCFE' },
    
    // 注释
    { token: 'comment', foreground: '6A9955', fontStyle: 'italic' },
    { token: 'comment.doc', foreground: '7F848E', fontStyle: 'italic' },
    
    // 数字
    { token: 'number', foreground: 'B5CEA8' },
    { token: 'number.float', foreground: 'B5CEA8' },
    { token: 'number.hex', foreground: 'B5CEA8' },
    { token: 'number.octal', foreground: 'B5CEA8' },
    { token: 'number.binary', foreground: 'B5CEA8' },
    
    // 函数和类
    { token: 'function', foreground: 'DCDCAA' },
    { token: 'function.builtin', foreground: '4FC1FF' },
    { token: 'function.quant', foreground: '4EC9B0', fontStyle: 'bold' },
    { token: 'class', foreground: '4EC9B0', fontStyle: 'bold' },
    { token: 'class.builtin', foreground: '4FC1FF' },
    
    // 变量和标识符
    { token: 'variable', foreground: '9CDCFE' },
    { token: 'variable.parameter', foreground: '9CDCFE' },
    { token: 'variable.other', foreground: 'D4D4D4' },
    
    // 操作符
    { token: 'operator', foreground: 'D4D4D4' },
    { token: 'operator.arithmetic', foreground: 'D4D4D4' },
    { token: 'operator.comparison', foreground: 'D4D4D4' },
    { token: 'operator.logical', foreground: '569CD6' },
    
    // 分隔符
    { token: 'delimiter', foreground: 'D4D4D4' },
    { token: 'delimiter.bracket', foreground: 'FFD700' },
    { token: 'delimiter.parenthesis', foreground: 'FFD700' },
    { token: 'delimiter.square', foreground: 'FFD700' },
    
    // 装饰器
    { token: 'decorator', foreground: 'FFCB6B', fontStyle: 'bold' },
    
    // 类型注解
    { token: 'type', foreground: '4EC9B0' },
    { token: 'type.annotation', foreground: '4EC9B0' },
    
    // 常量
    { token: 'constant', foreground: '4FC1FF' },
    { token: 'constant.builtin', foreground: '569CD6' },
    
    // 异常
    { token: 'exception', foreground: 'F44747', fontStyle: 'bold' }
  ],
  colors: {
    'editor.background': '#1E1E1E',
    'editor.foreground': '#D4D4D4',
    'editorLineNumber.foreground': '#858585',
    'editorLineNumber.activeForeground': '#C6C6C6',
    'editor.selectionBackground': '#264F78',
    'editor.selectionHighlightBackground': '#ADD6FF26',
    'editor.inactiveSelectionBackground': '#3A3D41',
    'editor.wordHighlightBackground': '#575757B8',
    'editor.wordHighlightStrongBackground': '#004972B8',
    'editor.findMatchBackground': '#515C6A',
    'editor.findMatchHighlightBackground': '#EA5C0055',
    'editor.findRangeHighlightBackground': '#3A3D4166',
    'editor.hoverHighlightBackground': '#264F7840',
    'editor.lineHighlightBackground': '#2A2D2E',
    'editor.rangeHighlightBackground': '#FFFFFF0B',
    'editorCursor.foreground': '#AEAFAD',
    'editorWhitespace.foreground': '#404040',
    'editorIndentGuide.background': '#404040',
    'editorIndentGuide.activeBackground': '#707070',
    'editorRuler.foreground': '#5A5A5A'
  }
};

/**
 * Python语言配置
 */
export const PYTHON_LANGUAGE_CONFIG = {
  comments: {
    lineComment: '#',
    blockComment: ['"""', '"""']
  },
  brackets: [
    ['{', '}'],
    ['[', ']'],
    ['(', ')']
  ],
  autoClosingPairs: [
    { open: '{', close: '}' },
    { open: '[', close: ']' },
    { open: '(', close: ')' },
    { open: '"', close: '"', notIn: ['string'] },
    { open: "'", close: "'", notIn: ['string', 'comment'] },
    { open: '"""', close: '"""' },
    { open: "'''", close: "'''" }
  ],
  surroundingPairs: [
    { open: '{', close: '}' },
    { open: '[', close: ']' },
    { open: '(', close: ')' },
    { open: '"', close: '"' },
    { open: "'", close: "'" },
    { open: '"""', close: '"""' },
    { open: "'''", close: "'''" }
  ],
  folding: {
    markers: {
      start: new RegExp('^\\s*#region\\b'),
      end: new RegExp('^\\s*#endregion\\b')
    }
  },
  indentationRules: {
    increaseIndentPattern: /^\s*(def|class|if|elif|else|for|while|with|try|except|finally|async def).*:$/,
    decreaseIndentPattern: /^\s*(elif|else|except|finally)\b.*$/
  }
};

/**
 * Python语法标记器配置
 */
export const PYTHON_TOKENIZER_CONFIG = {
  defaultToken: 'invalid',
  tokenPostfix: '.python',
  
  keywords: PYTHON_KEYWORDS,
  builtins: PYTHON_BUILTINS,
  quantKeywords: QUANT_KEYWORDS,
  
  operators: [
    '=', '>', '<', '!', '~', '?', ':', '==', '<=', '>=', '!=',
    '&&', '||', '++', '--', '+', '-', '*', '/', '&', '|', '^', '%',
    '<<', '>>', '>>>', '+=', '-=', '*=', '/=', '&=', '|=', '^=',
    '%=', '<<=', '>>=', '>>>='
  ],
  
  symbols: /[=><!~?:&|+\-*\/\^%]+/,
  escapes: /\\(?:[abfnrtv\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,
  
  tokenizer: {
    root: [
      // 标识符和关键字
      [/[a-zA-Z_]\w*/, {
        cases: {
          '@keywords': 'keyword',
          '@builtins': 'function.builtin',
          '@quantKeywords': 'function.quant',
          '@default': 'identifier'
        }
      }],
      
      // 装饰器
      [/@[a-zA-Z_]\w*/, 'decorator'],
      
      // 字符串
      [/"""/, 'string', '@string_triple_double'],
      [/'''/, 'string', '@string_triple_single'],
      [/"/, 'string', '@string_double'],
      [/'/, 'string', '@string_single'],
      
      // 数字
      [/0[xX][0-9a-fA-F]+/, 'number.hex'],
      [/0[oO][0-7]+/, 'number.octal'],
      [/0[bB][01]+/, 'number.binary'],
      [/\d*\.\d+([eE][\-+]?\d+)?/, 'number.float'],
      [/\d+[eE][\-+]?\d+/, 'number.float'],
      [/\d+/, 'number'],
      
      // 操作符和分隔符
      [/[{}()\[\]]/, '@brackets'],
      [/@symbols/, {
        cases: {
          '@operators': 'operator',
          '@default': ''
        }
      }],
      
      // 注释
      [/#.*$/, 'comment'],
      
      // 空白字符
      [/[ \t\r\n]+/, 'white']
    ],
    
    string_double: [
      [/[^\\"]+/, 'string'],
      [/@escapes/, 'string.escape'],
      [/\\./, 'string.escape.invalid'],
      [/"/, 'string', '@pop']
    ],
    
    string_single: [
      [/[^\\']+/, 'string'],
      [/@escapes/, 'string.escape'],
      [/\\./, 'string.escape.invalid'],
      [/'/, 'string', '@pop']
    ],
    
    string_triple_double: [
      [/[^"\\]+/, 'string'],
      [/@escapes/, 'string.escape'],
      [/\\./, 'string.escape.invalid'],
      [/"""/, 'string', '@pop'],
      [/"/, 'string']
    ],
    
    string_triple_single: [
      [/[^'\\]+/, 'string'],
      [/@escapes/, 'string.escape'],
      [/\\./, 'string.escape.invalid'],
      [/'''/, 'string', '@pop'],
      [/'/, 'string']
    ]
  }
};

/**
 * 配置Monaco Editor的Python语法高亮
 * @param {Object} monaco - Monaco Editor实例
 */
export const configurePythonSyntaxHighlighting = (monaco) => {
  // 注册Python语言
  monaco.languages.register({ id: 'python' });
  
  // 设置语言配置
  monaco.languages.setLanguageConfiguration('python', PYTHON_LANGUAGE_CONFIG);
  
  // 设置语法标记器
  monaco.languages.setMonarchTokensProvider('python', PYTHON_TOKENIZER_CONFIG);
  
  // 定义主题
  monaco.editor.defineTheme('pythonTheme', PYTHON_THEME_CONFIG);
  
  // 设置主题
  monaco.editor.setTheme('pythonTheme');
  
  console.log('Python语法高亮配置完成');
};

/**
 * 获取Python代码片段模板
 */
export const getPythonCodeTemplates = () => {
  return {
    strategy_basic: {
      label: '基础策略模板',
      description: '包含初始化和主逻辑函数的基础策略模板',
      code: `def initialize(context):
    """
    策略初始化函数
    在策略开始运行前调用一次
    """
    # 设置交易标的
    context.symbol = '000001.SZ'
    
    # 设置基准
    context.benchmark = '000300.SH'
    
    # 设置手续费
    set_order_cost(OrderCost(close_tax=0.001, open_commission=0.0003,
                            close_commission=0.0003, min_commission=5), type='stock')

def handle_data(context, data):
    """
    策略主逻辑函数
    每个交易周期调用一次
    """
    # 获取当前价格
    current_price = get_current_price(context.symbol)
    
    # 记录价格
    record(price=current_price)
    
    # 在这里编写你的交易逻辑
    pass`
    },
    
    ma_strategy: {
      label: '移动平均策略',
      description: '基于双移动平均线的交易策略',
      code: `def initialize(context):
    """移动平均策略初始化"""
    context.symbol = '000001.SZ'
    context.short_window = 5
    context.long_window = 20

def handle_data(context, data):
    """移动平均策略主逻辑"""
    # 获取历史价格数据
    prices = get_history([context.symbol], 'close', context.long_window)
    
    # 计算移动平均线
    short_ma = sma(prices[context.symbol], context.short_window)
    long_ma = sma(prices[context.symbol], context.long_window)
    
    # 获取当前持仓
    current_position = get_portfolio().positions[context.symbol].amount
    
    # 交易逻辑
    if short_ma[-1] > long_ma[-1] and current_position == 0:
        # 金叉买入
        order_value(context.symbol, 10000)
        log.info(f"买入信号: 短期均线{short_ma[-1]:.2f} > 长期均线{long_ma[-1]:.2f}")
    elif short_ma[-1] < long_ma[-1] and current_position > 0:
        # 死叉卖出
        order_target_shares(context.symbol, 0)
        log.info(f"卖出信号: 短期均线{short_ma[-1]:.2f} < 长期均线{long_ma[-1]:.2f}")
    
    # 记录数据
    record(short_ma=short_ma[-1], long_ma=long_ma[-1])`
    },
    
    rsi_strategy: {
      label: 'RSI策略',
      description: '基于RSI指标的超买超卖策略',
      code: `def initialize(context):
    """RSI策略初始化"""
    context.symbol = '000001.SZ'
    context.rsi_window = 14
    context.rsi_upper = 70
    context.rsi_lower = 30

def handle_data(context, data):
    """RSI策略主逻辑"""
    # 获取历史价格数据
    prices = get_history([context.symbol], 'close', context.rsi_window + 1)
    
    # 计算RSI指标
    rsi_value = rsi(prices[context.symbol], context.rsi_window)
    current_rsi = rsi_value[-1]
    
    # 获取当前持仓
    current_position = get_portfolio().positions[context.symbol].amount
    
    # 交易逻辑
    if current_rsi < context.rsi_lower and current_position == 0:
        # RSI超卖，买入
        order_value(context.symbol, 10000)
        log.info(f"RSI超卖买入信号: RSI={current_rsi:.2f}")
    elif current_rsi > context.rsi_upper and current_position > 0:
        # RSI超买，卖出
        order_target_shares(context.symbol, 0)
        log.info(f"RSI超买卖出信号: RSI={current_rsi:.2f}")
    
    # 记录数据
    record(rsi=current_rsi)`
    }
  };
};
