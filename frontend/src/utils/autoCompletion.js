/**
 * Monaco Editor自动补全配置
 * 
 * 为Python量化交易策略提供智能代码补全功能
 * 包括关键字、函数、变量、技术指标等的自动补全
 */

/**
 * 量化交易API函数补全建议
 */
export const QUANT_API_COMPLETIONS = [
  // 策略生命周期函数
  {
    label: 'initialize',
    kind: 'Function',
    insertText: 'def initialize(context):\n    """\n    策略初始化函数\n    在策略开始运行前调用一次\n    """\n    ${1:pass}',
    insertTextRules: 'InsertAsSnippet',
    documentation: {
      value: '**策略初始化函数**\n\n在策略开始运行前调用一次，用于设置策略参数、交易标的等。\n\n**参数:**\n- context: 策略上下文对象'
    },
    detail: '策略初始化函数'
  },
  {
    label: 'handle_data',
    kind: 'Function',
    insertText: 'def handle_data(context, data):\n    """\n    策略主逻辑函数\n    每个交易周期调用一次\n    """\n    ${1:pass}',
    insertTextRules: 'InsertAsSnippet',
    documentation: {
      value: '**策略主逻辑函数**\n\n每个交易周期调用一次，包含策略的核心交易逻辑。\n\n**参数:**\n- context: 策略上下文对象\n- data: 当前数据对象'
    },
    detail: '策略主逻辑函数'
  },
  {
    label: 'before_trading_start',
    kind: 'Function',
    insertText: 'def before_trading_start(context, data):\n    """\n    开盘前调用函数\n    在每个交易日开盘前调用\n    """\n    ${1:pass}',
    insertTextRules: 'InsertAsSnippet',
    documentation: {
      value: '**开盘前调用函数**\n\n在每个交易日开盘前调用，用于执行开盘前的准备工作。\n\n**参数:**\n- context: 策略上下文对象\n- data: 当前数据对象'
    },
    detail: '开盘前调用函数'
  },
  
  // 交易函数
  {
    label: 'order_shares',
    kind: 'Function',
    insertText: 'order_shares(${1:symbol}, ${2:shares})',
    insertTextRules: 'InsertAsSnippet',
    documentation: {
      value: '**按股数下单**\n\n根据指定的股数进行买卖操作。\n\n**参数:**\n- symbol: 股票代码\n- shares: 股数（正数买入，负数卖出）\n\n**返回:**\n- Order对象'
    },
    detail: '按股数下单'
  },
  {
    label: 'order_value',
    kind: 'Function',
    insertText: 'order_value(${1:symbol}, ${2:value})',
    insertTextRules: 'InsertAsSnippet',
    documentation: {
      value: '**按金额下单**\n\n根据指定的金额进行买卖操作。\n\n**参数:**\n- symbol: 股票代码\n- value: 金额（正数买入，负数卖出）\n\n**返回:**\n- Order对象'
    },
    detail: '按金额下单'
  },
  {
    label: 'order_target_shares',
    kind: 'Function',
    insertText: 'order_target_shares(${1:symbol}, ${2:target_shares})',
    insertTextRules: 'InsertAsSnippet',
    documentation: {
      value: '**调整持仓到目标股数**\n\n将指定股票的持仓调整到目标股数。\n\n**参数:**\n- symbol: 股票代码\n- target_shares: 目标股数\n\n**返回:**\n- Order对象'
    },
    detail: '调整持仓到目标股数'
  },
  {
    label: 'order_target_value',
    kind: 'Function',
    insertText: 'order_target_value(${1:symbol}, ${2:target_value})',
    insertTextRules: 'InsertAsSnippet',
    documentation: {
      value: '**调整持仓到目标金额**\n\n将指定股票的持仓调整到目标金额。\n\n**参数:**\n- symbol: 股票代码\n- target_value: 目标金额\n\n**返回:**\n- Order对象'
    },
    detail: '调整持仓到目标金额'
  },
  {
    label: 'order_target_percent',
    kind: 'Function',
    insertText: 'order_target_percent(${1:symbol}, ${2:target_percent})',
    insertTextRules: 'InsertAsSnippet',
    documentation: {
      value: '**调整持仓到目标比例**\n\n将指定股票的持仓调整到总资产的目标比例。\n\n**参数:**\n- symbol: 股票代码\n- target_percent: 目标比例（0-1之间）\n\n**返回:**\n- Order对象'
    },
    detail: '调整持仓到目标比例'
  },
  
  // 数据获取函数
  {
    label: 'get_current_price',
    kind: 'Function',
    insertText: 'get_current_price(${1:symbol})',
    insertTextRules: 'InsertAsSnippet',
    documentation: {
      value: '**获取当前价格**\n\n获取指定股票的当前价格。\n\n**参数:**\n- symbol: 股票代码\n\n**返回:**\n- float: 当前价格'
    },
    detail: '获取当前价格'
  },
  {
    label: 'get_history',
    kind: 'Function',
    insertText: 'get_history(${1:symbols}, ${2:fields}, ${3:bar_count})',
    insertTextRules: 'InsertAsSnippet',
    documentation: {
      value: '**获取历史数据**\n\n获取指定股票的历史数据。\n\n**参数:**\n- symbols: 股票代码列表\n- fields: 字段名（如"close", "open", "high", "low", "volume"）\n- bar_count: 数据条数\n\n**返回:**\n- DataFrame: 历史数据'
    },
    detail: '获取历史数据'
  },
  {
    label: 'get_portfolio',
    kind: 'Function',
    insertText: 'get_portfolio()',
    insertTextRules: 'InsertAsSnippet',
    documentation: {
      value: '**获取投资组合信息**\n\n获取当前的投资组合信息，包括持仓、现金等。\n\n**返回:**\n- Portfolio对象'
    },
    detail: '获取投资组合信息'
  },
  {
    label: 'get_open_orders',
    kind: 'Function',
    insertText: 'get_open_orders(${1:symbol})',
    insertTextRules: 'InsertAsSnippet',
    documentation: {
      value: '**获取未成交订单**\n\n获取指定股票的未成交订单。\n\n**参数:**\n- symbol: 股票代码（可选）\n\n**返回:**\n- 订单列表'
    },
    detail: '获取未成交订单'
  },
  
  // 技术指标函数
  {
    label: 'sma',
    kind: 'Function',
    insertText: 'sma(${1:data}, ${2:window})',
    insertTextRules: 'InsertAsSnippet',
    documentation: {
      value: '**简单移动平均线**\n\n计算简单移动平均线。\n\n**参数:**\n- data: 价格数据\n- window: 窗口期\n\n**返回:**\n- Series: SMA值'
    },
    detail: '简单移动平均线'
  },
  {
    label: 'ema',
    kind: 'Function',
    insertText: 'ema(${1:data}, ${2:window})',
    insertTextRules: 'InsertAsSnippet',
    documentation: {
      value: '**指数移动平均线**\n\n计算指数移动平均线。\n\n**参数:**\n- data: 价格数据\n- window: 窗口期\n\n**返回:**\n- Series: EMA值'
    },
    detail: '指数移动平均线'
  },
  {
    label: 'rsi',
    kind: 'Function',
    insertText: 'rsi(${1:data}, ${2:window})',
    insertTextRules: 'InsertAsSnippet',
    documentation: {
      value: '**相对强弱指数**\n\n计算RSI指标。\n\n**参数:**\n- data: 价格数据\n- window: 窗口期（通常为14）\n\n**返回:**\n- Series: RSI值（0-100）'
    },
    detail: '相对强弱指数'
  },
  {
    label: 'macd',
    kind: 'Function',
    insertText: 'macd(${1:data}, ${2:fast_period}, ${3:slow_period}, ${4:signal_period})',
    insertTextRules: 'InsertAsSnippet',
    documentation: {
      value: '**MACD指标**\n\n计算MACD指标。\n\n**参数:**\n- data: 价格数据\n- fast_period: 快线周期（通常为12）\n- slow_period: 慢线周期（通常为26）\n- signal_period: 信号线周期（通常为9）\n\n**返回:**\n- tuple: (MACD线, 信号线, 柱状图)'
    },
    detail: 'MACD指标'
  },
  {
    label: 'bollinger_bands',
    kind: 'Function',
    insertText: 'bollinger_bands(${1:data}, ${2:window}, ${3:std_dev})',
    insertTextRules: 'InsertAsSnippet',
    documentation: {
      value: '**布林带**\n\n计算布林带指标。\n\n**参数:**\n- data: 价格数据\n- window: 窗口期（通常为20）\n- std_dev: 标准差倍数（通常为2）\n\n**返回:**\n- tuple: (上轨, 中轨, 下轨)'
    },
    detail: '布林带'
  },
  
  // 工具函数
  {
    label: 'record',
    kind: 'Function',
    insertText: 'record(${1:key}=${2:value})',
    insertTextRules: 'InsertAsSnippet',
    documentation: {
      value: '**记录数据**\n\n记录自定义数据，用于后续分析和图表显示。\n\n**参数:**\n- key=value: 键值对形式的数据\n\n**示例:**\n```python\nrecord(price=current_price, ma=ma_value)\n```'
    },
    detail: '记录数据'
  },
  {
    label: 'log.info',
    kind: 'Function',
    insertText: 'log.info(${1:message})',
    insertTextRules: 'InsertAsSnippet',
    documentation: {
      value: '**记录信息日志**\n\n记录信息级别的日志。\n\n**参数:**\n- message: 日志消息'
    },
    detail: '记录信息日志'
  },
  {
    label: 'log.warning',
    kind: 'Function',
    insertText: 'log.warning(${1:message})',
    insertTextRules: 'InsertAsSnippet',
    documentation: {
      value: '**记录警告日志**\n\n记录警告级别的日志。\n\n**参数:**\n- message: 日志消息'
    },
    detail: '记录警告日志'
  },
  {
    label: 'log.error',
    kind: 'Function',
    insertText: 'log.error(${1:message})',
    insertTextRules: 'InsertAsSnippet',
    documentation: {
      value: '**记录错误日志**\n\n记录错误级别的日志。\n\n**参数:**\n- message: 日志消息'
    },
    detail: '记录错误日志'
  }
];

/**
 * Python内置函数和关键字补全
 */
export const PYTHON_BUILTIN_COMPLETIONS = [
  {
    label: 'len',
    kind: 'Function',
    insertText: 'len(${1:obj})',
    insertTextRules: 'InsertAsSnippet',
    documentation: '返回对象的长度',
    detail: '内置函数'
  },
  {
    label: 'range',
    kind: 'Function',
    insertText: 'range(${1:start}, ${2:stop}, ${3:step})',
    insertTextRules: 'InsertAsSnippet',
    documentation: '生成数字序列',
    detail: '内置函数'
  },
  {
    label: 'enumerate',
    kind: 'Function',
    insertText: 'enumerate(${1:iterable})',
    insertTextRules: 'InsertAsSnippet',
    documentation: '返回枚举对象',
    detail: '内置函数'
  },
  {
    label: 'zip',
    kind: 'Function',
    insertText: 'zip(${1:iterable1}, ${2:iterable2})',
    insertTextRules: 'InsertAsSnippet',
    documentation: '将多个可迭代对象打包',
    detail: '内置函数'
  },
  {
    label: 'sum',
    kind: 'Function',
    insertText: 'sum(${1:iterable})',
    insertTextRules: 'InsertAsSnippet',
    documentation: '计算可迭代对象的总和',
    detail: '内置函数'
  },
  {
    label: 'max',
    kind: 'Function',
    insertText: 'max(${1:iterable})',
    insertTextRules: 'InsertAsSnippet',
    documentation: '返回最大值',
    detail: '内置函数'
  },
  {
    label: 'min',
    kind: 'Function',
    insertText: 'min(${1:iterable})',
    insertTextRules: 'InsertAsSnippet',
    documentation: '返回最小值',
    detail: '内置函数'
  }
];

/**
 * 常用代码片段补全
 */
export const CODE_SNIPPETS = [
  {
    label: 'if __name__ == "__main__"',
    kind: 'Snippet',
    insertText: 'if __name__ == "__main__":\n    ${1:pass}',
    insertTextRules: 'InsertAsSnippet',
    documentation: 'Python主程序入口',
    detail: '代码片段'
  },
  {
    label: 'try-except',
    kind: 'Snippet',
    insertText: 'try:\n    ${1:pass}\nexcept ${2:Exception} as e:\n    ${3:pass}',
    insertTextRules: 'InsertAsSnippet',
    documentation: '异常处理',
    detail: '代码片段'
  },
  {
    label: 'for loop',
    kind: 'Snippet',
    insertText: 'for ${1:item} in ${2:iterable}:\n    ${3:pass}',
    insertTextRules: 'InsertAsSnippet',
    documentation: 'for循环',
    detail: '代码片段'
  },
  {
    label: 'while loop',
    kind: 'Snippet',
    insertText: 'while ${1:condition}:\n    ${2:pass}',
    insertTextRules: 'InsertAsSnippet',
    documentation: 'while循环',
    detail: '代码片段'
  },
  {
    label: 'class definition',
    kind: 'Snippet',
    insertText: 'class ${1:ClassName}:\n    def __init__(self${2:, args}):\n        ${3:pass}',
    insertTextRules: 'InsertAsSnippet',
    documentation: '类定义',
    detail: '代码片段'
  }
];

/**
 * 配置Monaco Editor的自动补全功能
 * @param {Object} monaco - Monaco Editor实例
 */
export const configureAutoCompletion = (monaco) => {
  // 注册自动补全提供者
  monaco.languages.registerCompletionItemProvider('python', {
    provideCompletionItems: (model, position) => {
      const word = model.getWordUntilPosition(position);
      const range = {
        startLineNumber: position.lineNumber,
        endLineNumber: position.lineNumber,
        startColumn: word.startColumn,
        endColumn: word.endColumn
      };

      // 获取当前行的文本
      const lineText = model.getLineContent(position.lineNumber);
      const textBeforeCursor = lineText.substring(0, position.column - 1);
      
      // 根据上下文提供不同的补全建议
      let suggestions = [];
      
      // 量化交易API补全
      const quantSuggestions = QUANT_API_COMPLETIONS.map(item => ({
        ...item,
        kind: monaco.languages.CompletionItemKind[item.kind],
        insertTextRules: monaco.languages.CompletionItemInsertTextRule[item.insertTextRules],
        range: range
      }));
      
      // Python内置函数补全
      const builtinSuggestions = PYTHON_BUILTIN_COMPLETIONS.map(item => ({
        ...item,
        kind: monaco.languages.CompletionItemKind[item.kind],
        insertTextRules: monaco.languages.CompletionItemInsertTextRule[item.insertTextRules],
        range: range
      }));
      
      // 代码片段补全
      const snippetSuggestions = CODE_SNIPPETS.map(item => ({
        ...item,
        kind: monaco.languages.CompletionItemKind[item.kind],
        insertTextRules: monaco.languages.CompletionItemInsertTextRule[item.insertTextRules],
        range: range
      }));
      
      // 合并所有建议
      suggestions = [...quantSuggestions, ...builtinSuggestions, ...snippetSuggestions];
      
      // 根据输入内容过滤建议
      const inputText = word.word.toLowerCase();
      if (inputText) {
        suggestions = suggestions.filter(suggestion => 
          suggestion.label.toLowerCase().includes(inputText)
        );
      }
      
      return { suggestions: suggestions };
    },
    
    // 触发字符
    triggerCharacters: ['.', '(', '[', '"', "'"]
  });
  
  // 注册悬停提示提供者
  monaco.languages.registerHoverProvider('python', {
    provideHover: (model, position) => {
      const word = model.getWordAtPosition(position);
      if (!word) return null;
      
      // 查找对应的函数文档
      const allCompletions = [...QUANT_API_COMPLETIONS, ...PYTHON_BUILTIN_COMPLETIONS];
      const completion = allCompletions.find(item => item.label === word.word);
      
      if (completion && completion.documentation) {
        return {
          range: new monaco.Range(
            position.lineNumber,
            word.startColumn,
            position.lineNumber,
            word.endColumn
          ),
          contents: [
            { value: `**${completion.label}**` },
            { value: completion.documentation.value || completion.documentation }
          ]
        };
      }
      
      return null;
    }
  });
  
  // 注册签名帮助提供者
  monaco.languages.registerSignatureHelpProvider('python', {
    signatureHelpTriggerCharacters: ['(', ','],
    provideSignatureHelp: (model, position) => {
      // 这里可以实现函数签名提示
      // 简化实现，实际项目中可以更详细
      return {
        dispose: () => {},
        value: {
          signatures: [],
          activeSignature: 0,
          activeParameter: 0
        }
      };
    }
  });
  
  console.log('自动补全配置完成');
};
