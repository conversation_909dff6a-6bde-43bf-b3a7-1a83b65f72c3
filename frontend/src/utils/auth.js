/**
 * 认证相关工具函数
 * 
 * 提供用户认证状态管理、令牌验证、自动登录等功能
 */

/**
 * 检查用户是否已登录
 * @returns {boolean} - 是否已登录
 */
export const isAuthenticated = () => {
  const token = localStorage.getItem('token');
  const user = localStorage.getItem('user');
  
  if (!token || !user) {
    return false;
  }
  
  // 检查令牌是否过期
  const tokenExpiresIn = localStorage.getItem('token_expires_in');
  const rememberMe = localStorage.getItem('remember_me') === 'true';
  const longTermExpiry = localStorage.getItem('long_term_expiry');
  
  if (rememberMe && longTermExpiry) {
    // 检查长期登录是否过期
    const expiryDate = new Date(longTermExpiry);
    if (new Date() > expiryDate) {
      clearAuthData();
      return false;
    }
    return true;
  }
  
  if (tokenExpiresIn) {
    // 简单的过期检查（实际应用中应该检查具体的过期时间戳）
    const expiresIn = parseInt(tokenExpiresIn);
    if (expiresIn <= 0) {
      clearAuthData();
      return false;
    }
  }
  
  return true;
};

/**
 * 获取当前用户信息
 * @returns {Object|null} - 用户信息对象或null
 */
export const getCurrentUser = () => {
  try {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  } catch (error) {
    console.error('解析用户信息失败:', error);
    return null;
  }
};

/**
 * 获取访问令牌
 * @returns {string|null} - 访问令牌或null
 */
export const getAccessToken = () => {
  return localStorage.getItem('token');
};

/**
 * 获取刷新令牌
 * @returns {string|null} - 刷新令牌或null
 */
export const getRefreshToken = () => {
  return localStorage.getItem('refresh_token');
};

/**
 * 检查是否启用了记住登录状态
 * @returns {boolean} - 是否记住登录状态
 */
export const isRememberMeEnabled = () => {
  return localStorage.getItem('remember_me') === 'true';
};

/**
 * 清除所有认证相关数据
 */
export const clearAuthData = () => {
  localStorage.removeItem('token');
  localStorage.removeItem('refresh_token');
  localStorage.removeItem('token_type');
  localStorage.removeItem('token_expires_in');
  localStorage.removeItem('user');
  localStorage.removeItem('saved_username');
  localStorage.removeItem('remember_me');
  localStorage.removeItem('long_term_expiry');
};

/**
 * 设置用户认证信息
 * @param {Object} authData - 认证数据
 * @param {string} authData.access_token - 访问令牌
 * @param {string} authData.token_type - 令牌类型
 * @param {number} authData.expires_in - 过期时间（秒）
 * @param {string} [authData.refresh_token] - 刷新令牌
 * @param {Object} userData - 用户数据
 * @param {boolean} rememberMe - 是否记住登录状态
 */
export const setAuthData = (authData, userData, rememberMe = false) => {
  // 保存令牌信息
  localStorage.setItem('token', authData.access_token);
  localStorage.setItem('token_type', authData.token_type);
  localStorage.setItem('token_expires_in', authData.expires_in.toString());
  
  if (authData.refresh_token) {
    localStorage.setItem('refresh_token', authData.refresh_token);
  }
  
  // 保存用户信息
  if (userData) {
    localStorage.setItem('user', JSON.stringify(userData));
  }
  
  // 处理记住登录状态
  if (rememberMe) {
    localStorage.setItem('remember_me', 'true');
    
    // 设置长期过期时间（30天）
    const longTermExpiry = new Date();
    longTermExpiry.setDate(longTermExpiry.getDate() + 30);
    localStorage.setItem('long_term_expiry', longTermExpiry.toISOString());
    
    // 保存用户名以便下次自动填充
    if (userData?.username) {
      localStorage.setItem('saved_username', userData.username);
    }
  } else {
    localStorage.removeItem('remember_me');
    localStorage.removeItem('long_term_expiry');
    localStorage.removeItem('saved_username');
  }
};

/**
 * 检查令牌是否即将过期（在过期前5分钟）
 * @returns {boolean} - 是否即将过期
 */
export const isTokenExpiringSoon = () => {
  const tokenExpiresIn = localStorage.getItem('token_expires_in');
  
  if (!tokenExpiresIn) {
    return false;
  }
  
  const expiresIn = parseInt(tokenExpiresIn);
  const fiveMinutes = 5 * 60; // 5分钟
  
  return expiresIn <= fiveMinutes;
};

/**
 * 自动检查并刷新令牌
 * @returns {Promise<boolean>} - 是否成功刷新
 */
export const autoRefreshToken = async () => {
  const refreshToken = getRefreshToken();
  const rememberMe = isRememberMeEnabled();
  
  if (!refreshToken || !rememberMe) {
    return false;
  }
  
  try {
    const { apiService } = await import('../services/api');
    const response = await apiService.refreshToken(refreshToken);
    
    if (response.data?.access_token) {
      // 更新令牌信息
      localStorage.setItem('token', response.data.access_token);
      localStorage.setItem('token_expires_in', response.data.expires_in.toString());
      
      return true;
    }
  } catch (error) {
    console.error('自动刷新令牌失败:', error);
    clearAuthData();
  }
  
  return false;
};

/**
 * 获取保存的用户名（用于自动填充）
 * @returns {string|null} - 保存的用户名或null
 */
export const getSavedUsername = () => {
  const rememberMe = isRememberMeEnabled();
  return rememberMe ? localStorage.getItem('saved_username') : null;
};

/**
 * 检查用户邮箱是否已验证
 * @returns {boolean} - 邮箱是否已验证
 */
export const isEmailVerified = () => {
  const user = getCurrentUser();
  return user?.is_verified === true;
};

/**
 * 检查用户是否有特定权限
 * @param {string} permission - 权限名称
 * @returns {boolean} - 是否有权限
 */
export const hasPermission = (permission) => {
  const user = getCurrentUser();
  
  if (!user) {
    return false;
  }
  
  // 管理员拥有所有权限
  if (user.role === 'admin') {
    return true;
  }
  
  // 检查用户权限列表
  const permissions = user.permissions || [];
  return permissions.includes(permission);
};

/**
 * 检查用户是否有特定角色
 * @param {string} role - 角色名称
 * @returns {boolean} - 是否有该角色
 */
export const hasRole = (role) => {
  const user = getCurrentUser();
  return user?.role === role;
};

/**
 * 登出用户
 * @param {boolean} [redirectToLogin=true] - 是否重定向到登录页
 */
export const logout = async (redirectToLogin = true) => {
  try {
    // 调用登出API
    const { apiService } = await import('../services/api');
    await apiService.logout();
  } catch (error) {
    console.error('登出API调用失败:', error);
  } finally {
    // 清除本地认证数据
    clearAuthData();
    
    // 重定向到登录页
    if (redirectToLogin && window.location.pathname !== '/login') {
      window.location.href = '/login';
    }
  }
};

/**
 * 初始化认证状态检查
 * 在应用启动时调用，检查是否有有效的登录状态
 */
export const initializeAuth = () => {
  // 检查是否有有效的认证状态
  if (!isAuthenticated()) {
    clearAuthData();
    return false;
  }
  
  // 如果令牌即将过期且启用了记住登录，尝试自动刷新
  if (isTokenExpiringSoon() && isRememberMeEnabled()) {
    autoRefreshToken().catch(error => {
      console.error('初始化时自动刷新令牌失败:', error);
    });
  }
  
  return true;
};
