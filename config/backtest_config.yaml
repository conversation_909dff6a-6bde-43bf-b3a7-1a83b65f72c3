# 事件驱动回测系统配置文件

# 回测基本配置
backtest:
  # 回测时间范围
  start_date: "2023-01-01"
  end_date: "2023-12-31"
  
  # 初始资金（元）
  initial_capital: 1000000
  
  # 交易品种列表
  symbols:
    - "000001.SZ"  # 平安银行
    - "000002.SZ"  # 万科A
    - "600000.SH"  # 浦发银行
    - "600036.SH"  # 招商银行
  
  # 数据频率
  timeframe: "1D"  # 1D, 1H, 30m, 15m, 5m, 1m
  
  # 基准指数
  benchmark: "000300.SH"  # 沪深300

# 交易成本配置
trading_costs:
  # 手续费率（双边）
  commission_rate: 0.0003  # 万分之三
  
  # 滑点率
  slippage_rate: 0.001  # 千分之一
  
  # 印花税率（仅卖出）
  stamp_duty_rate: 0.001  # 千分之一
  
  # 最小手续费（元）
  min_commission: 5.0

# 数据源配置
data_source:
  # 数据源类型: csv, database, api
  type: "csv"
  
  # 数据路径
  path: "data/"
  
  # 数据验证
  validate_data: true
  
  # 填充缺失数据
  fill_missing: true
  
  # 数据缓存
  enable_cache: true
  cache_size: 1000

# 策略配置
strategies:
  # 移动平均线交叉策略
  - strategy_id: "ma_cross_001"
    strategy_name: "移动平均线交叉策略"
    strategy_class: "MovingAverageCrossoverStrategy"
    enabled: true
    
    # 策略参数
    parameters:
      short_window: 10    # 短期移动平均线周期
      long_window: 30     # 长期移动平均线周期
      
    # 交易品种
    symbols:
      - "000001.SZ"
      - "000002.SZ"
    
    # 仓位限制
    max_position_size: 1000
    
    # 风险限制
    risk_limit: 0.02  # 单笔交易最大风险2%
  
  # RSI策略（示例）
  - strategy_id: "rsi_001"
    strategy_name: "RSI超买超卖策略"
    strategy_class: "RSIStrategy"
    enabled: false
    
    parameters:
      rsi_period: 14
      oversold_threshold: 30
      overbought_threshold: 70
    
    symbols:
      - "600000.SH"
      - "600036.SH"
    
    max_position_size: 800
    risk_limit: 0.015

# 风险管理配置
risk_management:
  # 仓位限制
  max_position_size: 2000        # 最大单个仓位
  max_portfolio_value: 10000000  # 最大组合价值（元）
  
  # 损失限制
  max_daily_loss: 50000          # 最大日损失（元）
  max_total_loss: 200000         # 最大总损失（元）
  
  # 回撤限制
  max_drawdown: 0.15             # 最大回撤比例 15%
  
  # 杠杆限制
  max_leverage: 1.0              # 最大杠杆倍数
  
  # 集中度限制
  max_concentration: 0.3         # 最大单品种集中度 30%
  
  # 止损止盈
  stop_loss_pct: 0.05           # 止损百分比 5%
  take_profit_pct: 0.10         # 止盈百分比 10%
  
  # 风险检查频率
  check_frequency: "real_time"   # real_time, daily, hourly

# 执行引擎配置
execution:
  # 订单类型偏好
  default_order_type: "LIMIT"    # MARKET, LIMIT
  
  # 订单有效期
  default_time_in_force: "DAY"   # DAY, GTC, IOC, FOK
  
  # 订单大小限制
  min_order_size: 100            # 最小订单数量
  max_order_size: 10000          # 最大订单数量
  
  # 价格精度
  price_precision: 2             # 价格小数位数
  
  # 订单超时时间（秒）
  order_timeout: 300

# 事件系统配置
event_system:
  # 消息总线配置
  message_bus:
    max_workers: 4               # 最大工作线程数
    enable_async: true           # 启用异步处理
    
  # 事件队列配置
  event_queue:
    max_size: 10000             # 队列最大容量
    enable_priority: true        # 启用优先级排序
    batch_size: 10              # 批处理大小
    
  # 事件循环配置
  event_loop:
    process_interval: 0.001      # 处理间隔（秒）
    max_retries: 3              # 最大重试次数

# 性能配置
performance:
  # 数据回放速度
  replay_speed: 0               # 0表示最快速度
  
  # 内存管理
  max_memory_usage: 8           # 最大内存使用（GB）
  gc_frequency: 1000            # 垃圾回收频率
  
  # 缓存配置
  enable_data_cache: true
  cache_cleanup_interval: 3600  # 缓存清理间隔（秒）

# 日志配置
logging:
  # 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
  level: "INFO"
  
  # 日志格式
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # 日志文件
  file: "logs/backtest.log"
  
  # 日志轮转
  max_file_size: "100MB"
  backup_count: 5
  
  # 控制台输出
  console_output: true

# 输出配置
output:
  # 结果保存路径
  results_path: "results/"
  
  # 保存格式
  formats:
    - "json"
    - "csv"
    - "excel"
  
  # 图表生成
  generate_charts: true
  chart_format: "png"
  
  # 报告生成
  generate_report: true
  report_format: "html"
  
  # 详细程度
  detail_level: "full"  # basic, standard, full

# 监控配置
monitoring:
  # 性能监控
  enable_performance_monitoring: true
  
  # 内存监控
  enable_memory_monitoring: true
  
  # 事件统计
  enable_event_statistics: true
  
  # 监控报告间隔（秒）
  report_interval: 60
  
  # 监控数据保存
  save_monitoring_data: true

# 调试配置
debug:
  # 调试模式
  enabled: false
  
  # 详细日志
  verbose_logging: false
  
  # 事件跟踪
  trace_events: false
  
  # 性能分析
  enable_profiling: false
  
  # 断点调试
  enable_breakpoints: false

# 扩展配置
extensions:
  # 自定义数据源
  custom_data_providers: []
  
  # 自定义策略
  custom_strategies: []
  
  # 自定义风险规则
  custom_risk_rules: []
  
  # 插件路径
  plugin_paths:
    - "plugins/"
    - "custom_modules/"

# 环境配置
environment:
  # 运行环境: development, testing, production
  mode: "development"
  
  # 时区
  timezone: "Asia/Shanghai"
  
  # 语言
  language: "zh_CN"
  
  # 并行处理
  enable_multiprocessing: false
  max_processes: 4
