"""
用户服务

提供用户管理相关的API接口，包括：
- 用户注册和登录
- 用户信息管理
- 权限验证
- JWT令牌管理
"""

import logging
import uvicorn
from fastapi import FastAPI, HTTPException, Depends, status
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, EmailStr
from typing import Optional, Dict, Any, List
import sys
import os
import smtplib
import secrets
import hashlib
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
from datetime import datetime, timedelta
import asyncio
from concurrent.futures import ThreadPoolExecutor

# 导入RBAC相关模块
from models.rbac import Role, Permission, ResourceType, ActionType
from services.rbac_service import rbac_service
from middleware.auth_middleware import auth_middleware, permission_checker, require_admin, require_user_manage

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="用户服务",
    description="AI量化交易工具用户管理服务",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# JWT认证
security = HTTPBearer()

# 邮件配置
EMAIL_CONFIG = {
    "smtp_server": "smtp.gmail.com",  # 可配置为其他SMTP服务器
    "smtp_port": 587,
    "sender_email": "<EMAIL>",  # 配置发送邮箱
    "sender_password": "your_app_password",  # 配置邮箱密码或应用密码
    "use_tls": True
}

# 数据模型
class UserRegister(BaseModel):
    username: str
    email: EmailStr  # 使用EmailStr确保邮箱格式正确
    password: str
    full_name: Optional[str] = None

class UserLogin(BaseModel):
    username: str
    password: str
    remember_me: Optional[bool] = False  # 添加记住登录状态选项

class UserResponse(BaseModel):
    id: str
    username: str
    email: str
    full_name: Optional[str]
    is_active: bool
    is_verified: bool  # 添加邮箱验证状态
    roles: List[str] = []  # 用户角色列表
    permissions: List[str] = []  # 用户权限列表
    created_at: str

class TokenResponse(BaseModel):
    access_token: str
    token_type: str
    expires_in: int
    refresh_token: Optional[str] = None  # 添加刷新令牌支持

class EmailVerificationRequest(BaseModel):
    email: EmailStr

class EmailVerificationConfirm(BaseModel):
    token: str

class RefreshTokenRequest(BaseModel):
    refresh_token: str

class RoleAssignRequest(BaseModel):
    user_id: str
    role_id: str
    expires_at: Optional[datetime] = None

class RoleCreateRequest(BaseModel):
    id: str
    name: str
    description: str
    permissions: List[str] = []

class RoleUpdateRequest(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    permissions: Optional[List[str]] = None

class PermissionCheckRequest(BaseModel):
    resource_type: str
    action: str
    resource_id: Optional[str] = None

# 模拟数据库
users_db = {}
tokens_db = {}
verification_tokens_db = {}  # 邮箱验证令牌数据库
refresh_tokens_db = {}  # 刷新令牌数据库

# 线程池用于异步发送邮件
email_executor = ThreadPoolExecutor(max_workers=2)

# 设置认证中间件的token数据库引用
auth_middleware.set_tokens_db(tokens_db)

def generate_verification_token() -> str:
    """生成邮箱验证令牌"""
    return secrets.token_urlsafe(32)

def generate_token_hash(token: str) -> str:
    """生成令牌哈希"""
    return hashlib.sha256(token.encode()).hexdigest()

async def send_verification_email(email: str, token: str, username: str):
    """发送邮箱验证邮件"""
    def _send_email():
        try:
            # 创建邮件内容
            subject = "AI量化交易工具 - 邮箱验证"

            # HTML邮件模板
            html_body = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>邮箱验证</title>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                    .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                    .header {{ background-color: #1890ff; color: white; padding: 20px; text-align: center; }}
                    .content {{ padding: 20px; background-color: #f9f9f9; }}
                    .button {{
                        display: inline-block;
                        padding: 12px 24px;
                        background-color: #1890ff;
                        color: white;
                        text-decoration: none;
                        border-radius: 4px;
                        margin: 20px 0;
                    }}
                    .footer {{ padding: 20px; text-align: center; color: #666; font-size: 12px; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>AI量化交易工具</h1>
                        <p>邮箱验证</p>
                    </div>
                    <div class="content">
                        <h2>您好，{username}！</h2>
                        <p>感谢您注册AI量化交易工具。请点击下面的按钮验证您的邮箱地址：</p>
                        <p style="text-align: center;">
                            <a href="http://localhost:3000/verify-email?token={token}" class="button">
                                验证邮箱
                            </a>
                        </p>
                        <p>如果按钮无法点击，请复制以下链接到浏览器地址栏：</p>
                        <p style="word-break: break-all; background-color: #f0f0f0; padding: 10px; border-radius: 4px;">
                            http://localhost:3000/verify-email?token={token}
                        </p>
                        <p><strong>注意：</strong>此验证链接将在24小时后过期。</p>
                    </div>
                    <div class="footer">
                        <p>此邮件由系统自动发送，请勿回复。</p>
                        <p>如有疑问，请联系技术支持。</p>
                    </div>
                </div>
            </body>
            </html>
            """

            # 创建邮件对象
            msg = MimeMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = EMAIL_CONFIG["sender_email"]
            msg['To'] = email

            # 添加HTML内容
            html_part = MimeText(html_body, 'html', 'utf-8')
            msg.attach(html_part)

            # 发送邮件（在实际环境中需要配置真实的SMTP服务器）
            # 这里只是模拟发送过程
            logger.info(f"模拟发送验证邮件到: {email}")
            logger.info(f"验证链接: http://localhost:3000/verify-email?token={token}")

            # 在实际环境中的SMTP发送代码：
            # with smtplib.SMTP(EMAIL_CONFIG["smtp_server"], EMAIL_CONFIG["smtp_port"]) as server:
            #     if EMAIL_CONFIG["use_tls"]:
            #         server.starttls()
            #     server.login(EMAIL_CONFIG["sender_email"], EMAIL_CONFIG["sender_password"])
            #     server.send_message(msg)

            return True

        except Exception as e:
            logger.error(f"发送验证邮件失败: {e}")
            return False

    # 在线程池中异步执行邮件发送
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(email_executor, _send_email)

@app.on_event("startup")
async def startup_event():
    """服务启动事件"""
    try:
        logger.info("用户服务启动成功")
    except Exception as e:
        logger.error(f"用户服务启动失败: {e}")
        raise

@app.on_event("shutdown")
async def shutdown_event():
    """服务关闭事件"""
    try:
        logger.info("用户服务关闭")
    except Exception as e:
        logger.error(f"用户服务关闭异常: {e}")

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy", "service": "user-service"}

@app.post("/api/v1/users/register", response_model=UserResponse)
async def register_user(user_data: UserRegister):
    """用户注册"""
    try:
        # 检查用户名是否已存在
        if user_data.username in users_db:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已存在"
            )

        # 检查邮箱是否已被使用
        for user in users_db.values():
            if user["email"] == user_data.email:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="邮箱已被使用"
                )

        # 创建用户（初始状态为未验证）
        user_id = f"user_{len(users_db) + 1}"
        user = {
            "id": user_id,
            "username": user_data.username,
            "email": user_data.email,
            "password_hash": f"hashed_{user_data.password}",  # 实际应用中需要真正的哈希
            "full_name": user_data.full_name,
            "is_active": True,
            "is_verified": False,  # 初始状态为未验证
            "created_at": datetime.now().isoformat()
        }

        users_db[user_data.username] = user

        # 为新用户分配默认角色（观察者角色）
        rbac_service.assign_role_to_user(
            user_id=user_id,
            role_id="viewer",  # 默认分配观察者角色
            assigned_by="system"
        )

        # 生成邮箱验证令牌
        verification_token = generate_verification_token()
        verification_tokens_db[verification_token] = {
            "user_id": user_id,
            "email": user_data.email,
            "expires_at": (datetime.now() + timedelta(hours=24)).isoformat(),
            "used": False
        }

        # 发送验证邮件
        email_sent = await send_verification_email(
            user_data.email,
            verification_token,
            user_data.username
        )

        if not email_sent:
            logger.warning(f"验证邮件发送失败，但用户已创建: {user_data.username}")

        logger.info(f"用户注册成功: {user_data.username}")

        # 获取用户角色和权限信息
        user_roles = rbac_service.get_user_roles(user_id)
        user_permissions = rbac_service.get_user_permissions(user_id)

        user["roles"] = [role.id for role in user_roles]
        user["permissions"] = list(user_permissions)

        return UserResponse(**user)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"用户注册失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="用户注册失败"
        )

@app.post("/api/v1/users/login", response_model=TokenResponse)
async def login_user(login_data: UserLogin):
    """用户登录"""
    try:
        # 验证用户凭据
        user = users_db.get(login_data.username)
        if not user or user["password_hash"] != f"hashed_{login_data.password}":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )

        # 检查用户是否被禁用
        if not user.get("is_active", True):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="账号已被禁用，请联系管理员"
            )

        # 生成访问令牌
        access_token = f"token_{user['id']}_{len(tokens_db)}"

        # 根据是否记住登录状态设置不同的过期时间
        if login_data.remember_me:
            # 记住登录状态：30天
            expires_in = 30 * 24 * 3600  # 30天
            expires_at = (datetime.now() + timedelta(days=30)).isoformat()
        else:
            # 普通登录：1小时
            expires_in = 3600  # 1小时
            expires_at = (datetime.now() + timedelta(hours=1)).isoformat()

        tokens_db[access_token] = {
            "user_id": user["id"],
            "username": user["username"],
            "expires_at": expires_at,
            "remember_me": login_data.remember_me
        }

        # 生成刷新令牌（如果记住登录状态）
        refresh_token = None
        if login_data.remember_me:
            refresh_token = f"refresh_{user['id']}_{len(refresh_tokens_db)}"
            refresh_tokens_db[refresh_token] = {
                "user_id": user["id"],
                "username": user["username"],
                "expires_at": (datetime.now() + timedelta(days=60)).isoformat(),  # 刷新令牌60天有效
                "access_token": access_token
            }

        logger.info(f"用户登录成功: {login_data.username}, 记住登录: {login_data.remember_me}")

        return TokenResponse(
            access_token=access_token,
            token_type="bearer",
            expires_in=expires_in,
            refresh_token=refresh_token
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"用户登录失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="用户登录失败"
        )

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """获取当前用户"""
    token = credentials.credentials
    token_data = tokens_db.get(token)

    if not token_data:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的访问令牌"
        )

    # 检查令牌是否过期
    expires_at = datetime.fromisoformat(token_data["expires_at"])
    if datetime.now() > expires_at:
        del tokens_db[token]
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="访问令牌已过期"
        )

    user = users_db.get(token_data["username"])
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户不存在"
        )

    # 获取用户角色和权限信息
    user_id = user["id"]
    user_roles = rbac_service.get_user_roles(user_id)
    user_permissions = rbac_service.get_user_permissions(user_id)

    # 更新用户信息
    user_data = user.copy()
    user_data["roles"] = [role.id for role in user_roles]
    user_data["permissions"] = list(user_permissions)

    return UserResponse(**user_data)

@app.get("/api/v1/users/me", response_model=UserResponse)
async def get_user_profile(current_user: UserResponse = Depends(get_current_user)):
    """获取用户信息"""
    return current_user

@app.post("/api/v1/users/verify-email")
async def verify_email(verification_data: EmailVerificationConfirm):
    """验证邮箱"""
    try:
        token = verification_data.token

        # 检查验证令牌是否存在
        token_info = verification_tokens_db.get(token)
        if not token_info:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的验证令牌"
            )

        # 检查令牌是否已使用
        if token_info["used"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="验证令牌已使用"
            )

        # 检查令牌是否过期
        expires_at = datetime.fromisoformat(token_info["expires_at"])
        if datetime.now() > expires_at:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="验证令牌已过期"
            )

        # 查找用户并更新验证状态
        user_id = token_info["user_id"]
        user_found = False

        for username, user in users_db.items():
            if user["id"] == user_id:
                user["is_verified"] = True
                user_found = True
                logger.info(f"用户邮箱验证成功: {username}")
                break

        if not user_found:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )

        # 标记令牌为已使用
        verification_tokens_db[token]["used"] = True

        return {"message": "邮箱验证成功", "verified": True}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"邮箱验证失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="邮箱验证失败"
        )

@app.post("/api/v1/users/resend-verification")
async def resend_verification_email(email_data: EmailVerificationRequest):
    """重新发送验证邮件"""
    try:
        email = email_data.email

        # 查找用户
        user_found = None
        for username, user in users_db.items():
            if user["email"] == email:
                user_found = user
                break

        if not user_found:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="邮箱地址未注册"
            )

        # 检查用户是否已验证
        if user_found["is_verified"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已验证，无需重复验证"
            )

        # 生成新的验证令牌
        verification_token = generate_verification_token()
        verification_tokens_db[verification_token] = {
            "user_id": user_found["id"],
            "email": email,
            "expires_at": (datetime.now() + timedelta(hours=24)).isoformat(),
            "used": False
        }

        # 发送验证邮件
        email_sent = await send_verification_email(
            email,
            verification_token,
            user_found["username"]
        )

        if not email_sent:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="验证邮件发送失败"
            )

        logger.info(f"重新发送验证邮件: {email}")

        return {"message": "验证邮件已重新发送", "email": email}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重新发送验证邮件失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="重新发送验证邮件失败"
        )

@app.post("/api/v1/users/refresh-token", response_model=TokenResponse)
async def refresh_access_token(refresh_data: RefreshTokenRequest):
    """刷新访问令牌"""
    try:
        refresh_token = refresh_data.refresh_token

        # 检查刷新令牌是否存在
        token_info = refresh_tokens_db.get(refresh_token)
        if not token_info:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的刷新令牌"
            )

        # 检查刷新令牌是否过期
        expires_at = datetime.fromisoformat(token_info["expires_at"])
        if datetime.now() > expires_at:
            # 清除过期的刷新令牌
            del refresh_tokens_db[refresh_token]
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="刷新令牌已过期，请重新登录"
            )

        # 查找用户
        user_id = token_info["user_id"]
        username = token_info["username"]
        user = users_db.get(username)

        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )

        # 检查用户是否被禁用
        if not user.get("is_active", True):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="账号已被禁用"
            )

        # 撤销旧的访问令牌
        old_access_token = token_info.get("access_token")
        if old_access_token and old_access_token in tokens_db:
            del tokens_db[old_access_token]

        # 生成新的访问令牌
        new_access_token = f"token_{user_id}_{len(tokens_db)}"
        expires_in = 30 * 24 * 3600  # 30天（因为是通过刷新令牌获取的）
        expires_at = (datetime.now() + timedelta(days=30)).isoformat()

        tokens_db[new_access_token] = {
            "user_id": user_id,
            "username": username,
            "expires_at": expires_at,
            "remember_me": True
        }

        # 更新刷新令牌中的访问令牌引用
        refresh_tokens_db[refresh_token]["access_token"] = new_access_token

        logger.info(f"访问令牌刷新成功: {username}")

        return TokenResponse(
            access_token=new_access_token,
            token_type="bearer",
            expires_in=expires_in,
            refresh_token=refresh_token  # 返回相同的刷新令牌
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"刷新令牌失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="刷新令牌失败"
        )

@app.post("/api/v1/users/logout")
async def logout_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """用户登出"""
    try:
        token = credentials.credentials

        # 查找并删除访问令牌
        token_data = tokens_db.get(token)
        if token_data:
            del tokens_db[token]

            # 查找并删除相关的刷新令牌
            user_id = token_data["user_id"]
            refresh_tokens_to_delete = []

            for refresh_token, refresh_data in refresh_tokens_db.items():
                if refresh_data.get("access_token") == token or refresh_data.get("user_id") == user_id:
                    refresh_tokens_to_delete.append(refresh_token)

            for refresh_token in refresh_tokens_to_delete:
                del refresh_tokens_db[refresh_token]

            logger.info(f"用户登出成功: {token_data.get('username', 'unknown')}")

        return {"message": "登出成功"}

    except Exception as e:
        logger.error(f"用户登出失败: {e}")
        # 即使登出失败也返回成功，避免客户端状态不一致
        return {"message": "登出成功"}

# ==================== 权限管理API接口 ====================

@app.post("/api/v1/users/assign-role")
@require_user_manage
async def assign_role_to_user(role_assign: RoleAssignRequest, current_user = Depends(get_current_user)):
    """为用户分配角色（需要用户管理权限）"""
    try:
        success = rbac_service.assign_role_to_user(
            user_id=role_assign.user_id,
            role_id=role_assign.role_id,
            assigned_by=current_user.id,
            expires_at=role_assign.expires_at
        )

        if success:
            logger.info(f"管理员 {current_user.username} 为用户 {role_assign.user_id} 分配角色 {role_assign.role_id}")
            return {"message": "角色分配成功", "user_id": role_assign.user_id, "role_id": role_assign.role_id}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="角色分配失败"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"分配角色失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="分配角色失败"
        )

@app.post("/api/v1/users/revoke-role")
@require_user_manage
async def revoke_role_from_user(user_id: str, role_id: str, current_user = Depends(get_current_user)):
    """撤销用户角色（需要用户管理权限）"""
    try:
        success = rbac_service.revoke_role_from_user(user_id, role_id)

        if success:
            logger.info(f"管理员 {current_user.username} 撤销用户 {user_id} 的角色 {role_id}")
            return {"message": "角色撤销成功", "user_id": user_id, "role_id": role_id}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="角色撤销失败"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"撤销角色失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="撤销角色失败"
        )

@app.get("/api/v1/users/{user_id}/roles")
@require_user_manage
async def get_user_roles_api(user_id: str, current_user = Depends(get_current_user)):
    """获取用户角色列表（需要用户管理权限）"""
    try:
        user_roles = rbac_service.get_user_roles(user_id)
        roles_data = [
            {
                "id": role.id,
                "name": role.name,
                "description": role.description,
                "is_system_role": role.is_system_role
            }
            for role in user_roles
        ]

        return {"user_id": user_id, "roles": roles_data}

    except Exception as e:
        logger.error(f"获取用户角色失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户角色失败"
        )

@app.get("/api/v1/users/{user_id}/permissions")
@require_user_manage
async def get_user_permissions_api(user_id: str, current_user = Depends(get_current_user)):
    """获取用户权限列表（需要用户管理权限）"""
    try:
        user_permissions = rbac_service.get_user_permissions(user_id)

        return {"user_id": user_id, "permissions": list(user_permissions)}

    except Exception as e:
        logger.error(f"获取用户权限失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户权限失败"
        )

@app.post("/api/v1/users/check-permission")
async def check_user_permission(permission_check: PermissionCheckRequest, current_user = Depends(get_current_user)):
    """检查当前用户权限"""
    try:
        permission_id = f"{permission_check.resource_type}:{permission_check.action}"
        has_permission = rbac_service.check_permission(
            current_user.id,
            permission_id,
            permission_check.resource_id
        )

        return {
            "user_id": current_user.id,
            "permission": permission_id,
            "resource_id": permission_check.resource_id,
            "has_permission": has_permission
        }

    except Exception as e:
        logger.error(f"检查权限失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="检查权限失败"
        )

@app.get("/api/v1/roles")
@require_user_manage
async def get_all_roles(current_user = Depends(get_current_user)):
    """获取所有角色列表（需要用户管理权限）"""
    try:
        roles_data = []
        for role in rbac_service.roles.values():
            if role.is_active:
                roles_data.append({
                    "id": role.id,
                    "name": role.name,
                    "description": role.description,
                    "permissions": role.permissions,
                    "is_system_role": role.is_system_role,
                    "created_at": role.created_at.isoformat(),
                    "updated_at": role.updated_at.isoformat()
                })

        return {"roles": roles_data}

    except Exception as e:
        logger.error(f"获取角色列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取角色列表失败"
        )

@app.get("/api/v1/permissions")
@require_user_manage
async def get_all_permissions(current_user = Depends(get_current_user)):
    """获取所有权限列表（需要用户管理权限）"""
    try:
        permissions_data = []
        for permission in rbac_service.permissions.values():
            if permission.is_active:
                permissions_data.append({
                    "id": permission.id,
                    "name": permission.name,
                    "description": permission.description,
                    "resource_type": permission.resource_type.value,
                    "action": permission.action.value,
                    "conditions": permission.conditions,
                    "created_at": permission.created_at.isoformat(),
                    "updated_at": permission.updated_at.isoformat()
                })

        return {"permissions": permissions_data}

    except Exception as e:
        logger.error(f"获取权限列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取权限列表失败"
        )

# ==================== 角色管理API接口 ====================

@app.post("/api/v1/roles")
@require_admin
async def create_role(role_data: RoleCreateRequest, current_user = Depends(get_current_user)):
    """创建新角色（需要管理员权限）"""
    try:
        # 检查角色ID是否已存在
        if role_data.id in rbac_service.roles:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="角色ID已存在"
            )

        # 验证权限ID是否有效
        invalid_permissions = []
        for perm_id in role_data.permissions:
            if perm_id not in rbac_service.permissions:
                invalid_permissions.append(perm_id)

        if invalid_permissions:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"无效的权限ID: {', '.join(invalid_permissions)}"
            )

        # 创建角色
        role = rbac_service.create_role({
            "id": role_data.id,
            "name": role_data.name,
            "description": role_data.description,
            "permissions": role_data.permissions
        })

        if role:
            logger.info(f"管理员 {current_user.username} 创建角色: {role_data.id}")
            return {
                "message": "角色创建成功",
                "role": {
                    "id": role.id,
                    "name": role.name,
                    "description": role.description,
                    "permissions": role.permissions,
                    "is_system_role": role.is_system_role,
                    "created_at": role.created_at.isoformat()
                }
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="角色创建失败"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建角色失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建角色失败"
        )

@app.put("/api/v1/roles/{role_id}")
@require_admin
async def update_role(role_id: str, role_updates: RoleUpdateRequest, current_user = Depends(get_current_user)):
    """更新角色（需要管理员权限）"""
    try:
        # 检查角色是否存在
        if role_id not in rbac_service.roles:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="角色不存在"
            )

        # 准备更新数据
        updates = {}
        if role_updates.name is not None:
            updates["name"] = role_updates.name
        if role_updates.description is not None:
            updates["description"] = role_updates.description
        if role_updates.permissions is not None:
            # 验证权限ID是否有效
            invalid_permissions = []
            for perm_id in role_updates.permissions:
                if perm_id not in rbac_service.permissions:
                    invalid_permissions.append(perm_id)

            if invalid_permissions:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"无效的权限ID: {', '.join(invalid_permissions)}"
                )

            updates["permissions"] = role_updates.permissions

        # 更新角色
        success = rbac_service.update_role(role_id, updates)

        if success:
            logger.info(f"管理员 {current_user.username} 更新角色: {role_id}")
            updated_role = rbac_service.roles[role_id]
            return {
                "message": "角色更新成功",
                "role": {
                    "id": updated_role.id,
                    "name": updated_role.name,
                    "description": updated_role.description,
                    "permissions": updated_role.permissions,
                    "is_system_role": updated_role.is_system_role,
                    "updated_at": updated_role.updated_at.isoformat()
                }
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="角色更新失败"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新角色失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新角色失败"
        )

@app.delete("/api/v1/roles/{role_id}")
@require_admin
async def delete_role(role_id: str, current_user = Depends(get_current_user)):
    """删除角色（需要管理员权限）"""
    try:
        success = rbac_service.delete_role(role_id)

        if success:
            logger.info(f"管理员 {current_user.username} 删除角色: {role_id}")
            return {"message": "角色删除成功", "role_id": role_id}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="角色删除失败"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除角色失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除角色失败"
        )

@app.get("/api/v1/roles/{role_id}")
@require_user_manage
async def get_role_details(role_id: str, current_user = Depends(get_current_user)):
    """获取角色详情（需要用户管理权限）"""
    try:
        role = rbac_service.roles.get(role_id)

        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="角色不存在"
            )

        return {
            "role": {
                "id": role.id,
                "name": role.name,
                "description": role.description,
                "permissions": role.permissions,
                "is_system_role": role.is_system_role,
                "is_active": role.is_active,
                "created_at": role.created_at.isoformat(),
                "updated_at": role.updated_at.isoformat()
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取角色详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取角色详情失败"
        )

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8001,
        reload=True,
        log_level="info"
    )