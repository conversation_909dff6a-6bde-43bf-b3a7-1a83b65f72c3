"""
RBAC权限检查服务

提供基于角色的访问控制功能，包括权限验证、角色管理等
"""

from typing import List, Dict, Optional, Set
from datetime import datetime
import logging
from ..models.rbac import (
    Permission, Role, UserRole, RoleHierarchy,
    SYSTEM_PERMISSIONS, SYSTEM_ROLES,
    ResourceType, ActionType
)

logger = logging.getLogger(__name__)

class RBACService:
    """RBAC权限管理服务"""
    
    def __init__(self):
        # 模拟数据库存储
        self.permissions: Dict[str, Permission] = SYSTEM_PERMISSIONS.copy()
        self.roles: Dict[str, Role] = SYSTEM_ROLES.copy()
        self.user_roles: Dict[str, List[UserRole]] = {}  # user_id -> [UserRole]
        self.role_hierarchy: List[RoleHierarchy] = []
        
        # 初始化角色层次结构
        self._initialize_role_hierarchy()
    
    def _initialize_role_hierarchy(self):
        """初始化角色层次结构"""
        # 管理员 > 风险管理员 > 交易员 > 分析师 > 观察者
        hierarchy_data = [
            ("admin", "risk_manager"),
            ("admin", "trader"),
            ("trader", "analyst"),
            ("analyst", "viewer"),
            ("risk_manager", "viewer")
        ]
        
        for parent, child in hierarchy_data:
            self.role_hierarchy.append(RoleHierarchy(
                parent_role_id=parent,
                child_role_id=child,
                created_at=datetime.now()
            ))
    
    def assign_role_to_user(self, user_id: str, role_id: str, assigned_by: str, 
                           expires_at: Optional[datetime] = None) -> bool:
        """为用户分配角色"""
        try:
            # 检查角色是否存在
            if role_id not in self.roles:
                logger.error(f"角色不存在: {role_id}")
                return False
            
            # 检查用户是否已有该角色
            user_roles = self.user_roles.get(user_id, [])
            for user_role in user_roles:
                if user_role.role_id == role_id and user_role.is_active:
                    logger.warning(f"用户 {user_id} 已拥有角色 {role_id}")
                    return True
            
            # 创建用户角色关联
            user_role = UserRole(
                user_id=user_id,
                role_id=role_id,
                assigned_by=assigned_by,
                assigned_at=datetime.now(),
                expires_at=expires_at,
                is_active=True
            )
            
            if user_id not in self.user_roles:
                self.user_roles[user_id] = []
            
            self.user_roles[user_id].append(user_role)
            
            logger.info(f"成功为用户 {user_id} 分配角色 {role_id}")
            return True
            
        except Exception as e:
            logger.error(f"分配角色失败: {e}")
            return False
    
    def revoke_role_from_user(self, user_id: str, role_id: str) -> bool:
        """撤销用户角色"""
        try:
            user_roles = self.user_roles.get(user_id, [])
            
            for user_role in user_roles:
                if user_role.role_id == role_id and user_role.is_active:
                    user_role.is_active = False
                    logger.info(f"成功撤销用户 {user_id} 的角色 {role_id}")
                    return True
            
            logger.warning(f"用户 {user_id} 没有角色 {role_id}")
            return False
            
        except Exception as e:
            logger.error(f"撤销角色失败: {e}")
            return False
    
    def get_user_roles(self, user_id: str) -> List[Role]:
        """获取用户的所有有效角色"""
        try:
            user_roles = self.user_roles.get(user_id, [])
            active_roles = []
            
            for user_role in user_roles:
                # 检查角色是否激活且未过期
                if not user_role.is_active:
                    continue
                
                if user_role.expires_at and datetime.now() > user_role.expires_at:
                    user_role.is_active = False
                    continue
                
                role = self.roles.get(user_role.role_id)
                if role and role.is_active:
                    active_roles.append(role)
            
            return active_roles
            
        except Exception as e:
            logger.error(f"获取用户角色失败: {e}")
            return []
    
    def get_user_permissions(self, user_id: str) -> Set[str]:
        """获取用户的所有权限"""
        try:
            permissions = set()
            user_roles = self.get_user_roles(user_id)
            
            for role in user_roles:
                # 添加角色的直接权限
                permissions.update(role.permissions)
                
                # 添加继承的权限（从父角色）
                inherited_permissions = self._get_inherited_permissions(role.id)
                permissions.update(inherited_permissions)
            
            # 过滤掉无效的权限
            valid_permissions = set()
            for perm_id in permissions:
                permission = self.permissions.get(perm_id)
                if permission and permission.is_active:
                    valid_permissions.add(perm_id)
            
            return valid_permissions
            
        except Exception as e:
            logger.error(f"获取用户权限失败: {e}")
            return set()
    
    def _get_inherited_permissions(self, role_id: str) -> Set[str]:
        """获取角色继承的权限"""
        inherited_permissions = set()
        
        # 查找父角色
        for hierarchy in self.role_hierarchy:
            if hierarchy.child_role_id == role_id:
                parent_role = self.roles.get(hierarchy.parent_role_id)
                if parent_role and parent_role.is_active:
                    inherited_permissions.update(parent_role.permissions)
                    # 递归获取父角色的继承权限
                    inherited_permissions.update(
                        self._get_inherited_permissions(parent_role.id)
                    )
        
        return inherited_permissions
    
    def check_permission(self, user_id: str, permission_id: str, 
                        resource_id: Optional[str] = None) -> bool:
        """检查用户是否有特定权限"""
        try:
            user_permissions = self.get_user_permissions(user_id)
            
            # 检查是否有该权限
            if permission_id not in user_permissions:
                return False
            
            # 获取权限详情
            permission = self.permissions.get(permission_id)
            if not permission or not permission.is_active:
                return False
            
            # 检查权限条件（如果有）
            if permission.conditions:
                return self._check_permission_conditions(
                    user_id, permission, resource_id
                )
            
            return True
            
        except Exception as e:
            logger.error(f"权限检查失败: {e}")
            return False
    
    def _check_permission_conditions(self, user_id: str, permission: Permission, 
                                   resource_id: Optional[str]) -> bool:
        """检查权限条件"""
        try:
            conditions = permission.conditions or {}
            
            # 检查时间限制
            if "time_restriction" in conditions:
                time_restriction = conditions["time_restriction"]
                current_hour = datetime.now().hour
                
                start_hour = time_restriction.get("start_hour", 0)
                end_hour = time_restriction.get("end_hour", 23)
                
                if not (start_hour <= current_hour <= end_hour):
                    return False
            
            # 检查资源所有权
            if "owner_only" in conditions and conditions["owner_only"]:
                if not resource_id:
                    return False
                # 这里应该检查资源是否属于用户
                # 实际实现中需要查询资源数据库
                pass
            
            # 检查数据范围限制
            if "data_scope" in conditions:
                data_scope = conditions["data_scope"]
                # 实现数据范围检查逻辑
                pass
            
            return True
            
        except Exception as e:
            logger.error(f"权限条件检查失败: {e}")
            return False
    
    def has_role(self, user_id: str, role_id: str) -> bool:
        """检查用户是否有特定角色"""
        user_roles = self.get_user_roles(user_id)
        return any(role.id == role_id for role in user_roles)
    
    def is_admin(self, user_id: str) -> bool:
        """检查用户是否为管理员"""
        return self.has_role(user_id, "admin")
    
    def can_access_resource(self, user_id: str, resource_type: ResourceType, 
                           action: ActionType, resource_id: Optional[str] = None) -> bool:
        """检查用户是否可以访问特定资源"""
        permission_id = f"{resource_type.value}:{action.value}"
        return self.check_permission(user_id, permission_id, resource_id)
    
    def get_accessible_resources(self, user_id: str, resource_type: ResourceType) -> List[str]:
        """获取用户可访问的资源列表"""
        # 这里应该根据用户权限返回可访问的资源ID列表
        # 实际实现中需要查询资源数据库
        accessible_resources = []
        
        # 检查基本读取权限
        if self.can_access_resource(user_id, resource_type, ActionType.READ):
            # 如果有读取权限，返回所有资源（简化实现）
            # 实际应用中应该根据具体的权限条件过滤
            accessible_resources = ["all"]  # 占位符
        
        return accessible_resources
    
    def create_role(self, role_data: Dict) -> Optional[Role]:
        """创建新角色"""
        try:
            role = Role(
                id=role_data["id"],
                name=role_data["name"],
                description=role_data["description"],
                permissions=role_data.get("permissions", []),
                is_system_role=False,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            self.roles[role.id] = role
            logger.info(f"成功创建角色: {role.id}")
            return role
            
        except Exception as e:
            logger.error(f"创建角色失败: {e}")
            return None
    
    def update_role(self, role_id: str, updates: Dict) -> bool:
        """更新角色"""
        try:
            role = self.roles.get(role_id)
            if not role:
                logger.error(f"角色不存在: {role_id}")
                return False
            
            # 系统角色不允许修改
            if role.is_system_role:
                logger.error(f"系统角色不允许修改: {role_id}")
                return False
            
            # 更新角色属性
            for key, value in updates.items():
                if hasattr(role, key) and key != "id":
                    setattr(role, key, value)
            
            role.updated_at = datetime.now()
            logger.info(f"成功更新角色: {role_id}")
            return True
            
        except Exception as e:
            logger.error(f"更新角色失败: {e}")
            return False
    
    def delete_role(self, role_id: str) -> bool:
        """删除角色"""
        try:
            role = self.roles.get(role_id)
            if not role:
                logger.error(f"角色不存在: {role_id}")
                return False
            
            # 系统角色不允许删除
            if role.is_system_role:
                logger.error(f"系统角色不允许删除: {role_id}")
                return False
            
            # 检查是否有用户使用该角色
            users_with_role = []
            for user_id, user_roles in self.user_roles.items():
                for user_role in user_roles:
                    if user_role.role_id == role_id and user_role.is_active:
                        users_with_role.append(user_id)
            
            if users_with_role:
                logger.error(f"角色 {role_id} 仍被用户使用，无法删除: {users_with_role}")
                return False
            
            del self.roles[role_id]
            logger.info(f"成功删除角色: {role_id}")
            return True
            
        except Exception as e:
            logger.error(f"删除角色失败: {e}")
            return False

# 全局RBAC服务实例
rbac_service = RBACService()
