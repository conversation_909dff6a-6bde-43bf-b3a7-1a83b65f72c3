"""
认证和权限检查中间件

提供API接口的认证验证和权限控制功能
"""

from fastapi import HTTPException, Depends, status
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from typing import List, Optional, Callable, Any
from functools import wraps
import logging

from ..services.rbac_service import rbac_service
from ..models.rbac import ResourceType, ActionType

logger = logging.getLogger(__name__)
security = HTTPBearer()

class AuthMiddleware:
    """认证中间件类"""
    
    def __init__(self):
        self.tokens_db = {}  # 这里应该注入实际的token存储
    
    def set_tokens_db(self, tokens_db):
        """设置token数据库引用"""
        self.tokens_db = tokens_db
    
    async def get_current_user_id(self, credentials: HTTPAuthorizationCredentials = Depends(security)) -> str:
        """获取当前用户ID"""
        try:
            token = credentials.credentials
            token_data = self.tokens_db.get(token)
            
            if not token_data:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="无效的访问令牌",
                    headers={"WWW-Authenticate": "Bearer"}
                )
            
            # 检查令牌是否过期
            from datetime import datetime
            expires_at = datetime.fromisoformat(token_data["expires_at"])
            if datetime.now() > expires_at:
                del self.tokens_db[token]
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="访问令牌已过期",
                    headers={"WWW-Authenticate": "Bearer"}
                )
            
            return token_data["user_id"]
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"获取用户ID失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="认证服务异常"
            )

# 全局认证中间件实例
auth_middleware = AuthMiddleware()

def require_permission(permission_id: str, resource_id_param: Optional[str] = None):
    """
    权限检查装饰器
    
    Args:
        permission_id: 权限ID，格式为 "resource_type:action"
        resource_id_param: 资源ID参数名（从路径参数或查询参数中获取）
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 获取当前用户ID
            user_id = None
            for arg in args:
                if hasattr(arg, 'credentials'):
                    user_id = await auth_middleware.get_current_user_id(arg)
                    break
            
            if not user_id:
                # 尝试从kwargs中获取用户ID
                current_user = kwargs.get('current_user')
                if current_user and hasattr(current_user, 'id'):
                    user_id = current_user.id
                else:
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="未找到用户认证信息"
                    )
            
            # 获取资源ID
            resource_id = None
            if resource_id_param:
                resource_id = kwargs.get(resource_id_param)
            
            # 检查权限
            if not rbac_service.check_permission(user_id, permission_id, resource_id):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"权限不足，需要权限: {permission_id}"
                )
            
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator

def require_role(role_id: str):
    """
    角色检查装饰器
    
    Args:
        role_id: 角色ID
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 获取当前用户ID
            user_id = None
            for arg in args:
                if hasattr(arg, 'credentials'):
                    user_id = await auth_middleware.get_current_user_id(arg)
                    break
            
            if not user_id:
                current_user = kwargs.get('current_user')
                if current_user and hasattr(current_user, 'id'):
                    user_id = current_user.id
                else:
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="未找到用户认证信息"
                    )
            
            # 检查角色
            if not rbac_service.has_role(user_id, role_id):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"权限不足，需要角色: {role_id}"
                )
            
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator

def require_admin():
    """管理员权限检查装饰器"""
    return require_role("admin")

def require_resource_access(resource_type: ResourceType, action: ActionType, 
                          resource_id_param: Optional[str] = None):
    """
    资源访问权限检查装饰器
    
    Args:
        resource_type: 资源类型
        action: 操作类型
        resource_id_param: 资源ID参数名
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 获取当前用户ID
            user_id = None
            for arg in args:
                if hasattr(arg, 'credentials'):
                    user_id = await auth_middleware.get_current_user_id(arg)
                    break
            
            if not user_id:
                current_user = kwargs.get('current_user')
                if current_user and hasattr(current_user, 'id'):
                    user_id = current_user.id
                else:
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="未找到用户认证信息"
                    )
            
            # 获取资源ID
            resource_id = None
            if resource_id_param:
                resource_id = kwargs.get(resource_id_param)
            
            # 检查资源访问权限
            if not rbac_service.can_access_resource(user_id, resource_type, action, resource_id):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"权限不足，无法{action.value}{resource_type.value}资源"
                )
            
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator

class PermissionChecker:
    """权限检查器类，用于在业务逻辑中进行权限检查"""
    
    @staticmethod
    def check_permission(user_id: str, permission_id: str, resource_id: Optional[str] = None) -> bool:
        """检查用户权限"""
        return rbac_service.check_permission(user_id, permission_id, resource_id)
    
    @staticmethod
    def check_role(user_id: str, role_id: str) -> bool:
        """检查用户角色"""
        return rbac_service.has_role(user_id, role_id)
    
    @staticmethod
    def check_resource_access(user_id: str, resource_type: ResourceType, 
                            action: ActionType, resource_id: Optional[str] = None) -> bool:
        """检查资源访问权限"""
        return rbac_service.can_access_resource(user_id, resource_type, action, resource_id)
    
    @staticmethod
    def is_admin(user_id: str) -> bool:
        """检查是否为管理员"""
        return rbac_service.is_admin(user_id)
    
    @staticmethod
    def get_user_permissions(user_id: str) -> List[str]:
        """获取用户权限列表"""
        return list(rbac_service.get_user_permissions(user_id))
    
    @staticmethod
    def get_user_roles(user_id: str) -> List[str]:
        """获取用户角色列表"""
        roles = rbac_service.get_user_roles(user_id)
        return [role.id for role in roles]
    
    @staticmethod
    def require_permission_or_raise(user_id: str, permission_id: str, 
                                  resource_id: Optional[str] = None):
        """检查权限，如果没有权限则抛出异常"""
        if not rbac_service.check_permission(user_id, permission_id, resource_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"权限不足，需要权限: {permission_id}"
            )
    
    @staticmethod
    def require_role_or_raise(user_id: str, role_id: str):
        """检查角色，如果没有角色则抛出异常"""
        if not rbac_service.has_role(user_id, role_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"权限不足，需要角色: {role_id}"
            )
    
    @staticmethod
    def require_admin_or_raise(user_id: str):
        """检查管理员权限，如果不是管理员则抛出异常"""
        if not rbac_service.is_admin(user_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，需要管理员权限"
            )

# 全局权限检查器实例
permission_checker = PermissionChecker()

# 常用权限检查装饰器
require_strategy_read = require_permission("strategy:read")
require_strategy_create = require_permission("strategy:create")
require_strategy_update = require_permission("strategy:update")
require_strategy_delete = require_permission("strategy:delete")
require_strategy_execute = require_permission("strategy:execute")

require_trading_read = require_permission("trading:read")
require_trading_create = require_permission("trading:create")
require_trading_cancel = require_permission("trading:cancel")

require_data_read = require_permission("data:read")
require_data_manage = require_permission("data:manage")

require_risk_read = require_permission("risk:read")
require_risk_manage = require_permission("risk:manage")

require_backtest_read = require_permission("backtest:read")
require_backtest_create = require_permission("backtest:create")

require_user_read = require_permission("user:read")
require_user_manage = require_permission("user:manage")

require_system_manage = require_permission("system:manage")

require_report_read = require_permission("report:read")
require_report_create = require_permission("report:create")
