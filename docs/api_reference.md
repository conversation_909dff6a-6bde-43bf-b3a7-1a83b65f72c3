# 事件驱动回测系统 API 参考文档

## 1. 核心事件类 (Events)

### 1.1 Event 基类

所有事件的抽象基类，定义了事件的基本属性和接口。

```python
@dataclass
class Event(ABC):
    event_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    event_type: EventType = field(init=False)
    timestamp: datetime = field(default_factory=datetime.now)
    source: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @abstractmethod
    def to_dict(self) -> Dict[str, Any]:
        """将事件转换为字典格式"""
        pass
    
    @classmethod
    @abstractmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Event':
        """从字典创建事件实例"""
        pass
```

### 1.2 MarketDataEvent 市场数据事件

#### BarEvent K线数据事件

```python
@dataclass
class BarEvent(MarketDataEvent):
    symbol: str                    # 交易品种代码
    open_price: Decimal           # 开盘价
    high_price: Decimal           # 最高价
    low_price: Decimal            # 最低价
    close_price: Decimal          # 收盘价
    volume: int                   # 成交量
    timeframe: str = "1D"         # 时间周期
    
    @property
    def typical_price(self) -> Decimal:
        """典型价格 (HLC/3)"""
        return (self.high_price + self.low_price + self.close_price) / 3
    
    @property
    def price_change_pct(self) -> Decimal:
        """价格变化百分比"""
        if self.open_price == 0:
            return Decimal('0')
        return (self.close_price - self.open_price) / self.open_price * 100
```

#### TickEvent Tick数据事件

```python
@dataclass
class TickEvent(MarketDataEvent):
    symbol: str                   # 交易品种代码
    price: Decimal               # 价格
    size: int                    # 数量
    tick_type: str = "TRADE"     # Tick类型: TRADE, BID, ASK
    bid_price: Optional[Decimal] = None    # 买价
    ask_price: Optional[Decimal] = None    # 卖价
    bid_size: Optional[int] = None         # 买量
    ask_size: Optional[int] = None         # 卖量
    
    @property
    def spread(self) -> Optional[Decimal]:
        """买卖价差"""
        if self.bid_price and self.ask_price:
            return self.ask_price - self.bid_price
        return None
```

### 1.3 SignalEvent 信号事件

#### StrategySignal 策略信号事件

```python
@dataclass
class StrategySignal(SignalEvent):
    symbol: str                   # 交易品种
    signal_type: SignalType      # 信号类型: BUY, SELL, HOLD等
    strength: SignalStrength     # 信号强度: WEAK, MODERATE, STRONG等
    confidence: float            # 信号置信度 0-1
    strategy_id: str             # 策略ID
    strategy_name: str           # 策略名称
    indicator_values: Dict[str, float] = field(default_factory=dict)
    reasoning: str = ""          # 信号产生原因
    
    def add_indicator(self, name: str, value: float) -> None:
        """添加技术指标值"""
        self.indicator_values[name] = value
```

### 1.4 OrderEvent 订单事件

#### OrderSubmittedEvent 订单提交事件

```python
@dataclass
class OrderSubmittedEvent(OrderEvent):
    order_id: str                # 订单ID
    symbol: str                  # 交易品种
    side: OrderSide             # 订单方向: BUY, SELL
    order_type: OrderType       # 订单类型: MARKET, LIMIT等
    quantity: int               # 订单数量
    price: Optional[Decimal]    # 订单价格
    exchange: str = ""          # 交易所
    commission_rate: float = 0.0 # 手续费率
```

#### OrderFilledEvent 订单成交事件

```python
@dataclass
class OrderFilledEvent(OrderEvent):
    fill_price: Decimal         # 成交价格
    fill_quantity: int          # 成交数量
    fill_time: datetime         # 成交时间
    commission: Decimal = Decimal('0')  # 手续费
    trade_id: Optional[str] = None      # 交易ID
    
    @property
    def fill_value(self) -> Decimal:
        """成交金额"""
        return self.fill_price * self.fill_quantity
    
    @property
    def net_value(self) -> Decimal:
        """净成交金额（扣除手续费）"""
        if self.side == OrderSide.BUY:
            return self.fill_value + self.commission
        else:
            return self.fill_value - self.commission
```

## 2. 核心引擎类 (Engines)

### 2.1 DataEngine 数据引擎

负责历史数据的加载、处理和回放。

```python
class DataEngine(EventHandler):
    def __init__(
        self,
        message_bus: MessageBus,
        data_provider: DataProvider,
        config: DataConfig
    ):
        """
        初始化数据引擎
        
        Args:
            message_bus: 消息总线
            data_provider: 数据提供者
            config: 数据配置
        """
    
    def start_replay(self, speed: float = 1.0) -> None:
        """
        开始数据回放
        
        Args:
            speed: 回放速度倍数，0表示最快速度
        """
    
    def stop_replay(self) -> None:
        """停止数据回放"""
    
    def get_current_time(self) -> Optional[datetime]:
        """获取当前回放时间"""
    
    def get_historical_data(self, symbol: str, periods: int = 100) -> Optional[pd.DataFrame]:
        """
        获取指定品种的历史数据
        
        Args:
            symbol: 交易品种
            periods: 历史周期数
            
        Returns:
            历史数据DataFrame
        """
```

### 2.2 StrategyEngine 策略引擎

负责策略管理和信号生成。

```python
class StrategyEngine(EventHandler):
    def __init__(self, message_bus: MessageBus):
        """初始化策略引擎"""
    
    def add_strategy(self, strategy: BaseStrategy) -> None:
        """添加策略"""
    
    def start_all_strategies(self) -> None:
        """启动所有策略"""
    
    def stop_all_strategies(self) -> None:
        """停止所有策略"""
    
    def get_strategy_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取策略统计信息"""
```

#### BaseStrategy 策略基类

```python
class BaseStrategy(ABC):
    def __init__(self, config: StrategyConfig):
        """初始化策略"""
    
    @abstractmethod
    def on_market_data(self, event: MarketDataEvent) -> Optional[StrategySignal]:
        """处理市场数据事件"""
    
    @abstractmethod
    def on_bar_data(self, event: BarEvent) -> Optional[StrategySignal]:
        """处理K线数据事件"""
    
    def on_tick_data(self, event: TickEvent) -> Optional[StrategySignal]:
        """处理Tick数据事件（可选实现）"""
    
    def start(self) -> None:
        """启动策略"""
    
    def stop(self) -> None:
        """停止策略"""
    
    def get_position(self, symbol: str) -> int:
        """获取指定品种的仓位"""
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取策略绩效统计"""
```

### 2.3 ExecutionEngine 执行引擎

负责订单管理和交易执行。

```python
class ExecutionEngine(EventHandler):
    def __init__(
        self,
        message_bus: MessageBus,
        portfolio: Portfolio,
        commission_rate: float = 0.0003,
        slippage_rate: float = 0.001
    ):
        """
        初始化执行引擎
        
        Args:
            message_bus: 消息总线
            portfolio: 投资组合
            commission_rate: 手续费率
            slippage_rate: 滑点率
        """
    
    def get_portfolio_stats(self) -> Dict[str, Any]:
        """获取投资组合统计"""
```

#### Portfolio 投资组合

```python
class Portfolio:
    def __init__(self, initial_capital: Decimal = Decimal('1000000')):
        """初始化投资组合"""
    
    def get_position(self, symbol: str) -> Optional[Position]:
        """获取仓位"""
    
    def update_position(
        self,
        symbol: str,
        side: OrderSide,
        quantity: int,
        price: Decimal,
        commission: Decimal = Decimal('0')
    ) -> Position:
        """更新仓位"""
    
    def get_total_value(self) -> Decimal:
        """获取总资产价值"""
    
    def get_total_pnl(self) -> Decimal:
        """获取总盈亏"""
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取绩效统计"""
```

### 2.4 RiskEngine 风险引擎

负责实时风险监控和控制。

```python
class RiskEngine(EventHandler):
    def __init__(
        self,
        message_bus: MessageBus,
        risk_limits: RiskLimits,
        portfolio_id: str = "default"
    ):
        """初始化风险引擎"""
    
    def add_risk_rule(self, rule: RiskRule) -> None:
        """添加风险规则"""
    
    def get_risk_stats(self) -> Dict[str, Any]:
        """获取风险统计"""
```

#### RiskRule 风险规则基类

```python
class RiskRule(ABC):
    @abstractmethod
    def check_risk(self, event: Event, context: Dict[str, Any]) -> Optional[RiskEvent]:
        """检查风险"""
    
    @abstractmethod
    def get_rule_name(self) -> str:
        """获取规则名称"""
```

## 3. 消息系统 (Message System)

### 3.1 MessageBus 消息总线

```python
class MessageBus:
    def __init__(self, max_workers: int = 4, enable_async: bool = True):
        """初始化消息总线"""
    
    def subscribe(
        self,
        handler: Union[EventHandler, Callable[[Event], None]],
        event_types: Union[EventType, List[EventType], None] = None,
        priority: int = 0,
        filters: Optional[List[EventFilter]] = None,
        handler_name: Optional[str] = None
    ) -> str:
        """
        订阅事件
        
        Args:
            handler: 事件处理器
            event_types: 事件类型列表
            priority: 处理优先级
            filters: 事件过滤器
            handler_name: 处理器名称
            
        Returns:
            订阅ID
        """
    
    def publish(self, event: Event, immediate: bool = False) -> None:
        """
        发布事件
        
        Args:
            event: 要发布的事件
            immediate: 是否立即处理
        """
    
    def start(self) -> None:
        """启动消息总线"""
    
    def stop(self) -> None:
        """停止消息总线"""
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
```

### 3.2 EventQueue 事件队列

```python
class EventQueue:
    def __init__(self, max_size: int = 10000, enable_priority: bool = True):
        """初始化事件队列"""
    
    def put(self, event: Event, priority: int = 0, max_retries: int = 3) -> bool:
        """将事件加入队列"""
    
    def get(self, timeout: Optional[float] = None) -> Optional[QueuedEvent]:
        """从队列获取事件"""
    
    def get_batch(self, batch_size: int = 10) -> List[QueuedEvent]:
        """批量获取事件"""
    
    def size(self) -> int:
        """获取队列大小"""
    
    def clear(self) -> int:
        """清空队列"""
```

## 4. 回测引擎 (Backtest Engine)

### 4.1 EventDrivenBacktester 事件驱动回测器

```python
class EventDrivenBacktester:
    def __init__(
        self,
        config: BacktestConfig,
        data_provider: DataProvider,
        strategies: List[BaseStrategy],
        risk_limits: Optional[RiskLimits] = None
    ):
        """初始化事件驱动回测器"""
    
    def run_backtest(
        self, 
        progress_callback: Optional[Callable[[float], None]] = None
    ) -> BacktestResult:
        """
        运行回测
        
        Args:
            progress_callback: 进度回调函数
            
        Returns:
            回测结果
        """
    
    def stop(self) -> None:
        """停止回测"""
    
    def is_running(self) -> bool:
        """检查是否正在运行"""
```

### 4.2 BacktestResult 回测结果

```python
@dataclass
class BacktestResult:
    config: BacktestConfig              # 回测配置
    start_time: datetime               # 开始时间
    end_time: datetime                 # 结束时间
    duration: timedelta                # 持续时间
    
    # 绩效指标
    initial_capital: Decimal           # 初始资金
    final_capital: Decimal             # 最终资金
    total_return: Decimal              # 总收益率
    annual_return: Decimal             # 年化收益率
    volatility: Decimal                # 波动率
    sharpe_ratio: Decimal              # 夏普比率
    max_drawdown: Decimal              # 最大回撤
    
    # 交易统计
    total_trades: int                  # 总交易次数
    winning_trades: int                # 盈利交易次数
    losing_trades: int                 # 亏损交易次数
    win_rate: Decimal                  # 胜率
    avg_win: Decimal                   # 平均盈利
    avg_loss: Decimal                  # 平均亏损
    profit_factor: Decimal             # 盈亏比
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
```

## 5. 配置类 (Configuration)

### 5.1 BacktestConfig 回测配置

```python
@dataclass
class BacktestConfig:
    start_date: datetime               # 回测开始日期
    end_date: datetime                 # 回测结束日期
    initial_capital: Decimal           # 初始资金
    symbols: List[str]                 # 交易品种列表
    timeframe: str = "1D"              # 数据频率
    commission_rate: float = 0.0003    # 手续费率
    slippage_rate: float = 0.001       # 滑点率
    data_source: str = "csv"           # 数据源类型
    data_path: str = "data/"           # 数据路径
    benchmark: str = "000300.SH"       # 基准指数
```

### 5.2 StrategyConfig 策略配置

```python
@dataclass
class StrategyConfig:
    strategy_id: str                   # 策略唯一标识
    strategy_name: str                 # 策略名称
    symbols: List[str]                 # 交易品种
    parameters: Dict[str, Any]         # 策略参数
    enabled: bool = True               # 是否启用
    max_position_size: int = 1000      # 最大仓位大小
    risk_limit: float = 0.02           # 风险限制
```

### 5.3 RiskLimits 风险限制

```python
@dataclass
class RiskLimits:
    max_position_size: int = 1000                    # 最大单个仓位
    max_portfolio_value: Decimal = Decimal('10000000')  # 最大组合价值
    max_daily_loss: Decimal = Decimal('50000')       # 最大日损失
    max_drawdown: Decimal = Decimal('0.1')           # 最大回撤比例
    max_leverage: Decimal = Decimal('3.0')           # 最大杠杆倍数
    max_concentration: Decimal = Decimal('0.2')      # 最大集中度
    stop_loss_pct: Decimal = Decimal('0.02')         # 止损百分比
    take_profit_pct: Decimal = Decimal('0.05')       # 止盈百分比
```

## 6. 使用示例

### 6.1 基本回测流程

```python
# 1. 创建配置
config = BacktestConfig(
    start_date=datetime(2023, 1, 1),
    end_date=datetime(2023, 12, 31),
    initial_capital=Decimal('1000000'),
    symbols=["000001.SZ", "000002.SZ"]
)

# 2. 创建数据提供者
data_provider = HistoricalDataProvider(data_path="data/")

# 3. 创建策略
strategy_config = StrategyConfig(
    strategy_id="ma_cross_001",
    strategy_name="移动平均线交叉策略",
    symbols=["000001.SZ", "000002.SZ"],
    parameters={'short_window': 10, 'long_window': 30}
)
strategy = MovingAverageCrossoverStrategy(strategy_config)

# 4. 创建回测器
backtester = EventDrivenBacktester(
    config=config,
    data_provider=data_provider,
    strategies=[strategy]
)

# 5. 运行回测
result = backtester.run_backtest()

# 6. 分析结果
print(f"总收益率: {result.total_return:.2%}")
print(f"夏普比率: {result.sharpe_ratio:.3f}")
print(f"最大回撤: {result.max_drawdown:.2%}")
```

### 6.2 自定义策略示例

```python
class CustomStrategy(BaseStrategy):
    def on_bar_data(self, event: BarEvent) -> Optional[StrategySignal]:
        # 实现自定义策略逻辑
        if self.should_buy(event):
            return self._create_signal(
                symbol=event.symbol,
                signal_type=SignalType.BUY,
                confidence=0.8,
                reasoning="自定义买入条件满足"
            )
        return None
    
    def should_buy(self, event: BarEvent) -> bool:
        # 自定义买入条件
        return event.close_price > event.open_price
```

更多详细示例请参考 `examples/` 目录下的示例文件。
