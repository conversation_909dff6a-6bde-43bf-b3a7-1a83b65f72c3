# 事件驱动回测系统架构图

## 系统整体架构

```mermaid
graph TB
    subgraph "事件驱动回测系统"
        subgraph "事件层 Event Layer"
            E1[MarketDataEvent<br/>市场数据事件]
            E2[SignalEvent<br/>信号事件]
            E3[OrderEvent<br/>订单事件]
            E4[PortfolioEvent<br/>组合事件]
            E5[RiskEvent<br/>风险事件]
        end
        
        subgraph "消息系统 Message System"
            MB[MessageBus<br/>消息总线]
            EQ[EventQueue<br/>事件队列]
            EL[EventLoop<br/>事件循环]
        end
        
        subgraph "核心引擎 Core Engines"
            DE[DataEngine<br/>数据引擎]
            SE[StrategyEngine<br/>策略引擎]
            EE[ExecutionEngine<br/>执行引擎]
            RE[RiskEngine<br/>风险引擎]
            BE[BacktestEngine<br/>回测引擎]
        end
        
        subgraph "数据层 Data Layer"
            DP[DataProvider<br/>数据提供者]
            CSV[CSV文件]
            DB[数据库]
            API[API接口]
        end
        
        subgraph "策略层 Strategy Layer"
            BS[BaseStrategy<br/>策略基类]
            MA[MovingAverage<br/>移动平均策略]
            RSI[RSI策略]
            ML[机器学习策略]
        end
        
        subgraph "风险管理 Risk Management"
            RL[RiskLimits<br/>风险限制]
            RR[RiskRules<br/>风险规则]
            RM[RiskManager<br/>风险管理器]
        end
    end
    
    %% 数据流向
    DP --> DE
    CSV --> DP
    DB --> DP
    API --> DP
    
    DE --> MB
    SE --> MB
    EE --> MB
    RE --> MB
    
    MB --> EQ
    EQ --> EL
    
    BS --> SE
    MA --> SE
    RSI --> SE
    ML --> SE
    
    RL --> RE
    RR --> RE
    RM --> RE
    
    BE --> DE
    BE --> SE
    BE --> EE
    BE --> RE
    
    %% 事件流
    E1 -.-> DE
    E2 -.-> SE
    E3 -.-> EE
    E4 -.-> EE
    E5 -.-> RE
    
    style E1 fill:#e1f5fe
    style E2 fill:#f3e5f5
    style E3 fill:#e8f5e8
    style E4 fill:#fff3e0
    style E5 fill:#ffebee
    
    style MB fill:#e3f2fd
    style EQ fill:#e3f2fd
    style EL fill:#e3f2fd
    
    style DE fill:#f1f8e9
    style SE fill:#f1f8e9
    style EE fill:#f1f8e9
    style RE fill:#f1f8e9
    style BE fill:#f1f8e9
```

## 事件流程图

```mermaid
sequenceDiagram
    participant DE as DataEngine
    participant MB as MessageBus
    participant SE as StrategyEngine
    participant EE as ExecutionEngine
    participant RE as RiskEngine
    participant P as Portfolio
    
    Note over DE,P: 事件驱动回测流程
    
    DE->>MB: 发布MarketDataEvent
    MB->>SE: 分发到策略引擎
    SE->>SE: 处理市场数据
    SE->>MB: 发布StrategySignal
    
    MB->>RE: 分发到风险引擎
    RE->>RE: 检查风险规则
    alt 风险检查通过
        RE->>MB: 允许交易
        MB->>EE: 分发到执行引擎
        EE->>EE: 创建订单
        EE->>MB: 发布OrderEvent
        EE->>EE: 模拟订单执行
        EE->>MB: 发布FillEvent
        EE->>P: 更新投资组合
        P->>MB: 发布PortfolioEvent
    else 风险检查失败
        RE->>MB: 发布RiskEvent
        MB->>EE: 拒绝交易
    end
    
    Note over DE,P: 循环处理下一个数据点
```

## 数据流架构

```mermaid
flowchart LR
    subgraph "数据源 Data Sources"
        CSV[CSV文件]
        DB[(数据库)]
        API[API接口]
    end
    
    subgraph "数据处理 Data Processing"
        DP[DataProvider<br/>数据提供者]
        DV[数据验证]
        DC[数据清洗]
        CACHE[数据缓存]
    end
    
    subgraph "事件生成 Event Generation"
        BAR[BarEvent<br/>K线事件]
        TICK[TickEvent<br/>Tick事件]
        BOOK[OrderBookEvent<br/>订单簿事件]
    end
    
    subgraph "事件处理 Event Processing"
        MB[MessageBus<br/>消息总线]
        EQ[EventQueue<br/>事件队列]
        EH[EventHandler<br/>事件处理器]
    end
    
    CSV --> DP
    DB --> DP
    API --> DP
    
    DP --> DV
    DV --> DC
    DC --> CACHE
    
    CACHE --> BAR
    CACHE --> TICK
    CACHE --> BOOK
    
    BAR --> MB
    TICK --> MB
    BOOK --> MB
    
    MB --> EQ
    EQ --> EH
    
    style CSV fill:#e8f5e8
    style DB fill:#e8f5e8
    style API fill:#e8f5e8
    
    style DP fill:#fff3e0
    style DV fill:#fff3e0
    style DC fill:#fff3e0
    style CACHE fill:#fff3e0
    
    style BAR fill:#e1f5fe
    style TICK fill:#e1f5fe
    style BOOK fill:#e1f5fe
    
    style MB fill:#f3e5f5
    style EQ fill:#f3e5f5
    style EH fill:#f3e5f5
```

## 策略执行流程

```mermaid
stateDiagram-v2
    [*] --> 策略初始化
    策略初始化 --> 等待市场数据
    
    等待市场数据 --> 接收数据事件
    接收数据事件 --> 数据处理
    
    数据处理 --> 技术指标计算
    技术指标计算 --> 信号生成判断
    
    信号生成判断 --> 无信号 : 条件不满足
    信号生成判断 --> 生成交易信号 : 条件满足
    
    无信号 --> 等待市场数据
    
    生成交易信号 --> 风险检查
    风险检查 --> 信号被拒绝 : 风险过高
    风险检查 --> 订单创建 : 风险可控
    
    信号被拒绝 --> 等待市场数据
    
    订单创建 --> 订单提交
    订单提交 --> 订单执行
    订单执行 --> 仓位更新
    仓位更新 --> 等待市场数据
    
    等待市场数据 --> [*] : 回测结束
```

## 风险管理架构

```mermaid
graph TD
    subgraph "风险监控 Risk Monitoring"
        RM[RiskManager<br/>风险管理器]
        RR1[PositionSizeRule<br/>仓位大小规则]
        RR2[DrawdownRule<br/>回撤规则]
        RR3[ConcentrationRule<br/>集中度规则]
        RR4[DailyLossRule<br/>日损失规则]
    end
    
    subgraph "风险事件 Risk Events"
        RE1[仓位超限事件]
        RE2[回撤超限事件]
        RE3[集中度超限事件]
        RE4[日损失超限事件]
    end
    
    subgraph "风险响应 Risk Response"
        RA1[拒绝交易]
        RA2[强制平仓]
        RA3[降低仓位]
        RA4[停止策略]
    end
    
    RM --> RR1
    RM --> RR2
    RM --> RR3
    RM --> RR4
    
    RR1 --> RE1
    RR2 --> RE2
    RR3 --> RE3
    RR4 --> RE4
    
    RE1 --> RA1
    RE2 --> RA2
    RE3 --> RA3
    RE4 --> RA4
    
    style RM fill:#ffebee
    style RR1 fill:#fff3e0
    style RR2 fill:#fff3e0
    style RR3 fill:#fff3e0
    style RR4 fill:#fff3e0
    
    style RE1 fill:#ffcdd2
    style RE2 fill:#ffcdd2
    style RE3 fill:#ffcdd2
    style RE4 fill:#ffcdd2
    
    style RA1 fill:#f8bbd9
    style RA2 fill:#f8bbd9
    style RA3 fill:#f8bbd9
    style RA4 fill:#f8bbd9
```

## 组件交互图

```mermaid
graph LR
    subgraph "输入层 Input Layer"
        DATA[历史数据]
        CONFIG[配置文件]
        STRATEGY[策略代码]
    end
    
    subgraph "处理层 Processing Layer"
        PARSER[数据解析器]
        VALIDATOR[数据验证器]
        LOADER[策略加载器]
    end
    
    subgraph "执行层 Execution Layer"
        BACKTESTER[回测引擎]
        MONITOR[性能监控]
        LOGGER[日志记录]
    end
    
    subgraph "输出层 Output Layer"
        RESULT[回测结果]
        REPORT[分析报告]
        CHART[可视化图表]
    end
    
    DATA --> PARSER
    CONFIG --> VALIDATOR
    STRATEGY --> LOADER
    
    PARSER --> BACKTESTER
    VALIDATOR --> BACKTESTER
    LOADER --> BACKTESTER
    
    BACKTESTER --> MONITOR
    BACKTESTER --> LOGGER
    
    MONITOR --> RESULT
    LOGGER --> REPORT
    RESULT --> CHART
    
    style DATA fill:#e8f5e8
    style CONFIG fill:#e8f5e8
    style STRATEGY fill:#e8f5e8
    
    style PARSER fill:#fff3e0
    style VALIDATOR fill:#fff3e0
    style LOADER fill:#fff3e0
    
    style BACKTESTER fill:#e3f2fd
    style MONITOR fill:#e3f2fd
    style LOGGER fill:#e3f2fd
    
    style RESULT fill:#f3e5f5
    style REPORT fill:#f3e5f5
    style CHART fill:#f3e5f5
```
